<style>
    .font-bold { font-weight: bold; }
</style>
<div class="wrapper">
    <% if (type === 'html') { %>
        <%- include('./form-header'); -%>
    <% } %>
    <section class="padding-sm pd-top-25">
        <div class="">
            <% if ((!is_shadow_user || (is_shadow_user && has_health_assessment_answers)) && showProfileHealthAssessment) { %>
            <div class="table-outer">
                <h2><i class="fas fa-stethoscope"></i> Health Assessment</h2>
                <% if(categories_names && categories_names.length){%>
                    <% categories_names.forEach((category) => { %>
                        <h5><%= category %></h5>
                        <table class="table wd-70 pd-sm">
                            <tbody>
                            <% assessment_categories[category].forEach((item) => {%>
                                <tr>
                                    <td>
                                        <%= item && item.question ? item.question : '' %>
                                    </td>
                                    <td class="no-dot capital <%= numberToYesNo((findIdInObj(health_assessment_answers, item.id, 'question_ref')).answer) === 'Yes' ? 'font-bold text-danger' : '' %>" >
                                        <%= numberToYesNo((findIdInObj(health_assessment_answers, item.id, 'question_ref')).answer) %>
                                    </td>
                                </tr>
                            <% })%>

                            </tbody>
                        </table>
                    <% })%>
                <% }%>
            </div>
            <% } %>

            <% if(showProfileMedicalAssessment && medical_assessments_answers && medical_assessments_answers.length){%>
                <div class="table-outer">
                    <h2><svg style="enable-background:new 0 0 415 484;width: 16px;height: 16px;max-height: 18px; vertical-align: middle;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 484 484" xml:space="preserve">
                        <path d="M414.74,95.558c-21.512-21.516-50.12-33.364-80.544-33.364c-30.428,0-59.032,11.848-80.544,33.364l-9.044,9.044    l-9.044-9.044c-21.516-21.516-50.124-33.364-80.548-33.364c-30.428,0-59.032,11.848-80.544,33.364    c-44.416,44.408-44.416,116.672,0,161.084L236.12,418.29c2.344,2.344,5.412,3.516,8.484,3.516c3.072,0,6.14-1.172,8.492-3.512    L414.74,256.646c21.516-21.512,33.364-50.12,33.364-80.544C448.104,145.674,436.256,117.07,414.74,95.558z M397.768,239.674    l-153.16,153.164L91.444,239.674c-35.056-35.056-35.056-92.092,0-127.148c16.98-16.98,39.556-26.332,63.572-26.332    c24.012,0,46.592,9.352,63.576,26.332l17.528,17.528c4.688,4.688,12.28,4.688,16.968,0l17.528-17.528    c16.984-16.98,39.56-26.332,63.576-26.332s46.596,9.352,63.576,26.332C432.824,147.582,432.824,204.618,397.768,239.674z"/>
                        <path d="M56,213.806H12c-6.628,0-12,5.372-12,12s5.372,12,12,12h44c6.628,0,12-5.372,12-12S62.628,213.806,56,213.806z"/>
                        <path d="M431.992,214.194H317.144l-17.872-39.308c-1.348-2.972-4.284-4.78-7.624-4.684c-3.26,0.14-6.108,2.24-7.196,5.316    l-36.528,102.936l-44.408-122.98c-1.096-3.036-3.916-5.12-7.148-5.272c-3.456-0.16-6.236,1.66-7.612,4.584l-29.84,63.408h-98.92    c-4.42,0-8,3.584-8,8s5.264,7.612,9.684,7.612h104c3.1,0,4.236-1.4,5.556-4.204l23.788-50.548l45.452,125.86    c1.144,3.168,4.152,5.28,7.524,5.28c0.008,0,0.016,0,0.016,0c3.38-0.008,6.388-2.14,7.516-5.324l37.376-105.332l11.804,25.964    c1.296,2.86,5.832,4.304,8.968,4.304h120c4.416,0,6.312-3.196,6.312-7.612S436.408,214.194,431.992,214.194z"/>
                        <path d="M472,209.806h-36c-6.628,0-12,5.372-12,12s5.372,12,12,12h36c6.628,0,12-5.372,12-12S478.628,209.806,472,209.806z"/>
                    </svg> Medical Assessment</h2>
                    <table class="table wd-70 pd-sm">
                        <tbody>
                        <% medical_assessments_answers.forEach((item) => {%>
                            <tr>
                                <td>
                                    <%= item && item.question ? item.question : '' %>
                                </td>
                                <td class="no-dot capital <%= numberToYesNo(item.answer) === 'Yes' ? 'font-bold text-danger' : '' %>">
                                    <%= numberToYesNo(item.answer) %>
                                </td>
                            </tr>
                            <% if(item && item.ans_details){%>
                                <tr>
                                    <td class="no-dot" style="font-size: 14px;" colspan="2"><strong>Details:</strong> <%= item.ans_details %></td>
                                </tr>
                            <% }%>
                        <% })%>

                        </tbody>
                    </table>
                </div>
            <% }%>

            <%if(!hide_site_health_block){%>
            <div class="table-outer">
                <h2><i class="fas fa-file-medical"></i> Site Health Assessment</h2>
                <table class="table wd-70 pd-sm">
                    <tbody>
                        <tr>
                            <td><b>Do you have any reportable medical conditions?</b></td>
                            <td class="no-dot capital"><b><%= induction_request.reportable_medical_conditions || '-' %></b></td>
                        </tr>
                        <% if(!hide_medication_block && induction_request.reportable_medical_conditions && induction_request.reportable_medical_conditions.toString().toLowerCase().trim() === 'yes'){%>
                            <tr>
                                <td class="no-dot">Detail:</td>
                                <td class="no-dot"><b><%= induction_request.rmc_detail %></b></td>
                            </tr>
                        <% }%>
                        <tr>
                            <td><b>Are you taking any regular medication, prescribed or otherwise?</b></td>
                            <td class="no-dot capital"><b><%= induction_request.on_long_medication || '-' %></b></td>
                        </tr>
                        <%if(has_medications && !hide_medication_block){%>
                            <% induction_request.medications.forEach((medication) => { %>
                                <tr>
                                    <td class="no-dot" colspan="2">
                                        <table class="table table-wd-auto wd-33 pd-sm table-bordered tp-0 mb-10">
                                            <tbody>
                                            <tr>
                                                <td class="no-dot">
                                                    Name: <b class="text-danger"><%= medication.medication_name %></b>
                                                </td>
                                                <td class="no-dot">
                                                    Dosage: <b class="text-danger"><%= medication.medication_dosage %></b>
                                                </td>
                                                <td class="no-dot">
                                                    Frequency: <b class="text-danger"><%= medication.medication_frequency %></b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="no-dot">
                                                    Commenced date: <b class="text-danger"><%= correctDateFormat(medication.medication_date_commenced) %></b>
                                                </td>
                                                <td class="no-dot" colspan="2">
                                                    Completion date:
                                                    <% if(medication.infinite_completion) { %>
                                                        <i class="bold text-danger">Indefinite</i>
                                                    <% }else { %>
                                                        <b class="text-danger"><%= correctDateFormat(medication.medication_date_of_completion) %></b>
                                                    <% }%>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            <%})%>
                        <%}%>
                        <%if(!hide_medication_block && show_side_effects_block){%>
                        <tr>
                            <td><b>Do you suffer from any side effects?</b></td>
                            <% if(induction_request.any_side_effects && induction_request.any_side_effects.toString().toLowerCase().trim() === 'yes'){%>
                                <td class="no-dot capital text-danger"> <b><%= induction_request.any_side_effects %></b></td>
                            <% }else{ %>
                                <td class="no-dot capital"> <b><%= induction_request.any_side_effects || '-' %></b></td>
                            <% } %>
                        </tr>
                        <% if(show_side_effects_block && induction_request.any_side_effects && induction_request.any_side_effects.toString().toLowerCase().trim() === 'yes'){%>
                            <tr>
                                <td class="no-dot">Details: </td>
                                <td class="no-dot"> <b class="text-danger"><%= induction_request.any_side_effect_detail %></b></td>
                            </tr>
                        <% } %>
                        <%}%>
                    </tbody>
                </table>
            </div>
            <%}%>
        </div>
    </section>
    
    <% if (type === 'html') { %>
        <%- include('./form-footer', {totalPages: 4, currentPageNo: 2}); -%>
    <% } %>
</div>
