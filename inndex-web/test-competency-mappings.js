/**
 * Simple test script to verify that getCompetencyMappings works with country code filtering
 * 
 * Usage: node test-competency-mappings.js
 * 
 * This script tests:
 * 1. getCompetencyMappings() without country code (should return all competencies)
 * 2. getCompetencyMappings('GB') with GB country code (should return only GB competencies)
 * 3. getCompetencyMappings('US') with US country code (should return only US competencies)
 */

const path = require('path');

// <PERSON><PERSON> sails global for testing
global.sails = {
    models: {
        competencies_reader: {
            find: async (query) => {
                // Mock data for testing
                const mockCompetencies = [
                    {
                        name: 'First Aid',
                        tags: ['first-aid'],
                        country_code: 'GB'
                    },
                    {
                        name: 'Fire Marshal',
                        tags: ['fire-marshal'],
                        country_code: 'GB'
                    },
                    {
                        name: 'SMSTS',
                        tags: ['supervisor'],
                        country_code: 'GB'
                    },
                    {
                        name: 'CPR Certification',
                        tags: ['first-aid'],
                        country_code: 'US'
                    },
                    {
                        name: 'Fire Safety Officer',
                        tags: ['fire-marshal'],
                        country_code: 'US'
                    },
                    {
                        name: 'Mental Health First Aid',
                        tags: ['mental-health-first-aider'],
                        country_code: 'GB'
                    }
                ];

                // Filter based on query
                let filtered = mockCompetencies.filter(comp => {
                    // Check tags filter
                    if (query.where.tags && query.where.tags['!='] === null) {
                        if (!comp.tags || comp.tags.length === 0) return false;
                    }
                    
                    // Check name filter
                    if (query.where.name && query.where.name['!='] === null) {
                        if (!comp.name) return false;
                    }
                    
                    // Check country_code filter
                    if (query.where.country_code) {
                        if (comp.country_code !== query.where.country_code) return false;
                    }
                    
                    return true;
                });

                console.log(`Mock DB query:`, JSON.stringify(query, null, 2));
                console.log(`Returning ${filtered.length} competencies`);
                
                return filtered;
            }
        }
    },
    log: {
        info: (...args) => console.log('[INFO]', ...args)
    }
};

// Import the function we want to test
const { getCompetencyMappings } = require('./api/services/DataProcessingService');

async function runTests() {
    console.log('='.repeat(60));
    console.log('Testing getCompetencyMappings function');
    console.log('='.repeat(60));

    try {
        // Test 1: No country code (should return all competencies)
        console.log('\n1. Testing getCompetencyMappings() without country code:');
        console.log('-'.repeat(50));
        const allMappings = await getCompetencyMappings();
        console.log('Result:', JSON.stringify(allMappings, null, 2));

        // Test 2: GB country code
        console.log('\n2. Testing getCompetencyMappings("GB"):');
        console.log('-'.repeat(50));
        const gbMappings = await getCompetencyMappings('GB');
        console.log('Result:', JSON.stringify(gbMappings, null, 2));

        // Test 3: US country code
        console.log('\n3. Testing getCompetencyMappings("US"):');
        console.log('-'.repeat(50));
        const usMappings = await getCompetencyMappings('US');
        console.log('Result:', JSON.stringify(usMappings, null, 2));

        // Test 4: Non-existent country code
        console.log('\n4. Testing getCompetencyMappings("FR"):');
        console.log('-'.repeat(50));
        const frMappings = await getCompetencyMappings('FR');
        console.log('Result:', JSON.stringify(frMappings, null, 2));

        console.log('\n' + '='.repeat(60));
        console.log('All tests completed successfully!');
        console.log('='.repeat(60));

        // Verify expected behavior
        console.log('\nVerification:');
        console.log(`- All mappings has ${Object.keys(allMappings).length} categories`);
        console.log(`- GB mappings has ${Object.keys(gbMappings).length} categories`);
        console.log(`- US mappings has ${Object.keys(usMappings).length} categories`);
        console.log(`- FR mappings has ${Object.keys(frMappings).length} categories`);

    } catch (error) {
        console.error('Test failed with error:', error);
        process.exit(1);
    }
}

// Run the tests
runTests();
