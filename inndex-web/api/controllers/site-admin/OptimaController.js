/*
*  OptimaController.js
* Controller to server API routes allowed to `SITE_ADMIN` role only.
*
* */

const dayjs = require('dayjs');
const {
    APP_ENV,
} = (sails.config.custom);
const {
    displayDateFormat_D_MMM_Y,
    dbDateFormat_YYYY_MM_DD,
    CURRENCY_SYMBOL,
} = sails.config.constants;
const {
    ExcelService: {
        streamExcelDownload,
        exportTimesheets,
    },
    HttpService: {typeOf},
    DataProcessingService: {
        secondsToHuman,
    },
    ResponseService: {successResponse, errorResponse},
    OptimaSyncService: {
        callOptimaBoxGateway,
        createBadge,
        deleteBadge,
    },
} = require('./../../services');
const TIME_INPUT_STATE = {
    DAY_HOURS: 1,
    NIGHT_HOURS: 2,
    SPLIT_SHIFT: 3,
    HOLIDAY: 4,
    SICK: 5,
};
const {
    TimesheetValidator: {
        saveTsCommentValidator,
        saveTimesheetInfoValidator,
        saveTsWorkerRecordValidator
    },
} = require('./../../validators');


const isTestCall = (projectRef) => {
    return (APP_ENV !== 'production') && [24, 22, 3].includes(projectRef);
};
const test_data_file_name = 'turnstile.test.json';
const _getTestData = () => {
    const fs = require('fs');
    let file_path = require('path').join(process.cwd(), test_data_file_name);
    sails.log.info('Loading test data from path:', file_path);
    return JSON.parse(fs.readFileSync(file_path, 'utf-8'));
};
const _updateTestData = (content) => {
    const fs = require('fs');
    let file_path = require('path').join(process.cwd(), test_data_file_name);
    sails.log.info('writing test data from path:', file_path);
    fs.writeFileSync(file_path, JSON.stringify(content));
    return content;
};

dateToBeFormated = (date) =>{
    return dayjs.utc(date).local().format(dbDateFormat_YYYY_MM_DD);
}

const computeWeekEndDate = (inputDate, endWeekDay) => {
    const jsTargetDay = endWeekDay === 7 ? 0 : endWeekDay;
    let currentDate = dayjs.utc(inputDate, dbDateFormat_YYYY_MM_DD);

    // offset from week start
    const daysUntilWeekend = (jsTargetDay - currentDate.day() + 7) % 7;
    // console.log('daysUntilWeekend', daysUntilWeekend);

    // subtract remaining days of week, to get starting point
    let startDate = currentDate.subtract((7 - daysUntilWeekend), 'day').add(1, 'day');
    // console.log('startOfWeek', startDate);

    let endDate = startDate.clone().add(6, 'day');
    endDate = dayjs.utc(endDate).format(dbDateFormat_YYYY_MM_DD);
    sails.log.info(`Generated startDate ${startDate} and endDate ${endDate} through current date ${currentDate}`);
    return endDate;
};


const createWeeklyTimesheet = async(req, timesheets) => {
    sails.log.info('Initiating createWeeklyTimesheet to creating new weeks');
    let grouped = {};
    let projectId = +req.param('projectId', 0);
    const toDate = req.param('to_date', '--');

    // Fetch all available weekly timesheets for the given user and project in one query
    const userProjectRefs = timesheets.map(ts => ({
        user_ref: ts.user_ref,
        project_ref: ts.project_ref
    }));

    const availableWeeks = await sails.models.projectweeklytimesheet.find({
        where: {
            or: userProjectRefs.map(ref => ({
                user_ref: ref.user_ref,
                project_ref: ref.project_ref,
                week_end_date: toDate
            }))
        }
    });


    const availableWeeksMap = {};
    availableWeeks.forEach(week => {
        const key = `${week.user_ref}_${week.project_ref}_${toDate}`;
        availableWeeksMap[key] = week.id;
    });

    for(let i= 0; i < timesheets.length; i++) {
        const ts = timesheets[i];
        if (!ts || !ts.day_of_yr || !ts.user_ref || !ts.project_ref) {
            sails.log.info('Skipping invalid timesheet:', ts);
            continue;
        }

        const weekKey = `${ts.user_ref}_${ts.project_ref}_${toDate}`;

        // Check if the weekly timesheet already exists
        if (availableWeeksMap[weekKey]) {
            sails.log.info('Using existing weekly timesheet ID:', availableWeeksMap[weekKey]);
            ts.weekly_timesheet_ref = availableWeeksMap[weekKey];
            continue;
        }

        // Group timesheets by week
        if (!grouped[weekKey]) {
            grouped[weekKey] = {
                user_ref: ts.user_ref,
                project_ref: ts.project_ref,
                week_end_date: new Date(toDate),
                status: ts.status || 1,
                modified_by: ts.modified_by || null,
                comments: ts.comments || []
            };
        }
        sails.log.info('Grouping timesheet:', ts);
    }

    sails.log.info('Got available week timesheet', timesheets);

    const weeklyTimesheets = Object.values(grouped);
    const projectInfo = await sails.models.project_reader.find({
        select: ['id', 'custom_field'],
        where: {id: projectId},
    });

    const timesheet_week_end = projectInfo[0].custom_field.timesheet_week_end;
    sails.log.info('Preparing to insert weekly timesheets:', weeklyTimesheets);

    const weeklyCreated = await sails.models.projectweeklytimesheet.createEach(weeklyTimesheets);
    sails.log.info('Weekly timesheets created:', weeklyCreated);

    weeklyCreated.forEach((ele) =>{
        timesheets.map((ts) => {
            const end_date = computeWeekEndDate(ts.day_of_yr,timesheet_week_end);
            if(end_date === dateToBeFormated(ele.week_end_date) && ts.user_ref === ele.user_ref) {
                sails.log.info('Updating timesheet with weekly timesheet ref:', ele.id);
                ts.weekly_timesheet_ref = ele.id
            }
        });
    })

    sails.log.info('Updated timesheets with weekly references:', timesheets);
    return {timesheets, weeklyCreated};
};

module.exports = {

    getTurnstileStatus: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let optima_setting = req.project_optima_setting;

        sails.log.info(`get project:${projectId} turnstile status`);
        let api_response = await callOptimaBoxGateway(optima_setting, {
            endpoint: `turnstileStatus`,
            method: 'GET',
        }, {});

        if(api_response.error && isTestCall(projectId)){
            sails.log.info('Serving test content for fetch status');
            let statusResponse = _getTestData();
            return successResponse(res, statusResponse);
        }

        return api_response.success ? successResponse(res, api_response.data) : errorResponse(res, '', ({
            ...(api_response.data || {}),
            statusCode: api_response.status
        }));
    },

    changeTurnstileState: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let turnstileId = +req.param('turnstileId', 0);
        /*
        State 0 = Auto mode (Have to use face to unlock the turnstile)
        State 1 = Opening Maintained (Free Spin)
        State 2 = Closure Maintained (Locked)
        State 3 = Pulse Opening (one rotation allowed)
        */
        let changeStateTo = +req.param('state', 0);

        let optima_setting = req.project_optima_setting;

        sails.log.info(`change project:${projectId} turnstile:${turnstileId} state to: ${changeStateTo}`);
        let api_response = await callOptimaBoxGateway(optima_setting, {
            endpoint: `turnstile`,
            method: 'POST',
        }, {
            resourceId: turnstileId,
            typeElement: 2,         // Additional parameter for pulse opening request only. but working for others too..
            action: changeStateTo
        });

        if(api_response.error && isTestCall(projectId)){
            sails.log.info('Serving test content for state change');
            let statusResponse = _getTestData();
            statusResponse.result = (statusResponse.result || []).map(t => {
                if(t.id === turnstileId){
                    t.state = changeStateTo;
                }
                return t;
            });
            _updateTestData(statusResponse);
            return successResponse(res, {_is_fake: true, action: "success", readerID: turnstileId, setStatus: changeStateTo});
        }

        return api_response.success ? successResponse(res, api_response.data) : errorResponse(res, '', ({
            ...(api_response.data || {}),
            statusCode: api_response.status
        }));
    },

    getRecentUnrecognizedEvents: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let type = req.param('type', 'unrecognized');
        let pageNumber = +req.param('pageNumber', 0) || 0;
        let limit = +req.param('pageSize', 10) || 10;
        let after = dayjs().subtract(10, 'minute').unix();

        let extra_filter = {};
        if(type === 'unrecognized'){
            extra_filter = {
                nature_id: 5, // Unrecognised User
                user_ref: null
            }
        }

        sails.log.info(`get "${type}" events of project:${projectId} after: ${after} limit: ${limit} page: ${pageNumber}`);
        let badge_events = await sails.models.badgeevent_reader.find({
            select: ['event_date_time', 'badge_number', 'entity', 'unit_name', 'reader_name', 'event_type', 'user_ref'],
            where: {
                project_ref: projectId,
                ...extra_filter,
                event_date_time: {
                    '>=': after
                }
            },
            skip: (pageNumber * limit),
            limit,
            sort: ['event_date_time DESC']
        });

        sails.log.info(`got total ${badge_events.length} events of project:${projectId} after: ${after}`);
        return successResponse(res, {
            events: badge_events,
            limit,
            pageNumber,
            after
        });
    },

    switchInductionBadgeWith: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let badgeNumber = +req.param('badgeNumber', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let optima_setting = req.project_optima_setting;
        let payload = _.pick((req.body || {}), [
            'groupId',
            'statusId',
            'timeSlotsId',
        ]);

        let induction = await sails.models.inductionrequest_reader.findOne({
            where: {project_ref : projectId, id: inductionRequestId},
            select: ['optima_badge_number', 'additional_data', 'comments']
        });

        if(!induction){
            sails.log.info(`induction not found, project: ${projectId}, id: ${inductionRequestId}`);
            return errorResponse(res, 'Induction record not found', {
                projectId,
                inductionRequestId,
            });
        }
        sails.log.info(`Switch badge for project: ${projectId}, id: ${inductionRequestId}, from: ${induction.optima_badge_number} to: ${badgeNumber}`);

        let {first_name, last_name} = (induction.additional_data && induction.additional_data.user_info) || {};
        let out = await createBadge(optima_setting, {
            ...payload,
            lastName: last_name,
            firstName: first_name,
            badgeNumber,
        });
        if(!out.badge){
            let message = (out.data && typeOf(out.data, 'string')) ? out.data : '';
            return errorResponse(res, `Failed while creating Badge # ${badgeNumber}. ${message}`, {
                projectId,
                inductionRequestId,
                badgeNumber,
                ...out,
            });
        }

        let old_deleted;
        if(induction.optima_badge_number){
            let {success: deleted} = await deleteBadge(optima_setting, induction.optima_badge_number);
            old_deleted = deleted || false;
        }

        let updated = await sails.models.inductionrequest.updateOne({id: induction.id}).set({
            optima_badge_number: out.badge
        });

        //@todo: spatel: Email notification about induction badge change?
        //@todo: spatel: Add Comment about who change badge, from/to
        let after = dayjs().subtract(20, 'minute').unix();
        sails.log.info(`Updating existing events of project: ${projectId}, badge: ${out.badge}, after: ${after} with user: ${updated.user_ref}`);
        await sails.models.badgeevent.update({
            project_ref: projectId,
            badge_number: out.badge,
            user_ref: null,
            event_date_time: {
                '>=': after
            },
        }).set({user_ref: updated.user_ref,});

        sails.log.info(`Switched badge for project: ${projectId}, id: ${inductionRequestId} with: ${out.badge}`);
        return successResponse(res, {
            projectId,
            inductionRequestId,
            newBadgeNumber: out.badge,
            oldBadgeNumber: induction.optima_badge_number,
            old_deleted,
            message: `User has been associated with new Access Card.${(old_deleted === false) ? `\nFailed to delete old badge # ${induction.optima_badge_number}` : ''}`
        });
    },

    // save single/bulk timesheets record
    saveTimesheetInfo: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let from_date = dayjs(req.param('from_date', '--'), dbDateFormat_YYYY_MM_DD);
        let to_date = dayjs(req.param('to_date', '--'), dbDateFormat_YYYY_MM_DD);

        if (
            !projectId ||
            !from_date.isValid() ||
            !to_date.isValid()
        ) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'Invalid date filter provided', {from_date, to_date});
        }
        let {validationError, payload} = saveTimesheetInfoValidator(req);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let from_date_str = from_date.format(dbDateFormat_YYYY_MM_DD);
        let to_date_str = to_date.add(1, 'days').format(dbDateFormat_YYYY_MM_DD);
        let {timesheets} = payload;
        let userIds = [...new Set(timesheets.map(t => t.user_ref))];

        sails.log.info(`Total ${timesheets.length} timesheets to save for project: ${projectId}, from, to: ${from_date_str}  <= date < ${to_date_str}, users: ${userIds}`);
        let existing = await sails.models.projecttimesheet.find({
            select: ['id', 'day_of_yr', 'user_ref'],
            where: {
                project_ref: projectId,
                user_ref: userIds,
                day_of_yr: {
                    '>=': from_date_str,
                    '<': to_date_str
                },
            },
        });
        sails.log.info(`Total existing timesheets are: ${existing.length}`);
        // add new records
        // update existing ones
        let {newOnes, existingOnes} = timesheets.reduce((result, t) => {
            let index = existing.findIndex(row => row.id === t.id || ((t.user_ref === row.user_ref) && (t.day_of_yr === row.day_of_yr)));
            t.modified_by = req.user.id;
            if (index > -1) {
                t.id = existing[index].id;
                result.existingOnes.push(t);
            } else {
                t.project_ref = projectId;
                result.newOnes.push(t);
            }
            return result;
        }, {
            newOnes: [],
            existingOnes: [],
        });

        sails.log.info(`Total timesheets to create: ${newOnes.length}, update: ${existingOnes.length}`);
        let result = [];
        let weekly_timesheets = [];
        if(newOnes.length) {
            const {timesheets, weeklyCreated}= await createWeeklyTimesheet(req, newOnes);
            newOnes = timesheets;
            let rows = await sails.models.projecttimesheet.createEach(newOnes);

            const groupedTimesheets = timesheets.reduce((acc, ts) => {
            if (!acc[ts.weekly_timesheet_ref]) {
                acc[ts.weekly_timesheet_ref] = [];

            }
            acc[ts.weekly_timesheet_ref].push(ts);
                return acc;
            }, {});

            weekly_timesheets = weeklyCreated.map(week => ({
                ...week,
                timesheets: groupedTimesheets[week.id] || []
            }));

            sails.log.info(rows, 'rows updated');
            result.push(...rows);
        }
        if(existingOnes.length){
            sails.log.info(`Timesheets to update: ${existingOnes.map(t => t.id)}`);
            let week_status = 1;
            for(let i = 0, len = existingOnes.length; i < len; i++) {
                sails.log.info('Updating project timesheet id:', existingOnes[i].id);
                let {
                    actual_seconds,
                    day_seconds,
                    night_seconds,
                    hours_state,
                    travel_seconds,
                    overtime_seconds,
                    training_seconds,
                    manager_auth_seconds,
                    price_work_amount,
                    modified_by,
                    status,
                    weekly_timesheet_ref
                } = existingOnes[i];
                week_status = status;
                let row = await sails.models.projecttimesheet.updateOne({
                    id: existingOnes[i].id,
                }).set({
                    actual_seconds,
                    day_seconds,
                    night_seconds,
                    hours_state,
                    travel_seconds,
                    overtime_seconds,
                    training_seconds,
                    manager_auth_seconds,
                    price_work_amount,
                    modified_by,
                    status,
                    weekly_timesheet_ref
                });
                result.push(row);
            }
            const toDate = req.param('to_date', '--');
            const updateWeek = await sails.models.projectweeklytimesheet.update({
                where:{
                    week_end_date: toDate,
                    project_ref: projectId,
                    user_ref: userIds,
                }
            }).set({status: week_status});
            sails.log.info(`Got existing weeks:- ${updateWeek.length}`);
            if(updateWeek.length){
                weekly_timesheets = updateWeek.map(week => ({
                    ...week,
                    timesheets: result.filter(ts => ts.weekly_timesheet_ref === week.id)
                }));
            }
        }
        return successResponse(res,{
            existing,
            timesheets,
            result,
            weekly_timesheets
        });
    },

    saveTimesheetComment: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let {validationError, payload} = saveTsCommentValidator(req);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }
        sails.log.info(`Save timesheet comment`, payload);
        let row = await sails.models.projecttimesheet.findOne({
            select: ['id', 'comments'],
            where: {
                project_ref: projectId,
                user_ref: payload.user_ref,
                day_of_yr: payload.day_of_yr,
            },
        });
        sails.log.info(`existing timesheet id to add comment?`, (row && row.id));
        if(row && row.id){
            let comments = [
                ...(row.comments || []),
                payload.comment
            ];
            let record = await sails.models.projecttimesheet.updateOne({ id: row.id,}).set({
                comments,
                modified_by: req.user.id,
            });
            return successResponse(res,{record});
        }

        let record = await sails.models.projecttimesheet.create({
            project_ref: projectId,
            user_ref: payload.user_ref,
            day_of_yr: payload.day_of_yr,
            comments: [
                payload.comment,
            ],
            modified_by: req.user.id,
        });
        return successResponse(res,{record});
    },

    saveWeeklyTimesheetComments: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let {validationError, payload} = saveTsCommentValidator(req);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }
        sails.log.info(`Save week timesheet comment`, payload);
        let row = await sails.models.projectweeklytimesheet.findOne({
            select: ['id', 'comments'],
            where: {
                project_ref: projectId,
                user_ref: payload.user_ref,
                week_end_date: payload.week_end_date,
            },
        });
        sails.log.info(`existing timesheet id to add comment?`, (row && row.id));
        if(row && row.id){
            let comments = [
                ...(row.comments || []),
                payload.comment
            ];
            let record = await sails.models.projectweeklytimesheet.updateOne({ id: row.id,}).set({
                comments,
                modified_by: req.user.id,
            });
            return successResponse(res,{record});
        }

        sails.log.info(`Creating new week along with comment as we don't have any for the user: ${payload.user_ref}, with week_end_date: ${payload.week_end_date}`);
        let record = await sails.models.projectweeklytimesheet.create({
            project_ref: projectId,
            user_ref: payload.user_ref,
            week_end_date: payload.week_end_date,
            comments: [
                payload.comment,
            ],
            modified_by: req.user.id,
        });
        return successResponse(res,{record});
    },

    saveWorkerRecords: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let {validationError, payload} = saveTsWorkerRecordValidator(req);
        if(validationError) {
            return errorResponse(res, 'Invalid Request.', {validationError});
        }
        let {week_end_date, user_ref, employee_number, hourly_rate} = payload;
        sails.log.info(`Save worker records`, payload);
        let row = await sails.models.projectweeklytimesheet_reader.find({
            where: {
                project_ref: projectId,
                user_ref: user_ref,
            },
        });
        sails.log.info(`existing weeks: ${row.length} to update worker records?`);
        let currentWeek = (row || []).find(ele => dateToBeFormated(ele.week_end_date) === week_end_date) || null;
        let record = [];
        if(row && row.length){
            let weekIds = row.map(ele => ele.id);
            record = await sails.models.projectweeklytimesheet.update({ id: weekIds}).set({
                employee_number: employee_number,
                hourly_rate: hourly_rate,
            });
        }
        if(!currentWeek){
            sails.log.info(`Creating new week, user: ${user_ref}, projectId: ${projectId}, week_end_date: ${week_end_date}`);
            let newOne = await sails.models.projectweeklytimesheet.create({
                project_ref: projectId,
                user_ref: user_ref,
                week_end_date: week_end_date,
                comments: [],
                modified_by: req.user.id,
                employee_number: employee_number,
                hourly_rate: hourly_rate,
            });
            record.push(newOne);
        }
        return successResponse(res,{record});
    },

    // toggle timesheet status
    approveUserTimesheets: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let weekly_timesheet_ref = +req.param('weekly_timesheet_ref', 0);
        let status = +req.param('status', 2);
        sails.log.info(`update timesheet status: ${status} project:${projectId}, weekly_timesheet_ref: ${weekly_timesheet_ref}`);
        let timesheets = await sails.models.projecttimesheet.update({
            where: {
                project_ref: projectId,
                weekly_timesheet_ref: weekly_timesheet_ref
            },
        }).set({status: status});
        let weekly_timesheet = await sails.models.projectweeklytimesheet.update({
            where: {
                id: weekly_timesheet_ref
            },
        }).set({status: status});
        sails.log.info(`updated timesheets project:${projectId}, count:${timesheets.length}`);

        return successResponse(res, {
            count: timesheets.length,
            timesheets,
            weekly_timesheet
        });
    },

    downloadTimesheets: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let userIds = (req.body.userIds || []);
        userIds = [...new Set(userIds.filter(id => !isNaN(+id)))];
        let from_date = dayjs(req.param('from_date', '--'), dbDateFormat_YYYY_MM_DD);
        let to_date = dayjs(req.param('to_date', '--'), dbDateFormat_YYYY_MM_DD);

        if (
            !projectId ||
            !from_date.isValid() ||
            !to_date.isValid()
        ) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'Invalid date filter provided', {from_date, to_date});
        }

        let from_date_str = from_date.format(dbDateFormat_YYYY_MM_DD);
        let to_date_str = to_date.format(dbDateFormat_YYYY_MM_DD);
        sails.log.info(`download timesheets project:${projectId}, from:${from_date_str} to:${to_date_str}, users:${userIds}`);
        // select project
        // select users' induction
        // select users' timesheets
        let project = await sails.models.project_reader.findOne({
            select: ['id', 'name', 'postcode', 'custom_field'],
            where: {id: projectId},
        });
        if (!project) {
            return errorResponse(res, 'Project info not found', {});
        }

        let ts_filter = {
            project_ref: projectId,
            day_of_yr: {
                '>=': from_date_str,
                '<=': to_date_str
            },
        };
        let weekly_ts_filter = {
            project_ref: projectId,
            week_end_date: req.param('to_date', '--')
        }

        if(userIds.length){
            ts_filter.user_ref = userIds;
            weekly_ts_filter.user_ref = userIds;
        }
        let timesheets = await sails.models.projecttimesheet_reader.find({
            where: ts_filter
        });

        if (!timesheets.length) {
            return errorResponse(res, 'No timesheet records found for provided date range', {});
        }
        sails.log.info(`total timesheets project:${projectId}, from:${from_date_str} to:${to_date_str}, count:${timesheets.length}`);

        const timesheetWeeks = await  sails.models.projectweeklytimesheet_reader.find({
            where: weekly_ts_filter
        });

        if (!timesheetWeeks.length) {
            return errorResponse(res, 'No timesheet weeks records found for provided date range', {});
        }

        const groupedTimesheets = timesheets.reduce((acc, ts) => {
            if (!acc[ts.weekly_timesheet_ref]) {
                acc[ts.weekly_timesheet_ref] = [];

            }
            acc[ts.weekly_timesheet_ref].push(ts);
            return acc;
        }, {});

        const weekly_timesheets = timesheetWeeks.map(week => ({
            ...week,
            timesheets: groupedTimesheets[week.id] || []
        }));
        sails.log.info('Got weekly timesheets:', weekly_timesheets.length);


        let inductions = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'creator_name', 'district', 'additional_data', 'user_ref'],
            where: {
                project_ref: projectId,
                user_ref: [...new Set(weekly_timesheets.map(t => t.user_ref))],
            }
        });
        sails.log.info(`total inductions project:${projectId}, from:${from_date_str} to:${to_date_str}, count:${inductions.length}`);
        let induction_rows = inductions.reduce((groups, ir) => {
            let {additional_data: {user_info, employment_detail}} = ir;
            let {first_name, last_name, email, nin} = user_info || {};
            let {employer, job_role, type_of_employment, employment_company} = employment_detail || {};
            groups[ir.user_ref] = {
                project_id: projectId,
                project_name: project.name,
                project_postcode: project.postcode,
                record_id: +ir.record_id,
                user_ref: ir.user_ref,
                district: ir.district,
                name: ir.creator_name,
                first_name, last_name, email, nin,
                employer, job_role, type_of_employment, employment_company
            };
            return groups;
        }, {});
        let {currency_code, timesheet_week_end} = project.custom_field || {};
        if(!timesheet_week_end){
            timesheet_week_end = 7;
        }
        let groups = timesheets.reduce((list, ts) => {
            const [weekly_timesheet = {}] = timesheetWeeks || [];
            const week_ends_on = dateToBeFormated(weekly_timesheet.week_end_date);
            let key = JSON.stringify({user_ref: ts.user_ref, week_end_on: week_ends_on});
            if(!list[key]){
                list[key] = {
                    ...induction_rows[ts.user_ref],
                    week_end_on: week_ends_on,
                    ts: [],
                    Mon_day: '-',
                    Mon_night: '-',
                    Mon_overtime: '-',
                    Tue_day: '-',
                    Tue_night: '-',
                    Tue_overtime: '-',
                    Wed_day: '-',
                    Wed_night: '-',
                    Wed_overtime: '-',
                    Thu_day: '-',
                    Thu_night: '-',
                    Thu_overtime: '-',
                    Fri_day: '-',
                    Fri_night: '-',
                    Fri_overtime: '-',
                    Sat_day: '-',
                    Sat_night: '-',
                    Sat_overtime: '-',
                    Sun_day: '-',
                    Sun_night: '-',
                    Sun_overtime: '-',

                    total_day_hr: 0,
                    total_night_hr: 0,
                    total_basic_hr: 0,
                    total_driving_hr: 0,
                    total_overtime_hr: 0,
                    total_training_hr: 0,
                    total_manager_a_hr: 0,
                    total_combined_hr: 0,
                    total_price_work: 0,

                    total_day_hr_label: 0,
                    total_night_hr_label: 0,
                    total_basic_hr_label: 0,
                    total_driving_hr_label: 0,
                    total_overtime_hr_label: 0,
                    total_training_hr_label: 0,
                    total_manager_a_hr_label: 0,
                    total_combined_hr_label: 0,
                    total_price_work_label: 0,
                };
            }
            let ts_day = dayjs(ts.day_of_yr, dbDateFormat_YYYY_MM_DD);
            // list[key].ts.push(ts);
            let value_day = '-';
            let value_night = '-';
            let value_overtime = '-';
            if([TIME_INPUT_STATE.DAY_HOURS,TIME_INPUT_STATE.NIGHT_HOURS,TIME_INPUT_STATE.SPLIT_SHIFT].includes(ts.hours_state)){

                if(ts.hours_state === TIME_INPUT_STATE.SPLIT_SHIFT){
                    value_day = secondsToHuman(ts.day_seconds);
                    value_night = secondsToHuman(ts.night_seconds);

                    list[key].total_day_hr += +(ts.day_seconds || 0);
                    list[key].total_basic_hr += +(ts.day_seconds || 0);

                    list[key].total_night_hr += +(ts.night_seconds || 0);
                    list[key].total_basic_hr += +(ts.night_seconds || 0);
                }else if(ts.hours_state === TIME_INPUT_STATE.NIGHT_HOURS){
                    value_night = secondsToHuman(ts.night_seconds);

                    list[key].total_night_hr += +(ts.night_seconds || 0);
                    list[key].total_basic_hr += +(ts.night_seconds || 0);
                }else if(ts.hours_state === TIME_INPUT_STATE.DAY_HOURS){
                    value_day = secondsToHuman(ts.day_seconds);

                    list[key].total_day_hr += +(ts.day_seconds || 0);
                    list[key].total_basic_hr += +(ts.day_seconds || 0);
                }
                value_overtime = secondsToHuman(ts.overtime_seconds);
                list[key].total_driving_hr += +(ts.travel_seconds || 0);
                list[key].total_overtime_hr += +(ts.overtime_seconds || 0);
                list[key].total_training_hr += +(ts.training_seconds || 0);
                list[key].total_manager_a_hr += +(ts.manager_auth_seconds || 0);
                list[key].total_combined_hr = (list[key].total_basic_hr + list[key].total_driving_hr + list[key].total_overtime_hr + list[key].total_training_hr + list[key].total_manager_a_hr);
                list[key].total_price_work += +(ts.price_work_amount || 0);

                list[key].total_day_hr_label = secondsToHuman(list[key].total_day_hr);
                list[key].total_night_hr_label = secondsToHuman(list[key].total_night_hr);
                list[key].total_basic_hr_label = secondsToHuman(list[key].total_basic_hr);
                list[key].total_driving_hr_label = secondsToHuman(list[key].total_driving_hr);
                list[key].total_overtime_hr_label = secondsToHuman(list[key].total_overtime_hr);
                list[key].total_training_hr_label = secondsToHuman(list[key].total_training_hr);
                list[key].total_manager_a_hr_label = secondsToHuman(list[key].total_manager_a_hr);
                list[key].total_combined_hr_label = secondsToHuman(list[key].total_combined_hr);
                list[key].total_price_work_label = (list[key].total_price_work.toFixed(2));
            }else if(ts.hours_state === TIME_INPUT_STATE.HOLIDAY){
                value_day = 'Holiday';
                value_night = 'Holiday';
                value_overtime = 'Holiday';
            }else if(ts.hours_state === TIME_INPUT_STATE.SICK){
                value_day = 'Sick';
                value_night = 'Sick';
                value_overtime = 'Sick';
            }
            list[key][`${ts_day.format('ddd')}_day`] = value_day;
            list[key][`${ts_day.format('ddd')}_night`] = value_night;
            list[key][`${ts_day.format('ddd')}_overtime`] = value_overtime;
            return list;
        }, {});

        let records = Object.values(groups).sort((a, b) => ((a.name || '').toLowerCase()).toString().localeCompare(((b.name || '').toLowerCase()).toString()));

        sails.log.info(`total records to export project:${projectId}, rows: ${records.length}`);
        // sails.log.info(`total records to export`, JSON.stringify(records, null, 2));
        let workbook = await exportTimesheets(records, timesheet_week_end, CURRENCY_SYMBOL[currency_code]);
        return streamExcelDownload(res, workbook, `${project.id}-${project.name}-Timesheet-week-ending-${to_date.format('DD-MM-YYYY')}.xlsx`);
    },
};
