const SITE_ADMIN_PREFIX = `site-admin/`;
const InductionController = `InductionController`;
const ProjectController = `ProjectController`;
const RollCallController = `RollCallController`;
const ProjectIncidentReportController = `ProjectIncidentReportController`;
const ClerkOfWorksController = `ClerkOfWorksController`;
const OptimaController = `${SITE_ADMIN_PREFIX}OptimaController`;
const PermitController_SA = `PermitController`;

// Declaring it as fn, so that it doesn't affect rest of sails.config loading.

module.exports = {

    getSiteAdminPolicies: () => {

        /*
         * Refer to config/policies.js file for attached policy details of the following functions.
         *
         * projectInductionsListSA
         * getInductionEmployersSA
         * getActiveUsersTimesheetList
         * getUserTimesheetDetails
         * getProjectInductionById
         * getProjectIncidentListSA
         * getProjectClerkOfWorksListSA
         *
         */

        return {
            [OptimaController]: {
                getRecentUnrecognizedEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                switchInductionBadgeWith: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
                getTurnstileStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
                changeTurnstileState: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
                approveUserTimesheets: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                saveTimesheetInfo: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                saveTimesheetComment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                saveWorkerRecords: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                saveWeeklyTimesheetComments: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                downloadTimesheets: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
            },
        };
    },

    getSiteAdminRoutes: () => {

        return {
            'get /api/sa/project/:projectId/inductions/list': { controller: 'induction', action: 'projectinductionslistsa', policy_checks: ['isMoreThanDeliveryManager'], tools: ['induction', 'm_e_and_a_control'], multiple: true, data_type_key: '' },
            'get /api/sa/project/:projectId/inductions/employers': { controller: 'induction', action: 'getInductionEmployersSA', policy_checks: ['isMoreThanDeliveryManager'] },
            'get /api/sa/project/:projectId/inductions/job-roles': `${InductionController}.getInductionJobRolesSA`,
            'get /api/sa/project/:projectId/induction/:inductionRequestId/detail': { controller: 'induction', action: 'getProjectInductionById', policy_checks: ['isMoreThanDeliveryManager'] },
            'get /api/project/:projectId/inducted-users': { controller: 'Project', action: 'getProjectInductedUsers', tools: ['*'], multiple: true, data_type_key: 'tool_key' },

            'get /api/sa/roll-call/:projectId/list': { controller: 'RollCall', action: 'getProjectRollCallRecords', tools: ['*'], multiple: true, data_type_key: '' },
            'post /api/sa/roll-call/:projectId/:rollCallId/download': `${RollCallController}.downloadRollCall`,
            'post /api/sa/project/:projectId/roll-call/random/download': `${ProjectController}.downloadRandomOnSiteOperativesReport`,

            'get /api/sa/project/:projectId/optima/events/:type/list': `${OptimaController}.getRecentUnrecognizedEvents`,
            'post /api/sa/project/:projectId/induction/:inductionRequestId/optima/switch-badge/:badgeNumber': `${OptimaController}.switchInductionBadgeWith`,
            'get /api/sa/project/:projectId/optima/turnstile/status': `${OptimaController}.getTurnstileStatus`,
            'post /api/sa/project/:projectId/optima/turnstile/:turnstileId/state/:state': `${OptimaController}.changeTurnstileState`,
            'get /api/sa/project/:projectId/active/timesheet/list': `${InductionController}.getActiveUsersTimesheetList`,
            'get /api/sa/project/:projectId/timesheet/:userId/fetch': `${InductionController}.getUserTimesheetDetails`,
            'post /api/sa/project/:projectId/timesheets/status/update': `${OptimaController}.approveUserTimesheets`,
            // 'post /api/sa/project/:projectId/timesheet/comment': `${OptimaController}.saveTimesheetComment`,
            'post /api/sa/project/:projectId/timesheet/comment': `${OptimaController}.saveWeeklyTimesheetComments`,
            'post /api/sa/project/:projectId/timesheet/worker-record': `${OptimaController}.saveWorkerRecords`,
            'post /api/sa/project/:projectId/timesheet/download/xls': `${OptimaController}.downloadTimesheets`,
            'post /api/sa/project/:projectId/timesheets/save': `${OptimaController}.saveTimesheetInfo`,
            'get /api/sa/project/:projectId/incident-report/list': { controller: 'projectincidentreport', action: 'getprojectincidentlistsa', tools: ['incident_report'], multiple: false, data_type_key: '' },
            'get /api/sa/project/:projectId/clerk-of-works/list': `${ClerkOfWorksController}.getProjectClerkOfWorksListSA`,
            'get /api/sa/project/:projectId/permit/templates': { controller: 'permit', action: 'fetchProjectPermitTemplates', tools: ['permit_tool'], multiple: false, data_type_key: '' },
            'post /api/sa/project/:projectId/permit-config/save': { controller: 'permit', action: 'saveProjectPermitConfig', tools: ['permit_tool'], multiple: false, data_type_key: '' },
            'get /api/sa/project/:projectId/permit-config/:id': { controller: 'permit', action: 'getProjectPermitConfig', tools: ['permit_tool'], multiple: false, data_type_key: '' },
            'get /api/sa/project/:projectId/permit-request/:id/view': { controller: 'permit', action: 'viewPermitDocument', tools: ['permit_tool'], multiple: false, data_type_key: '' },
            'get /api/sa/project/:projectId/tool-report/:toolName': { controller: 'powerbi', action: 'getProjectToolReport', tools: ['power_bi'], multiple: false, data_type_key: '' },
            'post /api/sa/project/:projectId/tool-report/download/:toolName': { controller: 'powerbi', action: 'exportProjectToolReport', tools: ['power_bi'], multiple: false, data_type_key: '' },
            'post /api/sa/project/:projectId/report/job/:id/update': { controller: 'powerbi', action: 'updateProjectExportQueue', tools: ['power_bi'], multiple: false, data_type_key: '' },
        };
    },
};
