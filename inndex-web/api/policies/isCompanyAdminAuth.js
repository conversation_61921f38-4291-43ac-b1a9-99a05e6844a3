/**
 * Created by spatel on 23/04/20.
 * @deprecated This policy is deprecated. Please use isSA_OR_CA_OR_CPA_Of_v2 instead.
 */

const {TokenUtil:{resourceIdentifier,ROLES}, ResponseService : { authErrorObject, sendResponse }} = require('./../services');
const moment = require('moment');

module.exports = function (req, res, next) {
     sails.log.error(
        "============= Received call on deprecated policy: isCompanyAdminAuth ======================"
    );
    if(req.user.roles.includes(ROLES.SUPER_ADMIN)) {
        sails.log.info(`UV: Validating is Super Admin.`);
        req.is_super_admin = true;
        return next();
    }
    sails.log.info('Validating if user is allowed for company admin route', req.user.id);
    let companyId = +(req.param('employerId') || req.param('companyId')) || 0;
    let resource = resourceIdentifier.COMPANY(companyId);
    let hasCompanyAdminRole = (req.user.raw_uac || []).find(uac => uac.role === ROLES.COMPANY_ADMIN && uac.resource === resource);
    if(hasCompanyAdminRole){
        req.user.companyAdminRole = hasCompanyAdminRole;
        return next();
    }
    sails.log.error('Restricted CA API call, User:', (req.user && req.user.id), req.path, `Time: ${moment().format()} Origin HOST:`, req.headers['host'], 'UA:', req.headers['user-agent']);
    return sendResponse(res, authErrorObject(sails.__('user_does_not_have_permission')));
};
