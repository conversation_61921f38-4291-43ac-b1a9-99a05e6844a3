-- Creating a new parent table `project_weekly_timesheet` to store aggregated weekly timesheets, linked from `project_timesheet`
CREATE TABLE project_weekly_timesheet (
                                          id SERIAL PRIMARY KEY,
                                          week_end_date DATE NOT NULL,
                                          user_ref INT NOT NULL,
                                          project_ref INT NOT NULL REFERENCES project(id) ON DELETE CASCADE,
                                          status INT DEFAULT 1,
                                          modified_by INT,
                                          comments JSONB DEFAULT '[]',
                                          hourly_rate FLOAT,
                                          employee_number TEXT,
                                          "createdAt" bigint,
                                          "updatedAt" bigint
);

-- Adding weekly_timesheet_ref column to project_timesheet to link each entry to its corresponding weekly summary in project_weekly_timesheet
ALTER TABLE project_timesheet
    ADD COLUMN weekly_timesheet_ref INT DEFAULT NULL;

ALTER TABLE project_timesheet
    ADD CONSTRAINT fk_weekly_timesheet_ref
        FOREIGN KEY (weekly_timesheet_ref)
            REFERENCES project_weekly_timesheet(id) ON DELETE CASCADE;

-- indexes for project_weekly_timesheet table
CREATE UNIQUE INDEX idx_unique_user_project_week
    ON project_weekly_timesheet (user_ref, project_ref, week_end_date);

-- Comment in the table
COMMENT ON TABLE project_weekly_timesheet IS 'This table is used to store weekly timesheets for each user per project. Each entry represents a weekly summary including metadata such as status, week end date, comments, and timestamps. further detailed daily timesheet info can be derived by joining with project_timesheet table.';

-- Comment in the column
COMMENT ON COLUMN project_timesheet.weekly_timesheet_ref IS
    'Foreign key reference to the weekly summary entry in project_weekly_timesheet. This links each individual timesheet entry to its aggregated weekly record.';
