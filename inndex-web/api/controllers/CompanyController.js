/**
 * Created by spatel on 21/09/19.
 */
 const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {
    COMPANY_SETTING_KEY,
} = sails.config.constants;
const {
    companyFn,
    inspectionFn,
} = require('./../sql.fn');
 const {
     ResponseService,
     TokenUtil,
     DataProcessingService: {
         getUserFullName,
         updateCompanyProjectsSupplyChain,
     },
     HttpService: {
         typeOf,
         decodeURIParam
     },
    DataToSeedService: {
        seedAssetMetaConfig
    },
 } = require('./../services');
 const _groupBy = _.groupBy;
 const uFuzzy = require("@leeoniya/ufuzzy");

const populateDivisions = async (records) => {
    if (!records.length) {
        return records;
    }
    let companyIds = [...new Set(records.map(r => r.id))];
    sails.log.info(`expanding divisions for ${companyIds.length} companies`);
    let divisions_list = await sails.models.companydivision_reader.find({
        select: ['id', 'name', 'employer_ref'],
        where: {employer_ref: companyIds}
    });

    records = records.map(r => {
        r.divisions = divisions_list.filter(d => d.employer_ref === r.id);
        return r;
    });
    sails.log.info(`expanded total ${divisions_list.length} divisions refs`);
    return records;
};
const getNestedProperty = (obj, path) => {
    return path.split('.').reduce((acc, part) => acc && acc[part], obj);
};

const groupByCompanyId = (fromCompaniesId, records, key) => {
    return fromCompaniesId.reduce((acc, companyId) => {
        acc[companyId] = records.filter(record => {
            const value = getNestedProperty(record, key);
            return value && value.includes(companyId);
        });
        return acc;
    }, {});
};

 const getEmployersList = async (req, res) => {
     let country_code = req.param('country_code', 'GB');
     let expand = (req.query.expand || '').toString().trim();
     let populate_divisions = (req.query.populate_divisions || '').toString().trim();
     let employerlist = [];
     sails.log.info(`get employers list, country_code: "${country_code}", expand: ${expand}, populate_divisions: ${populate_divisions}`);
     let selectQuery = [
         'name',
         'has_company_portal',
         ...((expand === 'true') ? ['max_projects'] : []),
         'has_mates_in_mind',
         'company_initial',
         'logo_file_id',
         'projects_alias',
         'features_status'
     ];
     if(populate_divisions === 'true') {
         employerlist = await sails.models.createemployer.find({
             select: selectQuery,
             where: {country_code},
         }).sort('name ASC');//.populate('divisions');
         employerlist = await populateDivisions(employerlist);
     } else {
         employerlist = await sails.models.createemployer_reader.find({
             select: selectQuery,
             where: {country_code},
         }).sort('name ASC');
     }

     if (employerlist.length) {
         // sails.log.info('fetching all companies logos');
         // let logoIds = employerlist.reduce((list, employer, index) => {
         //     if (employer.logo_file_id) {
         //         list.push(employer.logo_file_id);
         //     }
         //     return list;
         // }, []);
         //
         // let logoFiles = await sails.models.userfile_reader.find({
         //     where: {id: logoIds},
         //     select: ['id', 'file_url']
         // });
         if(req.is_super_admin){
             sails.log.info('fetching companies project & user counts');

             let default_project_count_query = `SELECT p.contractor, count(*) as count FROM (SELECT id, name, contractor FROM project WHERE contractor IS NOT NULL
                                                                                        -- AND is_active = 1
                                                                                        AND custom_field->>'country_code' = $1
                                                                                        AND project_category = 'default') as p GROUP BY p.contractor`;
             let default_project_result = await sails.sendNativeQuery(default_project_count_query, [country_code]);
             let default_project_count = (typeOf(default_project_result.rows, 'array') && default_project_result.rows.length)  ? default_project_result.rows : [];
             let contractors_of_default_project = default_project_count.reduce((obj, row) => {
                 obj[row.contractor] = (+row.count) || 0;
                 return obj;
             }, {});

             let company_project_count_query = `SELECT p.parent_company, count(*) as count FROM (SELECT id, name, parent_company FROM project WHERE parent_company IS NOT NULL
                                                                                                -- AND disabled_on IS NULL
                                                                                                AND custom_field->>'country_code' = $1
                                                                                                AND project_category = 'company-project') as p GROUP BY p.parent_company`;
             let company_project_result = await sails.sendNativeQuery(company_project_count_query, [country_code]);
             let company_project_count = (typeOf(company_project_result.rows, 'array') && company_project_result.rows.length)  ? company_project_result.rows : [];
             let parent_companies_of_company_project = company_project_count.reduce((obj, row) => {
                 obj[row.parent_company] = (+row.count) || 0;
                 return obj;
             }, {});


             let user_count_query = await sails.sendNativeQuery(`SELECT employer, count(*) as count
                                                                 FROM user_employment_detail
                                                                 WHERE user_ref in (select id from users where country_code = $1)
                                                                 GROUP BY employer`, [country_code]);
             let user_count = (typeOf(user_count_query.rows, 'array') && user_count_query.rows.length)  ? user_count_query.rows : [];
             let employer_of_user = user_count.reduce((obj, row) => {
                 obj[row.employer] = (+row.count) || 0;
                 return obj;
             }, {});

             sails.log.info('attaching companies project & user counts and Logos');
             employerlist = employerlist.map((employer)=> {
                 // let file = (logoFiles || []).find(logoFile => employer.logo_file_id && (employer.logo_file_id === logoFile.id)) || {};
                 // employer.logo_file_url = file.file_url || '';
                 employer.default_projects_count = contractors_of_default_project[employer.name] || 0;
                 employer.company_projects_count = parent_companies_of_company_project[employer.id] || 0;
                 employer.total_projects_count = (employer.default_projects_count + employer.company_projects_count);
                 employer.users_count = employer_of_user[employer.name] || 0;
                 return employer;
             });
         }
         // else if(logoFiles && logoFiles.length){
         //     sails.log.info('attaching all companies logos');
         //     employerlist = employerlist.map((employer, index)=> {
         //         let file = logoFiles.find(logoFile => employer.logo_file_id && (employer.logo_file_id === logoFile.id)) || {};
         //         employer.logo_file_url = file.file_url || '';
         //         return employer;
         //     });
         // }
     }
     sails.log.info('fetched company list records', (employerlist || []).length);
     return ResponseService.successResponse(res, {employerlist});
 };

 const createEmployer = async (req, res, {creatorId, creatorName, creatorEmail}) => {
     let employer = {
         name: (req.body.name || req.body.employerName),
         has_company_portal: req.body.has_company_portal,
         max_projects: req.body.max_projects,
         has_mates_in_mind: req.body.has_mates_in_mind,
         company_initial: req.body.company_initial,
         country_code: req.body.country_code || 'GB',
         logo_file_id: req.body.logo_file_id,
         projects_alias: req.body.projects_alias,
         features_status: req.body.features_status
     };
     if(!employer.name || !employer.name.toString().trim().length){
         return ResponseService.errorResponse(res, 'Name is required');
     }
     sails.log.info(`Searching employer if exists? "${employer.name}"`);
     let rawResult = await sails.sendNativeQuery(`SELECT id, name FROM employer WHERE ( LOWER(name) = $1 ) AND country_code = $2`,
         [employer.name.toString().toLowerCase().trim(), employer.country_code]
     );
     let record = (typeOf(rawResult.rows, 'array') && rawResult.rows.length)  ? rawResult.rows.shift() : {};
     if (record && record.id) {
         return ResponseService.successResponse(res, {
             already_exists: true,
             message: 'Record with given name already exist',
             employer: record
         });
     }

     employer.name = employer.name.toString().trim();
     sails.log.info(`creating employer, "${employer.name}", created by ${creatorId}`);
     let employer_record = await sails.models.createemployer.create(employer);
     sails.log.info('Created Employer');
     if(employer_record.has_company_portal) {
        sails.log.info("Seeding asset meta data for company portal enabled employer:", employer_record.id);
        await seedAssetMetaConfig(employer_record.id);
     }
     if (req.body.admins) {
         await TokenUtil.saveCompanyUsers(employer_record, req.body.admins);
     }

     employer_record = await attachLogoWithProject(employer_record);

     await sendAddEmployerNotification(employer_record, {creatorName, creatorEmail});

     return ResponseService.successResponse(res, {
         message: 'Success',
         employer: employer_record
     });
 };

 const sendAddEmployerNotification = async (employer, {creatorName, creatorEmail}) => {
     sails.log.info('Sending add employer notification.');
     let sendToUsers = (sails.config.custom.SUPPORT_MAIL_ADDRESSES || '').split(',');
     let len = (sendToUsers || []).length;
     sails.log.info('Total alerts to send', len);
     let subject = `New employer added!`;
     let html = await sails.renderView('pages/mail/employer-added-notify-admin', {
         title: subject,
         creatorName,
         creatorEmail,
         employerName: employer.name,
         countryCode: employer.country_code,
         layout: false
     });

     for (let i = 0; i < len; i++) {
         let toUser = sendToUsers[i];

         sails.log.info('Sending add employer notification to', toUser);
         try {
             await EmailService.sendMail(subject, [toUser], html);
         } catch (failure) {
             sails.log.info('Failed to send mail', failure);
         }
     }
     return true;
 };


 const attachLogoWithProject = async (employer) => {
     if (employer && employer.logo_file_id) {
         let logoFile = await sails.models.userfile.findOne({
             where: {id: employer.logo_file_id},
             select: ['file_url', 'file_mime']
         });

         if (logoFile && logoFile.file_url) {
             employer.logo_file_url = logoFile.file_url;
             employer.logo_file = logoFile;
         }
     }
     return employer;
 };

 const convertKeyofObjCompanyNameToId = (companiesInfo, results) => {
     (companiesInfo || []).map(companyInfo => {
         if (results[companyInfo.name]) {
             results[companyInfo.id] = results[companyInfo.name];
             delete results[companyInfo.name];
         }
     });
     return results;
 };

 const prepareCompanyUpdateCriteria = (valueArray, column) => {
     let criteria = valueArray.reduce((arr, value) => {
         arr.push({[column]: value});
         return arr;
     }, []);
     sails.log.info(`Criteria for column ${column}`, criteria);
     return criteria;
 };

 const attachCompanyDomains = async (records, userEmail) => {
     let companyIds = records.map(item => item.id);
     sails.log.info(`[attachCompanyDomains]: got ${companyIds.length} company ids to attach allowed domain config if exist.`);
     if (!companyIds.length) {
         return records;
     }
     let company_settings = await sails.models.companysetting_reader.find({
         where: {
             company_ref: companyIds,
             name: COMPANY_SETTING_KEY.COMPANY_ALLOWED_DOMAIN,
             enabled_on: {'!=': null},
         },
         select: ['company_ref', 'value'],
     }) || [];
     sails.log.info(`[attachCompanyDomains]: Found allowed domain config for ${company_settings.length} companies out of ${companyIds.length} companies`);

     const settingsMap = new Map(company_settings.map(cs => [cs.company_ref, cs]));

     records.forEach(item => {
         item.has_disallowed_domain = false;
         const cs = settingsMap.get(item.id);
         if (cs && cs.value && cs.value.length) {
             item.has_disallowed_domain = !(cs.value.filter((domain) => (userEmail).includes(domain)).length)
             item.allowed_domains = cs.value;
         }
     });
     return records;
 }

const companiesList = async (req, res) => {
    let pageSize = +req.param('pageSize', 50);
    let pageNumber = +req.param('pageNumber', 0);
    let sortKey = req.param('sortKey', 'id');
    let sortDir = req.param('sortDir', 'asc');
    let projectId = +req.param('projectId', 0);
    let countryCode = req.param('country_code', 'GB');
    let searchTerm = decodeURIParam((req.param('q', '')).toString().trim());
    let selectCompany = decodeURIParam((req.param('selectedCompany', '')).toString().trim());
    let preSelected = (req.param('selections', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);

    let {
        total: totalCount,
        records
    } = await companyFn.getCompaniesPage(pageSize, (pageSize * pageNumber), sortKey, sortDir, {searchTerm, countryCode, projectId});

    let selections = [];
    if(preSelected.length || selectCompany){
        sails.log.info(`attaching pre-selected companies: ${preSelected} / ${selectCompany}`);
        let where = {
            country_code: countryCode,
        }
        if (selectCompany) {
            where.name = selectCompany;
        } else {
            where.id = preSelected;
        }
        selections = await sails.models.createemployer_reader.find({
            select: ['id', 'name'],
            where,
            sort: [`${sortKey} ${sortDir}`]
        });
    }

    if(req.user) {
        records = await attachCompanyDomains(records, req.user.email);
    }

    return ResponseService.successResponse(res, {
        records,
        selections,
        q: searchTerm,
        countryCode,
        pageSize,
        pageNumber,
        sortKey,
        sortDir,
        totalCount,
    });
};

const searchByuFuzzy = async (companyNames,selections, searchTerm, pageSize) => {
    let opts = {};
    let uf = new uFuzzy(opts);
    let results = [];

    let idxs = uf.filter(companyNames, searchTerm);
    // idxs can be null when the needle is non-searchable (has no alpha-numeric chars)
        if (idxs != null && idxs.length > 0) {
            let info = uf.info(idxs, companyNames, searchTerm);
            // order is a double-indirection array (a re-order of the passed-in idxs)
            // this allows corresponding info to be grabbed directly by idx, if needed
            let order = uf.sort(info, companyNames, searchTerm);
            let leastPageSize = (idxs.length < +pageSize) ? idxs.length : +pageSize;
            // render filtered & ordered matches
            for (let i = 0; i < leastPageSize; i++) {
                // using info.idx here instead of idxs because uf.info() may have
                // further reduced the initial idxs based on prefix/suffix rules
                results.push(selections[info.idx[order[i]]]);
            }
        }
    sails.log.info("Total search records found => ", idxs.length, " for this searchTerm =>", searchTerm);
    return results;
};

const companiesListV3 = async (req, res) => {
    let pageSize = +req.param("pageSize", 10);
    let pageNumber = +req.param("pageNumber", 0);
    let countryCode = req.param("country_code", "GB");
    let searchTerm = req.param("q", "").toString().trim();
    let records = [];
    let totalCount = 0;

    let selections = await sails.models.createemployer_reader.find({
        select: ["id", "name"],
        where: {
            country_code: countryCode,
        }
    });
    sails.log.info('Fetched all companies result => ' , selections.length);
    const companyNames = selections.map((item) => item.name);
    sails.log.info('Initiated fuzzySearch to get filtered companies');
    records = await searchByuFuzzy(companyNames, selections, searchTerm, pageSize);
    totalCount = records.length;

    return ResponseService.successResponse(res, {
        records,
        q: searchTerm,
        countryCode,
        pageSize,
        pageNumber,
        totalCount,
    });
};

const companiesSearchesList = async (req, res) =>{
    let {pageSize, pageNumber, country_code, searchCharLimit, companyIds} = req.body;
    let totalCount = 0;
    let recordsList = [];
    let searchTerms = companyIds;
    let selections = await sails.models.createemployer_reader.find({
        select: ["id", "name"],
        where: {
            country_code: country_code,
        }
    });
    sails.log.info('Fetched all companies result => ' , selections.length);
    const companyNames = selections.map((item) => item.name);
    sails.log.info('Initiated fuzzySearch to get filtered companies');
    for (let i = 0; i < searchTerms.length; i++) {
        const records = await searchByuFuzzy(companyNames, selections, searchTerms[i].name.length >= searchCharLimit ? searchTerms[i].name.substr(0, searchCharLimit) : searchTerms[i].name, pageSize);
        totalCount = records.length;
        const obj = {
            name: searchTerms[i].name,
            id: searchTerms[i].id,
            records: records,
            totalCount: totalCount
        }
        recordsList.push(obj);
    }

    return ResponseService.successResponse(res, {
        recordsList,
        searchTerms,
        country_code,
        pageSize,
        pageNumber,
    });
}

const updateCompanyInduction=async(fromCompaniesId, toCompanyId)=>{
    let companyInduction  = await sails.models.companyinduction.update({
        or: prepareCompanyUpdateCriteria(fromCompaniesId , 'company_ref')
       })
       .set({
           company_ref : toCompanyId
       });
       return companyInduction;
};

 module.exports = {

     addEmployerViaInnTime: async (req, res) => {
         sails.log.info(`employer creation request from innTime app "${req.body.name || req.body.employerName}", project: ${req.project.id}`);
         return createEmployer(req, res, {
             creatorId: `project:${req.project.id}`,
             creatorName: 'innTime app',
             creatorEmail: 'N/A'
         });
     },

     addEmployer: async (req, res) => {
         return createEmployer(req, res, {
             creatorId: req.user.id,
             creatorName: getUserFullName(req.user),
             creatorEmail: req.user.email
         });
     },

     editEmployer: async (req, res) => {
         let employerId = req.param('employerId');
         sails.log.info('update employer request', employerId);
         let updateRequest = _.pick((req.body || {}), [
             'name',
             'has_company_portal',
             'max_projects',
             'has_mates_in_mind',
             'company_initial',
             'logo_file_id',
             'projects_alias',
             'features_status',
             'fatigue_form_title',
             'fatigue_form_file_id',
     ]);
         try {
            const existingEmployer = await sails.models.createemployer_reader.findOne({ id: employerId });
            if (!existingEmployer.has_company_portal && updateRequest.has_company_portal === true) {
                const existingConfigCount = await sails.models.assetcustomconfig_reader.count({ 'company_ref': employerId});
                if (existingConfigCount === 0) {
                    sails.log.info('Seeding company asset config data after company portal enabled for companyId:', employerId);
                    await seedAssetMetaConfig(employerId);
                }
            }

            let employer = await sails.models.createemployer.updateOne({id: employerId}).set(updateRequest);
            if (req.body.admins) {
                await TokenUtil.saveCompanyUsers(employer, req.body.admins);
            }

            employer = await attachLogoWithProject(employer);

            return ResponseService.successResponse(res, {employer});
         } catch (e) {
             sails.log.error('Failed to update Employer', e);
             return ResponseService.errorResponse(res, 'Failed to update Employer', e);
         }

     },

     getEmployer: getEmployersList,
     getEmployersListForInnTimeApp: getEmployersList,

     deleteEmployer: async (req, res) => {

         let deletedOneRecords = await sails.models.createemployer.destroy({
             id: req.body.id,
             has_company_portal: false
         });
         return ((deletedOneRecords && deletedOneRecords.length) ?
             ResponseService.successResponse(res, {deleted: deletedOneRecords}) :
             ResponseService.errorResponse(res, `"Company portal" enabled company can't be deleted`));
     },

     deleteCompanyUser: async (req, res) => {
         let resource = TokenUtil.resourceIdentifier.COMPANY(req.param('companyId'));

         sails.log.info('Remove CA access record, companyId', resource);
         let deletedOneRecords = await TokenUtil.removeUAC(+req.param('uacId'), {
             role: TokenUtil.ROLES.COMPANY_ADMIN,
             resource: resource
         });
         return ResponseService.successResponse(res, {deleted: deletedOneRecords});
     },

     getUserCompanies: async (req, res) => {
         let companiesIds = TokenUtil.getAllowedCompaniesOfUser(req.user.raw_uac);
         let companies = [];
         sails.log.info('Total companies authorized to user :', companiesIds && companiesIds.length);
         if(companiesIds && companiesIds.length){
             companies = await sails.models.createemployer.find({
                 id: companiesIds,
                 has_company_portal: true
             });
         }

         return ResponseService.successResponse(res, {companies});
     },

     getEmployerById: async (req, res) => {
         let employerId = req.param('employerId');
         sails.log.info('employerId', employerId);
         let expand = (req.query.expand || '').toString().trim() === 'true';
         try {
             if (employerId) {
                 let employer = await sails.models.createemployer
                     .findOne({
                         where: {id: employerId},
                         select: ['name',
                             'has_company_portal',
                             ...(expand ? ['max_projects', 'has_mates_in_mind'] : []),
                             'company_initial',
                             'logo_file_id',
                             'projects_alias',
                             'country_code',
                             'features_status', 'fatigue_form_title', 'fatigue_form_file_id']
                     }).populate('fatigue_form_file_id').populate('divisions');

                 employer = await attachLogoWithProject(employer);
                 if(expand){
                     //@todo: IF CA of current company then only admins list is needed. Otherwise do not add it.
                     employer.admins = await TokenUtil.getAllCompanyUsers(employerId);
                     sails.log.info(`Looking for Company induction settings of company: ${employerId}`);
                     let company_induction_setting  = await sails.models.companysetting_reader.findOne({
                         where: {
                             company_ref: employerId,
                             name: COMPANY_SETTING_KEY.COMPANY_INDUCTION,
                         }
                     });
                     employer._ci_enabled = (company_induction_setting && company_induction_setting.value && company_induction_setting.value.status) || false;
                     employer._company_induction_phrasing = (company_induction_setting && company_induction_setting.value && company_induction_setting.value.phrasing) ? company_induction_setting.value.phrasing : null;
                 }

                 sails.log.info('fetched employer record', employer.id);
                 return ResponseService.successResponse(res, {employer});
             } else {
                 sails.log.info('Error while fetching', 'Employer Id is required');
                 return ResponseService.errorResponse(res, sails.__('internal server error'), 'Employer Id is required');
             }
         } catch (error) {
             sails.log.info('Error while fetching', error);
             return ResponseService.errorResponse(res, sails.__('internal server error'), error);
         }
     },

     saveNewCompanyUsers: async (req, res) => {
         let company = req.body;
         if (company && company.id) {
             let company_users_request = company.admins;
             await TokenUtil.saveCompanyUsers(company, company_users_request);
             sails.log.info('updated records', (company_users_request || []).length);
             return ResponseService.successResponse(res, {});
         } else {
             sails.log.info('Error while saving company users', error);
             return ResponseService.errorResponse(res, sails.__('internal server error'), 'Error while saving company users');
         }
     },

    /*
     // @deprecated: on 23rd March 2023
     getEmployerLogoByName: async (req, res) => {
         let employerName = req.param('employerName');
         let countryCode = req.param('country_code', 'GB');
         sails.log.info('employer logo request', employerName);
         try {
             let employer = await sails.models.createemployer.findOne({
                 where: {name: employerName, country_code: countryCode},
                 select: ['logo_file_id']
             });

             let logo_file = {};
             if (employer && employer.logo_file_id) {
                 logo_file = await sails.models.userfile.findOne({
                     where: {id: employer.logo_file_id}
                 });
             }

             return ResponseService.successResponse(res, {
                 message: 'Success',
                 logo_file
             });
         } catch (e) {
             sails.log.error('Failed to fetch employer logo', e);
             return ResponseService.errorResponse(res, 'Failed to fetch employer logo', e);
         }
     },

     // @deprecated: on 23rd March 2023
     getEmployersLogo: async (req, res) => {
         try {
             let employerlist = await sails.models.createemployer.find({select: ['name', 'logo_file_id']}).sort('name ASC');
             if (employerlist.length) {
                 let logoIds = employerlist.map((employer, index) => {
                     if (employer.logo_file_id) {
                         return employer.logo_file_id;
                     }
                 });

                 let logoFiles = await sails.models.userfile.find({
                     where: {id: logoIds},
                     select: ['id', 'file_url']
                 });

                 if (logoFiles && logoFiles.length) {
                     employerlist = employerlist.map((employer, index)=> {
                         logoFiles.forEach((logoFile)=> {
                             if (employer.logo_file_id && (employer.logo_file_id === logoFile.id)) {
                                 employer.logo_file_url = logoFile.file_url;
                                 employer.logo_file_id = logoFile;
                                 return;
                             }
                         });
                         if (!employer.logo_file_url) {
                             employer.logo_file_url = '';
                         }
                         return employer;
                     });
                 }
             }
             sails.log.info('fetched records', (employerlist || []).length);
             return ResponseService.successResponse(res, {employers_logo: employerlist});
         } catch (error) {
             sails.log.info('Error while fetching', error);
             return ResponseService.errorResponse(res, sails.__('internal server error'), error);
         }
     },
    */

     getEntitiesCountByCompany: async (req, res) => {
         let employerId = req.param('employerId');
         sails.log.info('Fetch projects by employer id:', employerId);

         let employerInfo = await sails.models.createemployer.findOne({
             where: {id: employerId},
             select: ['name']
         });

         let employer_name = employerInfo.name;

         //company projects
         let active_projects_count = await sails.models.project.count({parent_company: employerId,is_active: 1, disabled_on: null});
         let inactive_projects_count = await sails.models.project.count({parent_company: employerId, disabled_on: {'!=': null}});

         //additional projects / Inherited projects
         let active_additional_projects_count = await sails.models.project.count({contractor: employer_name, project_category: 'default', is_active: 1, disabled_on: null});
         let inactive_additional_projects_count = await sails.models.project.count({contractor: employer_name, project_category: 'default', is_active: 0});

         let users_count = await sails.models.userempdetail.count({employer: employer_name});

         return ResponseService.successResponse(res, {
             active_company_projects: active_projects_count,
             active_projects_count: +active_projects_count + +active_additional_projects_count,
             inactive_projects_count: +inactive_projects_count + +inactive_additional_projects_count,
             users_count,
             employer_name,
         });
     },

     //Utkarsh: Updated the API on 5th Feb 2024 upto migration 281_
     mergeCompany: async (req, res) => {
         sails.log.info('Request to merge company.');
         let fromCompaniesId = req.body.from_companies_id;
         let toCompanyId = +req.body.to_company_id;
         let merge_as_well = (req.body.merge === 'yes');

         if (toCompanyId && (typeof fromCompaniesId === 'object' && fromCompaniesId.length)) {
             sails.log.info('Request is valid.');

             let companiesInfo = await sails.models.createemployer_reader.find({
                 where: {id: [...fromCompaniesId, toCompanyId]},
                 select: ['id', 'name', 'country_code']
             });

             sails.log.info('companiesInfo', companiesInfo);

             let [toCompanyInfo] = (companiesInfo || []).filter(company => company.id === toCompanyId);

             let fromCompaniesInfo = companiesInfo.filter(company => {
                 return fromCompaniesId.includes(company.id)
             });

             if (!toCompanyInfo.id || !fromCompaniesInfo.length) {
                 sails.log.info("Invalid request, Companies data are not available.");
                 return ResponseService.errorResponse(res, "Invalid request, Companies data are not available.");
             }

             let fromCompaniesName = fromCompaniesInfo.map(company => company.name);

             if (!merge_as_well) {
                 sails.log.info('Count request');

                 let companiesProjects = await sails.models.project_reader.find({
                     where: {parent_company: [...fromCompaniesId]},
                     select: ['id', 'parent_company']
                 });
                companiesProjects = _groupBy(companiesProjects || [], (l) => l.parent_company);

                let supplyChainProjects = await sails.sendNativeQuery(`SELECT id, parent_company, custom_field-> 'supply_chain_companies' as supply_chain_companies FROM project p
                    WHERE EXISTS (
                        SELECT 1
                        FROM json_array_elements_text(custom_field->'supply_chain_companies') AS elem
                        WHERE elem::int in (${fromCompaniesId.map(a=>+a)})
                    );
                `);
                supplyChainProjects = (typeOf(supplyChainProjects.rows, 'array') && supplyChainProjects.rows.length) ? supplyChainProjects.rows : [];

                supplyChainProjects = groupByCompanyId(
                    fromCompaniesId,
                    supplyChainProjects,
                    'supply_chain_companies'
                );

                let supplychainCompanySettings = await sails.sendNativeQuery(`SELECT *
                    FROM company_setting
                    WHERE name = 'company_supply_chain_config'
                    AND EXISTS (
                        SELECT 1
                        FROM jsonb_array_elements_text(value -> 'supply_chain') AS elem
                        WHERE elem::int in (${fromCompaniesId.map(a=>+a)})
                    );
                `);

                supplychainCompanySettings = (typeOf(supplychainCompanySettings.rows, 'array') && supplychainCompanySettings.rows.length) ? supplychainCompanySettings.rows : [];

                supplychainCompanySettings = groupByCompanyId(
                    fromCompaniesId,
                    supplychainCompanySettings,
                    'value.supply_chain'
                );

                 let companiesAsProjectContractor = await sails.models.project_reader.find({
                     where: {contractor: fromCompaniesName},
                     select: ['id', 'contractor']
                 });
                 companiesAsProjectContractor = _groupBy(companiesAsProjectContractor || [], (l) => l.contractor);
                 companiesAsProjectContractor = convertKeyofObjCompanyNameToId(companiesInfo, companiesAsProjectContractor);

                 let usersEmployer = await sails.models.userempdetail_reader.find({
                     where: {employer: fromCompaniesName},
                     select: ['id', 'employer', 'user_ref']
                 });
                 usersEmployer = _groupBy(usersEmployer || [], (l) => l.employer);
                 usersEmployer = convertKeyofObjCompanyNameToId(companiesInfo, usersEmployer);

                 let usersEmploymentCompany = await sails.models.userempdetail_reader.find({
                     where: {employment_company: fromCompaniesName},
                     select: ['id', 'employment_company', 'user_ref']
                 });
                 usersEmploymentCompany = _groupBy(usersEmploymentCompany || [], (l) => l.employment_company);
                 usersEmploymentCompany = convertKeyofObjCompanyNameToId(companiesInfo, usersEmploymentCompany);

                 let badgeEvent = await sails.models.badgeevent_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref', 'project_ref']
                 });
                 badgeEvent = _groupBy(badgeEvent || [], (l) => l.company_ref);

                 let clerkOfWorks = await sails.models.clerkofworks_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 clerkOfWorks = _groupBy(clerkOfWorks || [], (l) => l.company_ref);

                 let clerkOfWorkComments = await sails.models.clerkofworkcomments_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 clerkOfWorkComments = _groupBy(clerkOfWorkComments || [], (l) => l.company_ref);

                 let closeCalls = await sails.models.closecall_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 closeCalls = _groupBy(closeCalls || [], (l) => l.company_ref);

                 let eLearningModules = await sails.models.elearningmodule_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 eLearningModules = _groupBy(eLearningModules || [], (l) => l.company_ref);

                 let goodCalls = await sails.models.goodcall_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 goodCalls = _groupBy(goodCalls || [], (l) => l.company_ref);

                 let projectIncidentReports = await sails.models.projectincidentreport.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 projectIncidentReports = _groupBy(projectIncidentReports || [], (l) => l.company_ref);

                 let projectInspectionTours = await sails.models.projectinspectiontour_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 projectInspectionTours = _groupBy(projectInspectionTours || [], (l) => l.company_ref);

                 let projectPowra = await sails.models.projectpowra_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 projectPowra = _groupBy(projectPowra || [], (l) => l.company_ref);

                 let toolboxtalks = await sails.models.toolboxtalks_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 toolboxtalks = _groupBy(toolboxtalks || [], (l) => l.company_ref);

                 let companyInduction = await sails.models.companyinduction_reader.find({
                    where: {company_ref: fromCompaniesId},select: ['id' , "company_ref"]
                 });
                 companyInduction = _groupBy(companyInduction || [] , (l) => l.company_ref);

                 let touchByteLogs = await sails.models.touchbytelog_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref', 'project_ref']
                 });
                 touchByteLogs = _groupBy(touchByteLogs || [], (l) => l.company_ref);

                 let userDailyLogs = await sails.models.userdailylog_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref', 'project_ref']
                 });
                 userDailyLogs = _groupBy(userDailyLogs || [], (l) => l.company_ref);

                 let userTimeLogs = await sails.models.usertimelog_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref', 'project_ref']
                 });
                 userTimeLogs = _groupBy(userTimeLogs || [], (l) => l.company_ref);

                 let users = await sails.models.user_reader.find({
                     where: {parent_company: [...fromCompaniesId]},
                     select: ['id', 'first_name', 'last_name', 'parent_company']
                 });
                 users = _groupBy(users || [], (l) => l.parent_company);

                 let startingNoOfEscaped = 0;
                 let inClause = fromCompaniesName.map(() => {
                     startingNoOfEscaped++;
                     return `$${startingNoOfEscaped}`;
                 }).join(',');
                 let inductionRequestsByEmpResult = await sails.sendNativeQuery(`SELECT id, record_id, user_ref, additional_data->'employment_detail'->'employer' AS employer
             FROM induction_request
             WHERE (additional_data->'employment_detail'->>'employer') IN (${inClause})`,
                     [...fromCompaniesName]
                 );
                 let inductionRequestsByEmp = (typeOf(inductionRequestsByEmpResult.rows, 'array') && inductionRequestsByEmpResult.rows.length) ? inductionRequestsByEmpResult.rows : [];
                 inductionRequestsByEmp = _groupBy(inductionRequestsByEmp || [], (l) => l.employer);
                 inductionRequestsByEmp = convertKeyofObjCompanyNameToId(companiesInfo, inductionRequestsByEmp);

                 let inductionRequestsByEmpCompanyResult = await sails.sendNativeQuery(`SELECT id, record_id, user_ref, additional_data->'employment_detail'->'employment_company' AS employment_company
             FROM induction_request
             WHERE (additional_data->'employment_detail'->>'employment_company') IN (${inClause})`,
                     [...fromCompaniesName]
                 );
                 let inductionRequestsByEmpCompany = (typeOf(inductionRequestsByEmpCompanyResult.rows, 'array') && inductionRequestsByEmpCompanyResult.rows.length) ? inductionRequestsByEmpCompanyResult.rows : [];
                 inductionRequestsByEmpCompany = _groupBy(inductionRequestsByEmpCompany || [], (l) => l.employment_company);
                 inductionRequestsByEmpCompany = convertKeyofObjCompanyNameToId(companiesInfo, inductionRequestsByEmpCompany);

                 let inductionRequestsByUserParentCompanyResult = await sails.sendNativeQuery(`SELECT id, record_id, user_ref, additional_data->'user_info'->'parent_company'->'name' AS parent_company
             FROM induction_request
             WHERE (additional_data->'user_info'->'parent_company'->>'name') IN (${inClause})`,
                     [...fromCompaniesName]
                 );
                 let inductionRequestsByUserParentCompany = (typeOf(inductionRequestsByUserParentCompanyResult.rows, 'array') && inductionRequestsByUserParentCompanyResult.rows.length) ? inductionRequestsByUserParentCompanyResult.rows : [];
                 inductionRequestsByUserParentCompany = _groupBy(inductionRequestsByUserParentCompany || [], (l) => l.parent_company);
                 inductionRequestsByUserParentCompany = convertKeyofObjCompanyNameToId(companiesInfo, inductionRequestsByUserParentCompany);

                 let inductionRequestsByUserParentCompanyIdResult = await sails.sendNativeQuery(`SELECT id, record_id, user_ref, additional_data->'user_info'->'parent_company' AS parent_company
             FROM induction_request
             WHERE (additional_data->'user_info'->>'parent_company')::text IN (${inClause})`,
                     [...fromCompaniesId.map(String)]
                 );
                 let inductionRequestsByUserParentCompanyId = (typeOf(inductionRequestsByUserParentCompanyIdResult.rows, 'array') && inductionRequestsByUserParentCompanyIdResult.rows.length) ? inductionRequestsByUserParentCompanyIdResult.rows : [];
                 inductionRequestsByUserParentCompanyId = _groupBy(inductionRequestsByUserParentCompanyId || [], (l) => l.parent_company);
                 inductionRequestsByUserParentCompanyId = convertKeyofObjCompanyNameToId(companiesInfo, inductionRequestsByUserParentCompanyId);

                 let inductionRequestsByProjectContractorResult = await sails.sendNativeQuery(`SELECT id, record_id, user_ref, additional_data->'project'->'contractor' AS contractor
             FROM induction_request
             WHERE (additional_data->'project'->>'contractor') IN (${inClause})`,
                     [...fromCompaniesName]
                 );
                 let inductionRequestsByProjectContractor = (typeOf(inductionRequestsByProjectContractorResult.rows, 'array') && inductionRequestsByProjectContractorResult.rows.length) ? inductionRequestsByProjectContractorResult.rows : [];
                 inductionRequestsByProjectContractor = _groupBy(inductionRequestsByProjectContractor || [], (l) => l.contractor);
                 inductionRequestsByProjectContractor = convertKeyofObjCompanyNameToId(companiesInfo, inductionRequestsByProjectContractor);

                 let criteria = fromCompaniesId.reduce((arr, id) => {
                     arr.push({resource: TokenUtil.resourceIdentifier.COMPANY(id)});
                     return arr;
                 }, []);
                 let where = {
                     or: criteria
                 };

                 let usersPermission = await sails.models.userrole_reader.find({where, sort: ['id ASC'], select: ['id', 'resource']});
                 if (usersPermission && usersPermission.length) {
                     usersPermission.map(userPermission => {
                         userPermission.resource = TokenUtil.resourceIdentifier.getResourceId(TokenUtil.resourceIdentifier.COMPANY_PREFIX, userPermission.resource);
                         return userPermission;
                     });
                 }
                 usersPermission = _groupBy(usersPermission || [], (l) => l.resource);

                 let taggedOwnerFilter = (fromCompaniesId).reduce((str, id, index) => {
                     str += `${id} = ANY(tagged_owner)${(fromCompaniesId.length-1 > index) ? ' OR ' : ''}`
                     return str;
                 }, '');
                 let assetVehicleRawResult = await sails.sendNativeQuery(`SELECT id, tagged_owner FROM project_asset_vehicles WHERE ${taggedOwnerFilter}`);
                 let projectAssetVehiclesOwner = (typeOf(assetVehicleRawResult.rows, 'array') && assetVehicleRawResult.rows.length)  ? assetVehicleRawResult.rows : [];

                 let assetEquipmentRawResult = await sails.sendNativeQuery(`SELECT id, tagged_owner FROM project_asset_equipment WHERE ${taggedOwnerFilter}`);
                 let projectAssetEquipmentOwner = (typeOf(assetEquipmentRawResult.rows, 'array') && assetEquipmentRawResult.rows.length)  ? assetEquipmentRawResult.rows : [];
                 let projectTemporaryWorktaggedOwnerFilter  = (fromCompaniesId).reduce((str, id, index) => {
                    str += `${id} = ANY(tagged_owner) ${(fromCompaniesId.length-1 > index) ? ' OR ' : ''}`
                    return str;
                }, '')

                let temporaryWorkTaggedOwner = await sails.sendNativeQuery(`SELECT id, tagged_owner FROM project_asset_temporary_work WHERE ${projectTemporaryWorktaggedOwnerFilter} `)
                let projectAssetTemporaryWork = (typeOf(temporaryWorkTaggedOwner.rows, 'array') && temporaryWorkTaggedOwner.rows.length)  ? temporaryWorkTaggedOwner.rows : [];

                 let closeCallTaggedOwner = await sails.models.closecall_reader.find({
                     where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                 });
                 closeCallTaggedOwner = _groupBy(closeCallTaggedOwner || [], (l) => l.tagged_owner);

                 let goodCallTaggedOwner = await sails.models.goodcall_reader.find({
                     where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                 });
                 goodCallTaggedOwner = _groupBy(goodCallTaggedOwner || [], (l) => l.tagged_owner);

                 let visitor = await sails.models.visitor_reader.find({
                     where: {employer_ref: fromCompaniesId}, select: ['id', 'employer_ref']
                 });
                 visitor = _groupBy(visitor || [], (l) => l.employer_ref);

                 let ibChecklist = await sails.models.inspectionbuilder_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 ibChecklist = _groupBy(ibChecklist || [], (l) => l.company_ref);

                 let projectFatigueViolations = await sails.models.projectfatigueviolations_reader.find({
                     where: {user_company_ref: fromCompaniesId}, select: ['id', 'user_company_ref']
                 });
                 projectFatigueViolations = _groupBy(projectFatigueViolations || [], (l) => l.user_company_ref);

                 let companyDivision = await sails.models.companydivision_reader.find({
                     where: {employer_ref: fromCompaniesId}, select: ['id', 'employer_ref']
                 });
                 companyDivision = _groupBy(companyDivision || [], (l) => l.employer_ref);

                 let projectWorkpackageplansTaggedOwner = await sails.models.projectworkpackageplans_reader.find({
                    where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                });
                 projectWorkpackageplansTaggedOwner = _groupBy(projectWorkpackageplansTaggedOwner || [], (l) => l.tagged_owner);

                 let projectTaskBriefingsTaggedOwner = await sails.models.projecttaskbriefings_reader.find({
                    where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                 });
                 projectTaskBriefingsTaggedOwner = _groupBy(projectTaskBriefingsTaggedOwner || [], (l) => l.tagged_owner);

                 let toolboxTalksTaggedOwner = await sails.models.toolboxtalks_reader.find({
                    where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                 });
                 toolboxTalksTaggedOwner = _groupBy(toolboxTalksTaggedOwner || [], (l) => l.tagged_owner);

                 let projectRamsTaggedOwner = await sails.models.projectrams.find({
                     where: {tagged_owner: fromCompaniesId}, select: ['id', 'tagged_owner']
                 });
                 projectRamsTaggedOwner = _groupBy(projectRamsTaggedOwner || [], (l) => l.tagged_owner);

                 let incidentAlertPreference = await sails.models.incidentalertpreference_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 incidentAlertPreference = _groupBy(incidentAlertPreference || [], (l) => l.company_ref);

                 let projectIncidentReportCompany = await sails.models.projectincidentreport_reader.find({
                     where: {project_company: fromCompaniesId}, select: ['id', 'project_company']
                 });
                 projectIncidentReportCompany = _groupBy(projectIncidentReportCompany || [], (l) => l.project_company);

                 let companySettings = await sails.models.companysetting_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 companySettings = _groupBy(companySettings || [], (l) => l.company_ref);

                 let conductCards = await sails.models.conductcard_reader.find({
                     where: {company_ref: fromCompaniesId}, select: ['id', 'company_ref']
                 });
                 conductCards = _groupBy(conductCards || [], (l) => l.company_ref);

                 let associatedRecords = {
                     toCompanyInfo: toCompanyInfo,
                     supplyChainProjects: supplyChainProjects,
                     supplychainCompanySettings,
                     fromCompaniesInfo: fromCompaniesInfo,
                     companiesProjects: companiesProjects,
                     companiesAsProjectContractor: companiesAsProjectContractor,
                     usersEmployer: usersEmployer,
                     usersEmploymentCompany: usersEmploymentCompany,
                     badgeEvent: badgeEvent,
                     clerkOfWorks: clerkOfWorks,
                     clerkOfWorkComments: clerkOfWorkComments,
                     closeCalls: closeCalls,
                     eLearningModules: eLearningModules,
                     goodCalls: goodCalls,
                     projectIncidentReports: projectIncidentReports,
                     projectInspectionTours: projectInspectionTours,
                     projectPowra: projectPowra,
                     toolboxtalks: toolboxtalks,
                     companyInduction:companyInduction,
                     touchByteLogs: touchByteLogs,
                     userDailyLogs: userDailyLogs,
                     userTimeLogs: userTimeLogs,
                     users: users,
                     inductionRequestsByEmp: inductionRequestsByEmp,
                     inductionRequestsByEmpCompany: inductionRequestsByEmpCompany,
                     inductionRequestsByUserParentCompany: inductionRequestsByUserParentCompany,
                     inductionRequestsByUserParentCompanyId: inductionRequestsByUserParentCompanyId,
                     inductionRequestsByProjectContractor: inductionRequestsByProjectContractor,
                     usersPermission: usersPermission,
                     projectAssetVehiclesOwner: projectAssetVehiclesOwner,
                     projectAssetEquipmentOwner: projectAssetEquipmentOwner,
                     closeCallTaggedOwner: closeCallTaggedOwner,
                     goodCallTaggedOwner: goodCallTaggedOwner,
                     visitor: visitor,
                     ibChecklist: ibChecklist,
                     projectFatigueViolations: projectFatigueViolations,
                     companyDivision: companyDivision,
                     toolboxTalksTaggedOwner: toolboxTalksTaggedOwner,
                     projectTaskBriefingsTaggedOwner: projectTaskBriefingsTaggedOwner,
                     projectWorkpackageplansTaggedOwner: projectWorkpackageplansTaggedOwner,
                     projectRamsTaggedOwner: projectRamsTaggedOwner,
                     incidentAlertPreference: incidentAlertPreference,
                     projectIncidentReportCompany: projectIncidentReportCompany,
                     companySettings: companySettings,
                     conductCards: conductCards,
                     projectAssetTemporaryWork:projectAssetTemporaryWork
                 };
                 sails.log.info('Responding with count, no-merge');

                 return ResponseService.successResponse(res, {associatedRecords: associatedRecords});
             } else {
                 sails.log.info(`[mergeCompany] starting merge, from: ${fromCompaniesId} to: ${toCompanyInfo.id}`);

                 let companiesProjects = await sails.models.project.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'parent_company')
                 })
                     .set({
                         parent_company: toCompanyInfo.id
                     });
                let supplyChainProjects = await sails.sendNativeQuery(`UPDATE project
                    SET custom_field = jsonb_set(
                        jsonb(custom_field),
                        '{supply_chain_companies}',
                        (SELECT jsonb_agg(
                            CASE
                                WHEN elem::int in (${fromCompaniesId.map(a=>+a)}) THEN '${toCompanyInfo.id}'
                                ELSE elem
                            END::text::jsonb
                        )
                        FROM json_array_elements_text(custom_field -> 'supply_chain_companies') AS elem)
                    )
                    WHERE EXISTS (
                        SELECT 1
                        FROM json_array_elements_text(custom_field -> 'supply_chain_companies') AS elem
                        WHERE elem::int in (${fromCompaniesId.map(a=>+a)})
                    ) RETURNING id;
                `);

                let supplychainCompanySettings = await sails.sendNativeQuery(`update company_setting SET value = jsonb_set(
                    jsonb(value),
                    '{supply_chain}',
                    (SELECT jsonb_agg(
                        CASE
                            WHEN elem::int in (${fromCompaniesId.map(a=>+a)}) THEN '${toCompanyInfo.id}'
                            ELSE elem
                        END::text::jsonb
                    )
                    FROM jsonb_array_elements_text(value -> 'supply_chain') AS elem)
                    )
                    WHERE name = 'company_supply_chain_config' AND EXISTS (
                        SELECT 1
                        FROM jsonb_array_elements_text(value -> 'supply_chain') AS elem
                        WHERE elem::int in (${fromCompaniesId.map(a=>+a)})
                    ) RETURNING id;`);

                 let companiesAsProjectContractor = await sails.models.project.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesName, 'contractor')
                 })
                     .set({
                         contractor: toCompanyInfo.name
                     });

                 let usersEmployer = await sails.models.userempdetail.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesName, 'employer')
                 })
                     .set({
                         employer: toCompanyInfo.name
                     });

                 let usersEmploymentCompany = await sails.models.userempdetail.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesName, 'employment_company')
                 })
                     .set({
                         employment_company: toCompanyInfo.name
                     });

                 let badgeEvent = await sails.models.badgeevent.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let clerkOfWorks = await sails.models.clerkofworks.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let clerkOfWorkComments = await sails.models.clerkofworkcomments.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let closeCalls = await sails.models.closecall.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let eLearningModules = await sails.models.elearningmodule.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let goodCalls = await sails.models.goodcall.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let projectIncidentReports = await sails.models.projectincidentreport.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let projectInspectionTours = await sails.models.projectinspectiontour.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let projectPowra = await sails.models.projectpowra.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let toolboxtalks = await sails.models.toolboxtalks.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                let companyInduction;
                try{
                    companyInduction  = await updateCompanyInduction(fromCompaniesId,toCompanyInfo.id);
                }catch(e){
                    await sails.sendNativeQuery(`Delete FROM company_induction
                    WHERE company_ref in (${fromCompaniesId})
                    AND user_ref IN (SELECT user_ref FROM company_induction WHERE company_ref = ${toCompanyInfo.id});`)
                    companyInduction  = await updateCompanyInduction(fromCompaniesId,toCompanyInfo.id);
                }

                 let touchByteLogs = await sails.models.touchbytelog.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let userDailyLogs = await sails.models.userdailylog.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let userTimeLogs = await sails.models.usertimelog.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let users = await sails.models.user.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'parent_company')
                 })
                     .set({
                         parent_company: toCompanyInfo.id
                     });

                 sails.log.info(`[mergeCompany] updating induction references for merge`);
                 let startingNoOfEscaped = 0;
                 let inClause = fromCompaniesName.map(() => {
                     startingNoOfEscaped++;
                     return `$${startingNoOfEscaped}`;
                 }).join(',');

                 startingNoOfEscaped++;

                 /*
                 let inductionsOfGivenEmployer = await sails.sendNativeQuery(`UPDATE induction_request
                    SET employer_name = $${startingNoOfEscaped}
                        WHERE employer_name IN (${inClause})
                        AND additional_data->'user_info'->>'country_code' = '${toCompanyInfo.country_code}';`,
                     [...fromCompaniesName, toCompanyInfo.name]);
                 */

                 let inductionRequestsByEmpResult = await sails.sendNativeQuery(`UPDATE
                 induction_request
                 SET
                 additional_data =
                     jsonb_set(
                         jsonb (additional_data),
                         '{employment_detail, employer}',
                         to_jsonb($${startingNoOfEscaped}::text)
                 )
                 WHERE (additional_data->'employment_detail'->>'employer') IN (${inClause})
                             AND additional_data->'user_info'->>'country_code' = '${toCompanyInfo.country_code}';`,
                     [...fromCompaniesName, toCompanyInfo.name]
                 );
                 let inductionRequestsByEmp = inductionRequestsByEmpResult.rowCount;

                 let inductionRequestsByEmpCompanyResult = await sails.sendNativeQuery(`UPDATE
                 induction_request
                 SET
                 additional_data =
                     jsonb_set(
                         jsonb (additional_data),
                         '{employment_detail, employment_company}',
                         to_jsonb($${startingNoOfEscaped}::text)
                 )
                 WHERE (additional_data->'employment_detail'->>'employment_company') IN (${inClause})
                             AND additional_data->'user_info'->>'country_code' = '${toCompanyInfo.country_code}'`,
                     [...fromCompaniesName, toCompanyInfo.name]
                 );
                 let inductionRequestsByEmpCompany = inductionRequestsByEmpCompanyResult.rowCount;

                 let inductionRequestsByUserParentCompanyResult = await sails.sendNativeQuery(`UPDATE
                 induction_request
                 SET
                 additional_data =
                     jsonb_set(
                         jsonb (additional_data),
                         '{user_info, parent_company}',
                         to_jsonb($${startingNoOfEscaped}::numeric)
                 )
                 WHERE (additional_data->'user_info'->'parent_company'->>'name') IN (${inClause})
                             AND additional_data->'user_info'->>'country_code' = '${toCompanyInfo.country_code}'`,
                     [...fromCompaniesName, toCompanyInfo.id]
                 );
                 let inductionRequestsByUserParentCompany = inductionRequestsByUserParentCompanyResult.rowCount;

                 let inductionRequestsByUserParentCompanyIdResult = await sails.sendNativeQuery(`UPDATE
                     induction_request
                     SET
                     additional_data =
                         jsonb_set(
                             jsonb (additional_data),
                             '{user_info, parent_company}',
                             to_jsonb($${startingNoOfEscaped}::numeric )
                     )
                     WHERE (additional_data->'user_info'->>'parent_company')::text IN (${inClause})`,
                     [...fromCompaniesId.map(String), toCompanyInfo.id]
                 );
                 let inductionRequestsByUserParentCompanyId = inductionRequestsByUserParentCompanyIdResult.rowCount;

                 let inductionRequestsByProjectContractorResult = await sails.sendNativeQuery(`UPDATE
                 induction_request
                 SET
                 additional_data =
                     jsonb_set(
                         jsonb (additional_data),
                         '{project, contractor}',
                         to_jsonb($${startingNoOfEscaped}::text)
                 )
                 WHERE (additional_data->'project'->>'contractor') IN (${inClause})
                             AND additional_data->'project'->'custom_field'->>'country_code' = '${toCompanyInfo.country_code}'`,
                     [...fromCompaniesName, toCompanyInfo.name]
                 );
                 let inductionRequestsByProjectContractor = inductionRequestsByProjectContractorResult.rowCount;

                 sails.log.info(`[mergeCompany] updated induction references with new company`);

                 let criteria = fromCompaniesId.reduce((arr, id) => {
                     arr.push({resource: TokenUtil.resourceIdentifier.COMPANY(id)});
                     return arr;
                 }, []);
                 let usersPermission;
                 try{
                    usersPermission= await sails.models.userrole.update({
                         or: criteria
                        })
                        .set({
                            resource: TokenUtil.resourceIdentifier.COMPANY(toCompanyInfo.id)
                        });
                }catch(e){
                    let deleteQuery = `DELETE FROM user_role_permission
                        WHERE
                            resource IN (${fromCompaniesId.map(a => `'${TokenUtil.resourceIdentifier.COMPANY(a).toString()}'`).join(', ')})
                        AND
                            (user_ref, role, sub_resource) IN (
                            SELECT user_ref, role, sub_resource
                            FROM user_role_permission
                            WHERE resource  = 'company.${toCompanyInfo.id}')`;

                    let docs = await sails.sendNativeQuery(deleteQuery);
                    usersPermission  = await sails.models.userrole.update({
                        or: criteria
                        })
                        .set({
                            resource: TokenUtil.resourceIdentifier.COMPANY(toCompanyInfo.id)
                        });
                }

                 let projectAssetVehiclesOwner = 0;
                 let projectAssetEquipmentOwner = 0;
                 for (let i = 0; i < fromCompaniesId.length; i++) {
                     let where = `${fromCompaniesId[i]} = ANY(tagged_owner)`;

                     let assetVehiclesRawResult = await sails.sendNativeQuery(`UPDATE project_asset_vehicles
                                                                                  SET tagged_owner = array_replace(
                                                                                      tagged_owner,
                                                                                      tagged_owner[array_position(tagged_owner, $1)],
                                                                                      $2) WHERE ${where}`, [fromCompaniesId[i], toCompanyInfo.id]);
                     assetVehiclesRawResult = assetVehiclesRawResult.rowCount;
                     projectAssetVehiclesOwner += assetVehiclesRawResult;

                     let assetEquipmentsRawResult = await sails.sendNativeQuery(`UPDATE project_asset_equipment
                                                                                   SET tagged_owner = array_replace(
                                                                                       tagged_owner,
                                                                                       tagged_owner[array_position(tagged_owner, $1)],
                                                                                       $2) WHERE ${where}`, [fromCompaniesId[i], toCompanyInfo.id]);
                     assetEquipmentsRawResult = assetEquipmentsRawResult.rowCount;
                     projectAssetEquipmentOwner += assetEquipmentsRawResult;
                 }

                let projectAssetTemporaryWorkTaggedOwner = 0 ;
                for (let i = 0; i < fromCompaniesId.length; i++) {
                    let where = `${fromCompaniesId[i]} = ANY(tagged_owner)`;

                    let temporaryWorkTaggedOwner = await sails.sendNativeQuery(`UPDATE project_asset_temporary_work
                                                                                 SET tagged_owner = array_replace(
                                                                                     tagged_owner,
                                                                                     tagged_owner[array_position(tagged_owner, $1)],
                                                                                     $2) WHERE ${where}`, [fromCompaniesId[i], toCompanyInfo.id]);
                    temporaryWorkTaggedOwner = temporaryWorkTaggedOwner.rowCount;
                    projectAssetTemporaryWorkTaggedOwner += temporaryWorkTaggedOwner;
                }

                 let closeCallTaggedOwner = await sails.models.closecall.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                     .set({
                         tagged_owner: toCompanyInfo.id
                     });

                 let goodCallTaggedOwner = await sails.models.goodcall.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                     .set({
                         tagged_owner: toCompanyInfo.id
                     });

                 let visitor = await sails.models.visitor.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'employer_ref')
                 })
                     .set({
                         employer_ref: toCompanyInfo.id
                     });

                 let ibChecklist = await sails.models.inspectionbuilder.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let projectFatigueViolations = await sails.models.projectfatigueviolations.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'user_company_ref')
                 })
                     .set({
                         user_company_ref: toCompanyInfo.id
                     });

                 let companyDivision = await sails.models.companydivision.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'employer_ref')
                 })
                     .set({
                         employer_ref: toCompanyInfo.id
                     });

                 let toolboxTalksTaggedOwner = await sails.models.toolboxtalks.update({
                    or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                    .set({
                        tagged_owner: toCompanyInfo.id
                    });

                 let projectTaskBriefingsTaggedOwner = await sails.models.projecttaskbriefings.update({
                    or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                    .set({
                        tagged_owner: toCompanyInfo.id
                    });
                 let projectWorkpackageplansTaggedOwner = await sails.models.projectworkpackageplans.update({
                    or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                    .set({
                        tagged_owner: toCompanyInfo.id
                    });

                 let projectRamsTaggedOwner = await sails.models.projectrams.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'tagged_owner')
                 })
                     .set({
                         tagged_owner: toCompanyInfo.id
                     });

                 let incidentAlertPreference = await sails.models.incidentalertpreference.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 let projectIncidentReportCompany = await sails.models.projectincidentreport.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'project_company')
                 })
                     .set({
                         project_company: toCompanyInfo.id
                     });

                 let companySettings = await sails.models.companysetting.destroy({
                     company_ref: { in: fromCompaniesId }
                 });

                 let conductCards = await sails.models.conductcard.update({
                     or: prepareCompanyUpdateCriteria(fromCompaniesId, 'company_ref')
                 })
                     .set({
                         company_ref: toCompanyInfo.id
                     });

                 //update tagged_company_ref in checklist of inspection tours
                 sails.log.info(`[mergeCompany] processing tagged references of inspection tours`);
                 let inspections = await inspectionFn.getInspectionTourTaggedToCompany(fromCompaniesId);

                 let updateRequired = false;
                 let inspectionTourIds = [];
                 let inspectionTours = await sails.models.projectinspectiontour.find({
                     select: ['id', 'common_checklist', 'rail_checklist', 'industrial_checklist', 'additional_checklist'],
                     where: {id : inspections}
                 });
                 for(let index in inspectionTours) {
                     let inspectionTour = inspectionTours[index];
                     (inspectionTour.common_checklist || []).forEach(function (item, j) {
                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionTourIds.push(inspectionTour.id);
                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     (inspectionTour.rail_checklist || []).forEach(function (item, j) {
                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionTourIds.push(inspectionTour.id);

                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     (inspectionTour.industrial_checklist || []).forEach(function (item, j) {
                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionTourIds.push(inspectionTour.id);

                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     (inspectionTour.additional_checklist || []).forEach(function (item, j) {
                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionTourIds.push(inspectionTour.id);

                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     if (updateRequired) {
                         sails.log.info(`[mergeCompany] Updating inspection tour: ${inspectionTour.id}`);
                         let updateRequest = {
                             common_checklist: inspectionTour.common_checklist,
                             rail_checklist: inspectionTour.rail_checklist,
                             industrial_checklist: inspectionTour.industrial_checklist,
                             additional_checklist: inspectionTour.additional_checklist
                         };
                         await sails.models.projectinspectiontour.updateOne({id: inspectionTour.id}).set(updateRequest);
                     }
                     updateRequired = false;
                 }


                 //update tagged_company_ref in checklist of inspection builder feature
                 let ibr_ids = await inspectionFn.getIBReportTaggedToCompany(fromCompaniesId);
                 sails.log.info(`[mergeCompany] inspection builder IDs to update:`, ibr_ids);
                 updateRequired = false;
                 let inspectionBuilderReportIds = []
                 let inspectionBuilder = await sails.models.inspectionbuilderreport.find({
                     select: ['id', 'checklist', 'additional_checklist'],
                     where: {id: ibr_ids}
                 });

                 for(let index in inspectionBuilder) {
                     let inspection = inspectionBuilder[index];
                     (inspection.checklist || []).forEach(function (item, j) {
                         if (item.heading && item.subheadings) {
                             (item.subheadings || []).forEach(function (subItem, j) {
                                 if (subItem.tagged_company_ref && subItem.tagged_company_ref.length && subItem.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                                     for(let index in fromCompaniesId) {
                                         let fromCompanyIndex = subItem.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                         if (fromCompanyIndex !== -1) {
                                             subItem.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                             inspectionBuilderReportIds.push(inspection.id);

                                             updateRequired = true;
                                         }
                                     }
                                 }
                             });

                             return false
                         }

                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionBuilderReportIds.push(inspection.id);

                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     (inspection.additional_checklist || []).forEach(function (item, j) {
                         if (item.heading && item.subheadings) {
                             (item.subheadings || []).forEach(function (subItem, j) {
                                 if (subItem.tagged_company_ref && subItem.tagged_company_ref.length && subItem.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                                     for(let index in fromCompaniesId) {
                                         let fromCompanyIndex = subItem.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                         if (fromCompanyIndex !== -1) {
                                             subItem.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                             inspectionBuilderReportIds.push(inspection.id);

                                             updateRequired = true;
                                         }
                                     }
                                 }
                             });

                             return false
                         }

                         if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompaniesId.find(item => item))) {
                             for(let index in fromCompaniesId) {
                                 let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompaniesId[index]);
                                 if (fromCompanyIndex !== -1) {
                                     item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                     inspectionBuilderReportIds.push(inspection.id);

                                     updateRequired = true;
                                 }
                             }
                         }
                     });

                     if (updateRequired) {
                         sails.log.info(`Updating inspection: ${inspection.id}`);
                         let updateRequest = {
                             checklist: inspection.checklist,
                             additional_checklist: inspection.additional_checklist
                         };
                         await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(updateRequest);
                     }
                     updateRequired = false;
                 }

                 let deletedFromCompanies = [];
                 if (req.body.delete_from_companies) {
                     deletedFromCompanies = await sails.models.createemployer.destroy({
                         id: { in: fromCompaniesId }
                     });
                 }

                 let associatedRecords = {
                     companiesProjects: companiesProjects.length,
                     supplyChainProjects: supplyChainProjects.rowCount,
                     supplychainCompanySettings: supplychainCompanySettings.rowCount,
                     companiesAsProjectContractor: companiesAsProjectContractor.length,
                     usersEmployer: usersEmployer.length,
                     usersEmploymentCompany: usersEmploymentCompany.length,
                     badgeEvent: badgeEvent.length,
                     clerkOfWorks: clerkOfWorks.length,
                     clerkOfWorkComments: clerkOfWorkComments.length,
                     closeCalls: closeCalls.length,
                     eLearningModules: eLearningModules.length,
                     goodCalls: goodCalls.length,
                     projectIncidentReports: projectIncidentReports.length,
                     projectInspectionTours: projectInspectionTours.length,
                     projectPowra: projectPowra.length,
                     toolboxtalks: toolboxtalks.length,
                     companyInduction: companyInduction.length || 0,
                     touchByteLogs: touchByteLogs.length,
                     userDailyLogs: userDailyLogs.length,
                     userTimeLogs: userTimeLogs.length,
                     users: users.length,
                     inductionRequestsByEmp: inductionRequestsByEmp,
                     inductionRequestsByEmpCompany: inductionRequestsByEmpCompany,
                     inductionRequestsByUserParentCompany: inductionRequestsByUserParentCompany,
                     inductionRequestsByUserParentCompanyId: inductionRequestsByUserParentCompanyId,
                     inductionRequestsByProjectContractor: inductionRequestsByProjectContractor,
                     usersPermission: usersPermission.length,
                     deletedFromCompanies: deletedFromCompanies.length,
                     projectAssetVehiclesOwner: projectAssetVehiclesOwner,
                     projectAssetEquipmentOwner: projectAssetEquipmentOwner,
                     closeCallTaggedOwner: closeCallTaggedOwner.length,
                     goodCallTaggedOwner: goodCallTaggedOwner.length,
                     visitor: visitor.length,
                     ibChecklist: ibChecklist.length,
                     projectFatigueViolations: projectFatigueViolations.length,
                     companyDivision: companyDivision.length,
                     inspectionTourForTaggedCompanyRef: inspectionTourIds.length,
                     inspectionBuilderForTaggedCompanyRef: inspectionBuilderReportIds.length,
                     toolboxTalksTaggedOwner: toolboxTalksTaggedOwner.length,
                     projectTaskBriefingsTaggedOwner: projectTaskBriefingsTaggedOwner.length,
                     projectWorkpackageplansTaggedOwner: projectWorkpackageplansTaggedOwner.length,
                     projectRamsTaggedOwner: projectRamsTaggedOwner,
                     incidentAlertPreference: incidentAlertPreference.length,
                     projectIncidentReportCompany: projectIncidentReportCompany.length,
                     companySettings: companySettings.length,
                     conductCards: conductCards.length,
                     projectAssetTemporaryWorkTaggedOwner:projectAssetTemporaryWorkTaggedOwner
                 };

                 sails.log.info('Responding with count, merged', associatedRecords);

                 return ResponseService.successResponse(res, {associatedRecords});
             }
         }

         sails.log.info("Invalid request.");
         return ResponseService.errorResponse(res, "Invalid request.");
     },

     createOrUpdateCompanyDivisions: async(req, res) => {
         let divisions = req.body.divisions || [];
         for(const division of divisions) {
             if(division.id) {
                 await sails.models.companydivision.updateOne({id: division.id}).set(division)
             } else {
                 await sails.models.companydivision.create(division);
             }
         }
         return ResponseService.successResponse(res, {});
     },

    importSupplyChainCompanies: async (req, res) => {
        let companyId = +req.param('companyId');
        let countryCode = req.param('countryCode', 'GB');
        let companies = req.param('companies');

        sails.log.info(`Processing company supplychain import for company ${companyId}, countryCode ${countryCode}, by user ${req.user.id}`);

        let xlsxCompaniesSet = new Set();
        let companiesList = companies.map(company => {
            xlsxCompaniesSet.add((company['Company Name'] || '').toLowerCase().trim());
            return (company['Company Name'] || '').toString().trim();
        });

        let employerlist = await sails.models.createemployer_reader.find({
            select: ['id', 'name'],
            where: {country_code: countryCode},
        });

        /**
         * Using Sets for Faster Lookup: Replace the array companiesList with a Set for faster lookup when checking
         * for inclusion. Sets offer constant time complexity O(1) for operations like has, making them more efficient
         * for large datasets.
         *
         * Indexing for Faster Search: Create an index for the totalDBCompaniesList array to avoid linear search operations
         * when checking for matches. This can significantly improve performance for large arrays.
         */

        // Index totalDBCompaniesList by name
        let totalDBCompaniesIndex = new Map();

        totalDBCompaniesList = employerlist.map(item => {
            let companyName = item.name.toLowerCase().trim();
            // Add the name and item to the index.
            totalDBCompaniesIndex.set(companyName, item);
            return { id: item.id, name: companyName };
        });

        // using Set lookup
        let matchedCompanies = totalDBCompaniesList.filter(company => xlsxCompaniesSet.has(company.name));
        // using Map lookup
        let nonMatchedCompanies = companiesList.filter(company => !totalDBCompaniesIndex.has(company.toLowerCase()));

        sails.log.info(`Creating ${nonMatchedCompanies.length} employers to be added in supply chain for company ${companyId}`, nonMatchedCompanies);

        let companiesToCreate = nonMatchedCompanies.map(name => ({name,  country_code: countryCode}));
        let employer_record = await sails.models.createemployer.createEach(companiesToCreate);

        let savedSupplyChainConfig  = await sails.models.companysetting_reader.findOne({
            where: {
                company_ref: companyId,
                name: 'company_supply_chain_config',
            }
        });

        let needToPopulateProjects = false;
        let finalIdsTobeSaved = [...matchedCompanies, ...employer_record].map(c => c.id);

        if(savedSupplyChainConfig && savedSupplyChainConfig.id){
            let scConfig = savedSupplyChainConfig.value;
            sails.log.info(`Found existing supply chain config for company ${companyId}`, scConfig);

            finalIdsTobeSaved = _.uniq([...scConfig.supply_chain, ...finalIdsTobeSaved]);
            scConfig.supply_chain = finalIdsTobeSaved;
            needToPopulateProjects = scConfig.pre_select || false;

            sails.log.info(`Updating supply chain config for company ${companyId}, needToPopulateProjects ${needToPopulateProjects}, finalIdsTobeSaved`, finalIdsTobeSaved);

            let updatedSetting = await sails.models.companysetting.updateOne({
                id: savedSupplyChainConfig.id
            }).set({ value: scConfig });

            needToPopulateProjects && updateCompanyProjectsSupplyChain(companyId, finalIdsTobeSaved);

            return ResponseService.successResponse(res, {company_setting: updatedSetting});
        } else {
            sails.log.info(`Creating supply chain config for company ${companyId}, finalIdsTobeSaved`, finalIdsTobeSaved);
            let data = {
                company_ref: companyId,
                name: 'company_supply_chain_config',
                value: {
                    pre_select: false,
                    supply_chain: finalIdsTobeSaved,
                    allowed_to_edit: false,
                    active_for_all_projects: false
                }
            };
            let updatedSetting = await sails.models.companysetting.create(data);

            return ResponseService.successResponse(res, {company_setting: updatedSetting});
        }
    },

     deleteCompanyDivision: async(req, res) => {
         let divisionId = req.param('divisionId');
         sails.log.info('request to delete company division for id: ', divisionId);
         let companydivision = await sails.models.companydivision.destroy({id: divisionId});
         return ResponseService.successResponse(res, {companydivision: companydivision});
     },

     getCompanyTimezone: async (req, res) => {
        let employerId = req.param("employerId");
        let timezone = await sails.models.companysetting.findOne({ where:{company_ref: employerId, name: 'timezone' }, select:['value'] });
        return ResponseService.successResponse(res, { timezone });
     },

     companiesList: companiesList,
     companiesListV3:companiesListV3,
     companiesSearchesList: companiesSearchesList,
     companiesListForInnTimeApp: companiesList,
     supplyChainCompaniesList: async (req, res) => {
         let projectId = +req.param('projectId', 0);
         let pageSize = +req.param('pageSize', 50);
         let pageNumber = +req.param('pageNumber', 0);
         let sortKey = req.param('sortKey', 'name');
         let sortDir = req.param('sortDir', 'asc');
         let paramCountryCode = req.param('country_code', 'GB');
         let searchTerm = req.param('q', '').toString().trim();

         let resObj = {
             records:[],
             q: searchTerm,
             projectId,
             pageSize,
             pageNumber,
             sortKey,
             sortDir,
             paramCountryCode,
             totalCount:0,
         };

         let project = await sails.models.project_reader.findOne({ where: { id: projectId }, select: ['custom_field']});
         if(!project){
            return ResponseService.sendResponse(res, ResponseService.errorObject(`Project not found.`, resObj));
         }

         let {
             custom_field: {country_code, supply_chain_companies, has_supply_chain_companies}
         } = project;

         if(country_code !== paramCountryCode) {
            return ResponseService.successResponse(res, resObj);
         }

         if(!has_supply_chain_companies || supply_chain_companies.length === 0) {
            return ResponseService.sendResponse(res, ResponseService.errorObject(`Supply chain companies feature disabled.`, resObj));
         }

         let { total: totalCount, records } = await companyFn.getSupplyChainListByIDs(
             supply_chain_companies,
             pageSize,
             pageSize * pageNumber,
             sortKey,
             sortDir,
             { searchTerm, countryCode: country_code }
         );

         records = await attachCompanyDomains(records, req.user.email);
         return ResponseService.successResponse(res, {
             ...resObj,
             records,
             totalCount,
         });
     },
     getCompanyProjects:async(req, res) => {
        let employerId = req.param('employerId', 0);
        const is_active = req.param('is_active', 'true') === 'true';
        let projects =  await companyFn.getCompanyAccessibleProjects(employerId, ['name'], is_active);
        return  ResponseService.successResponse(res,{ projects });
     },
 };
