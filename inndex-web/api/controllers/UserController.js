/**
 * UserController
 *
 * @description :: Server-side actions for handling incoming requests.
 * @help        :: See https://sailsjs.com/docs/concepts/actions
 */
const { fetchUrlAs, decodeURIParam } = require('./../services/HttpService');
const HttpService = require('./../services/HttpService');
const { defaultUserPassword, getShadowUserEmail, displayDateFormat_DD_MM_YYYY_HH_mm_ss } = sails.config.constants;
const bcrypt = require("bcryptjs");
const _uniq = require("lodash/uniq");
const { authenticator } = require('otplib');
const {
    TokenUtil: {
        UPDATE_YOUR_APP_TO_LATEST,
        ROLES,
        isCallerDiscontinued,
        getCompanyInfo,
        removeUAC,
        storeUACRecordsOrError,
        getAllowedDefaultProjectsOfUser,
        allResourceAdminsWithOneOfDesignations,
        hasOnlySiteManagementAccess,
    },
    AccessLogService,
    UserRevisionService: { createUserRevision },
    DataProcessingService: {
        translateUserDocIntoPages,
        attachUserDocument,
        populateDocumentChildren,
        getDailyTimeEventV2,
        getUserCompanyTimeEvents,
        attachProfilePicWithUsersInfo,
        populateUserRefs,
        updateUserInductionsWithProfileChanges,
        getUpdatedOnboardStatus,
    },
    ResponseService: {
        errorResponse,
        successResponse
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendPushNotification
    },
    SharedService: {
        downloadPdfViaGenerator
    },
    EncryptUtil: {
        decryptSymmetric
    }
} = require('./../services');
const {
    UserInfoValidator: {
        saveUserSetting,
        addDocument,
        signedDocument,
        userEmploymentValidate,
        userPersonalDetailValidate,
    }
} = require('./../validators');
const {
    UsersFn: {
        getAllUsersFn
    }
} = require('../sql.fn');
const SharedService = require('./../services/SharedService');
const TokenUtil = require('./../services/TokenUtil');
const EMAIL_REGEX = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/;
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const USER_ALREADY_EXISTS = 'User with given details already exists';
const USER_DOES_NOT_EXISTS = 'User details do not exist';
const EMAIL_VERIFICATION_PENDING = 'Kindly verify your email address first.';
const INVALID_REQUEST = 'Invalid request';
const fs = require('fs');
const path = require('path');
let meta_file_path = path.join(process.cwd(), sails.config.constants.COUNTRIES_META_FILE);
let { countries } = JSON.parse(fs.readFileSync(meta_file_path, 'utf-8'));
const moment = require('moment');
const PDFMerger = require('pdf-merger-js');
const { expandUserDocFiles, getUserFullName, constructDistanceMatrix, processVisitingRecords } = require('./../services/DataProcessingService');
const momentTz = require('moment-timezone');
const dayjs = require('dayjs');

const doesUsernameExist = async (email) => {
    let user = await sails.models.user.findOne({where: {email}, select: ['id']});
    return !!user;
};

const getRolesForAllUsers = async (users = []) => {
    let usersIds = users.map((user) => user.id);
    let user_roles = await  sails.models.userrole_reader.find({...(usersIds.length && {user_ref: usersIds})});
    let user_roles_by_ref = user_roles.reduce((list, user_role) => {
        if (!user_role.user_ref) {
            return list;
        }
        if (!list[user_role.user_ref]) {
            list[user_role.user_ref] = [];
        }
        list[user_role.user_ref].push(user_role);
        return list;
    }, {});
    return user_roles_by_ref;
}

const sendSupportRequestCreateAlert = async (support_ticket) => {
    let sendToIds = [];
    sails.log.info('Sending Create alert for support-request', support_ticket.id);
    try {
        let sendToUsers = (sails.config.custom.CONTACT_MAIL_ADDRESS || '').split(',');
        let len = (sendToUsers || []).length;
        sails.log.info('Total alerts to send', len);

        let subject = `Support Request Received - #${support_ticket.id}`;
        let html = await sails.renderView('pages/mail/support-ticket-created', {
            title: subject,
            support_ticket,
            layout: false
        });
        for (let i = 0; i < len; i++) {
            let toUser = sendToUsers[i];

            sails.log.info('Sending mail to', toUser);
            try {
                let sentStatus = await EmailService.sendMail(subject, [toUser], html);
            } catch (failure) {
                sails.log.info('Failed to send mail', failure);
            }

        }
    } catch (e) {
        sails.log.info('Failed to send all alert ?', e);
    }
};

const sendVerifyEmailNotification = async (user = {}) => {
    sails.log.info('Sending Verify Email Alert to:', user.id);
    try {
        let subject = `Welcome to innDex! Confirm your email address`;
        let html = await sails.renderView('pages/mail/verify-email', {
            title: subject,
            user,
            layout: false
        });
        let sentStatus = await EmailService.sendMail(subject, [user.email], html);
        return sentStatus;
    } catch (e) {
        sails.log.info('Failed to send verification mail', e);
        return false;
    }
};

const sendNewUserNotificationEmailToCompany = async (employerName, user) => {
    let employer = await sails.models.createemployer.findOne({ name: employerName, country_code: user.country_code });
    if (employer && employer.has_company_portal) {
        sails.log.info(`Sending new user notification to Company managers, "${employerName}"`);
        let companyNominatedManagers = await allResourceAdminsWithOneOfDesignations(employer.id, ['nominated'], true, ROLES.COMPANY_ADMIN);
        sails.log.info('total company nominated managers, count:', companyNominatedManagers.length);

        let subject = "New Employee Added";
        let userName = user.first_name + ' ' + user.last_name;
        let medical_assessments_res = await sails.models.usermedicalassessmentans.find({ user_ref: user.id });
        let medical_assessment = [];
        let health_assessment = [];
        for (let i = 0; i < medical_assessments_res.length; i++) {
            if (medical_assessments_res[i].answer == '1') {
                console.log(medical_assessments_res[i].question, " : ", medical_assessments_res[i].answer)
                medical_assessment.push({
                    'question': medical_assessments_res[i].question,
                    'answer': 'Yes'
                })
            }
        }
        let health_assessments_res = await sails.models.userhealthassessmentanswer_reader.find({ user_ref: user.id });
        health_assessments_res = await populateQuestionRef(health_assessments_res, 'question_ref');

        for (let k = 0; k < health_assessments_res.length; k++) {
            if (health_assessments_res[k].answer == '1') {
                health_assessment.push({
                    'question': health_assessments_res[k].question_ref.question,
                    'answer': 'Yes'
                })
            }
        }
        let showHealthAndMedicalAssesment = false;
        if (medical_assessment.length > 0 || health_assessment.length > 0) {
            showHealthAndMedicalAssesment = true;
        }
        for (let j = 0, len = companyNominatedManagers.length; j < len; j++) {
            let nomManager = companyNominatedManagers[j].user_ref || {};
            try {
                let manager_name = nomManager.name;
                let emailHtml = await sails.renderView('pages/mail/user-added-employer', {
                    title: subject,
                    manager_name,
                    userName,
                    showHealthAndMedicalAssesment,
                    medical_assessment,
                    health_assessment,
                    layout: false
                });
                sails.log.info('Sending mail to', nomManager.email);
                await EmailService.sendMail(subject, [nomManager.email], emailHtml);
                sails.log.info('User added employer notification email has been sent');
            } catch (e) {
                sails.log.info('Unable to send User added employer notification email to nom manager.', e);
                return false;
            }
        }
    } else {
        sails.log.info(`Company portal not enabled for "${employerName}", id `, (employer && employer.id));
    }

};

const sendAcntDeactReqNotifAdmin = async (user = {}) => {
    sails.log.info('Sending account deactivate notification.');
    let sendToUsers = (sails.config.custom.SUPPORT_MAIL_ADDRESSES || '').split(',');
    let len = (sendToUsers || []).length;
    sails.log.info('Total alerts to send', len);
    let subject = `New account deactivate request!`;
    let html = await sails.renderView('pages/mail/account-decativate-request-notf-admin', {
        title: subject,
        user,
        layout: false
    });

    for (let i = 0; i < len; i++) {
        let toUser = sendToUsers[i];

        sails.log.info('Sending account deactivate notification to', toUser);
        try {
            await EmailService.sendMail(subject, [toUser], html);
        } catch (failure) {
            sails.log.info('Failed to send mail', failure);
        }
    }
};

const ResponseService = require('./../services/ResponseService');

/*
const getCountryCodeOf = (name) => {
    return ((countries || []).find(c => c.name === name) || {}).code;
};


const saveUserCountryCode = async (req, country_of_work) => {
    // update user with new country code
    if(!country_of_work){
        // figure out country code and update user if needed.
        let platform = req.headers['platform'] || ((req.headers['accept-language'] && (req.headers['accept-language']).indexOf('mobile') !== -1) ? req.headers['accept-language'] : '-');
        sails.log.warn('country code not supplied, platform:', platform);
    }
    if(country_of_work && req.user.country_of_work !== country_of_work){
        // update user
        sails.log.info('update country code with', country_of_work);
        let user = await sails.models.user.updateOne({ id: req.user.id }).set({ country_of_work: country_of_work });
        createUserRevision(req.user.id, {personal: user}).catch(sails.log.error);
        return true;
    }
    return false;
};
 */

const saveContactDetail = async (user_ref, body) => {
    sails.log.info('save contact details of user:', user_ref);
    let contactDetail = _.pick((body || {}), [
        'house_no',
        'street',
        'city',
        'post_code',
        'country',
        'home_no',
        'home_number',
        'mobile_no',
        'mobile_number',
        'emergency_contact',
        'emergency_contact_no',
        'emergency_contact_number'
    ]);

    let contact_detail = await sails.models.contactdetail.findOne({ user_ref });
    sails.log.info('existing contact-detail id', (contact_detail && contact_detail.id));

    // ----- backward compatibility logic -----
    if (contactDetail.home_no) {
        contactDetail.home_number = contact_detail ? { ...contact_detail.home_number, "number": contactDetail.home_no } : { "code": null, "number": contactDetail.home_no }
    } else if (contactDetail.home_no == "") {
        contactDetail.home_number = { "code": null, "number": contactDetail.home_no }
    } else if (contactDetail.home_number) {
        contactDetail.home_no = contactDetail.home_number.number
    }

    if (contactDetail.mobile_no) {
        contactDetail.mobile_number = contact_detail ? { ...contact_detail.mobile_number, "number": contactDetail.mobile_no } : { "code": null, "number": contactDetail.mobile_no }
    } else if (contactDetail.mobile_no == "") {
        contactDetail.mobile_number = { "code": null, "number": contactDetail.mobile_no }
    } else if (contactDetail.mobile_number) {
        contactDetail.mobile_no = contactDetail.mobile_number.number
    }

    if (contactDetail.emergency_contact_no) {
        contactDetail.emergency_contact_number = contact_detail ? { ...contact_detail.emergency_contact_number, "number": contactDetail.emergency_contact_no } : { "code": null, "number": contactDetail.emergency_contact_no }
    } else if (contactDetail.emergency_contact_no == "") {
        contactDetail.emergency_contact_number = { "code": null, "number": contactDetail.emergency_contact_no }
    } else if (contactDetail.emergency_contact_number) {
        contactDetail.emergency_contact_no = contactDetail.emergency_contact_number.number
    }
    // ----- end backward compatibility -----

    if (contact_detail && contact_detail.id) {
        contact_detail = await sails.models.contactdetail.updateOne({
            id: contact_detail.id
        }).set(contactDetail);
    } else {
        // Associate user while creating
        contactDetail.user_ref = user_ref;
        contact_detail = await sails.models.contactdetail.create(contactDetail);
    }
    sails.log.info('saved contact-detail successfully', contact_detail.id);
    // createUserRevision(user_ref, {address: contact_detail}).catch(sails.log.error);
    return contact_detail;
};

const getUserContactDetail = async (userId, reqFrom = '') => {
    sails.log.info('get contact details of', userId);
    let contact_detail = await sails.models.contactdetail_reader.findOne({ user_ref: userId });

    //attach district with contact details
    if (reqFrom === 'company' && contact_detail && contact_detail.country === 'United Kingdom') {
        //remove last 3 digit from postcode to form area code
        let areaCode = (contact_detail.post_code || '').substring(0, contact_detail.post_code.length - 3).trim();
        sails.log.info(`Area code is ${areaCode} for postcode ${contact_detail.post_code}.`, areaCode.length);
        let meta_districts = await sails.models.metadistrict_reader.find({
            select: ['district_name', 'area_codes'],
        }).sort([{ id: 'ASC' }]);

        let matchedDistrict = meta_districts.find(item => item.area_codes.includes(areaCode));
        let district_name = (matchedDistrict && matchedDistrict.district_name) || null;

        sails.log.info(`district for postcode ${contact_detail.post_code} is ${district_name}.`);
        contact_detail.district_name = district_name;
    };

    sails.log.info('get contact details query successful', contact_detail ? contact_detail.id : null);
    return contact_detail;
};

const saveUserHealthAssessmentAnswers = async (user_ref, body) => {
    sails.log.info('create / update User Health Assessment Answers', user_ref);

    let batchRecords = (body.health_assessments || []).map(row => {
        return {
            user_ref: user_ref,
            question_ref: _.isObject(row.question_ref) && row.question_ref.id ? row.question_ref.id : row.question_ref,
            answer: row.answer,
        }
    });
    sails.log.info('health_assessments_answers', batchRecords.length);
    let answers = await sails.models.userhealthassessmentanswer.destroy({ user_ref });
    sails.log.info('Deleted count', answers ? answers.length : null);

    let health_assessments = await sails.models.userhealthassessmentanswer.createEach(batchRecords);
    sails.log.info('inserted successfully', health_assessments ? health_assessments.length : null);
    return health_assessments;
};

const populateQuestionRef = async (records, key = 'question_ref') => {
    if (!records.length) {
        return records;
    }
    let ids = [...new Set(records.map(r => r[key]))];
    sails.log.info(`expanding ${key}: (${ids})`);
    let questions_list = await sails.models.userhealthassessmentquestion_reader.find({
        select: ['id', 'question', 'category', 'type', 'order', 'is_active'],
        where: {id: ids}
    });

    records = records.map(r => {
        r[key] = questions_list.find(j => j.id === r[key]) || null;
        return r;
    });
    sails.log.info(`expanded total ${questions_list.length} question_ref refs for ${records.length} records`);
    return records;
};

const getUserHealthAssessment = async (userId, expand) => {
    sails.log.info('get Health Assessment details of ', userId, 'expand?', expand);
    let health_assessments = await sails.models.userhealthassessmentanswer_reader.find({ user_ref: userId });
    if(expand === 'true'){
        health_assessments = await populateQuestionRef(health_assessments, 'question_ref');
    }
    sails.log.info('get Health Assessment query successful', health_assessments ? health_assessments.length : null);
    return health_assessments;
};

const saveUserMedicalAssessmentAnswers = async (user_ref, body) => {
    sails.log.info('create / update User Medical Assessment Answers', user_ref);

    let batchRecords = (body.medical_assessments || []).map(row => {
        return {
            user_ref: user_ref,
            question_id: row.question_id,
            question: row.question,
            ans_details: row.ans_details,
            answer: row.answer,
        }
    });
    sails.log.info('medical_assessments_answers', batchRecords.length);
    let answers = await sails.models.usermedicalassessmentans.destroy({ user_ref });
    sails.log.info('Deleted count', answers ? answers.length : null);

    let medical_assessments = await sails.models.usermedicalassessmentans.createEach(batchRecords);
    sails.log.info('inserted successfully', medical_assessments ? medical_assessments.length : null);
    return medical_assessments;
};

const getUserMedicalAssessment = async (userId) => {
    sails.log.info('get Medical Assessment details of ', userId);
    let medical_assessments = await sails.models.usermedicalassessmentans.find({ user_ref: userId });
    sails.log.info('get Medical Assessment query successful', medical_assessments ? medical_assessments.length : null);
    return medical_assessments;
};

const saveUserEmploymentDetail = async (user_ref, body, user) => {
    sails.log.info('create / update user employment details ', user_ref);
    let userEmpDetail = _.pick((body || {}), [
        'employer',
        'reporting_to',
        'job_role',
        'type_of_employment',
        'employment_company',
        'operative_type',
        'employee_number',
        // @deprecated since 23rd Feb
        'months_with_employer',
        'start_date_with_employer',
        'earn_mlw_e783',
        // 'working_arrangement',
        // 'working_pattern',
        // 'bank_name',
        // 'account_number',
        // 'sort_code',
        // 'utr_number'
    ]);

    // if (!userEmpDetail.account_number) {
    //     userEmpDetail.account_number = null;
    // }
    //
    // if (!userEmpDetail.sort_code) {
    //     userEmpDetail.sort_code = null;
    // }
    //
    // if (!userEmpDetail.utr_number) {
    //     userEmpDetail.utr_number = null;
    // }
    if (!['Agency'].includes(userEmpDetail.type_of_employment)) {
        userEmpDetail.employment_company = null;
    }

    // Associate user while creating
    userEmpDetail.user_ref = user_ref;
    let employment_detail = await sails.models.userempdetail.findOne({
        user_ref: user_ref
    });
    if (employment_detail) {
        // found existing, update it
        // update found one
        sails.log.info('updating existing record', employment_detail.id);
        let updated_employment_detail = await sails.models.userempdetail.updateOne({
            id: employment_detail.id,
            user_ref: user_ref
        }).set(userEmpDetail);

        if (employment_detail.employer !== userEmpDetail.employer) {
            sendNewUserNotificationEmailToCompany(userEmpDetail.employer, user).catch(sails.log.error);
        }
        sails.log.info('updated successfully', updated_employment_detail ? updated_employment_detail.id : null);
        // createUserRevision(user_ref, {employment: updated_employment_detail}).catch(sails.log.error);
        return updated_employment_detail;
    } else {
        // nothing found, create one
        sails.log.info('nothing found create new record');
        let created_employment_detail = await sails.models.userempdetail.create(userEmpDetail);
        sails.log.info('created successfully' , created_employment_detail ? created_employment_detail.id : null);
        sendNewUserNotificationEmailToCompany(userEmpDetail.employer, user).catch(sails.log.error);
        // createUserRevision(user_ref, {employment: created_employment_detail}).catch(sails.log.error);
        return created_employment_detail;
    }
};

const processToDownloadEmployeeInfo = async (req, res, whereClause, employeeId, companyId, type) => {
    //sails.log.info(`processToDownloadEmployeeInfo whereClause: ${whereClause}`,  `employeeId: ${employeeId}`);
    let user = await sails.models.user.findOne(whereClause);
    if (user && user.id) {
        if (user.profile_pic_ref) {
            user.profile_pic_ref = await sails.models.userfile.findOne({ id: user.profile_pic_ref });
        }
        let contact_details = await sails.models.contactdetail.findOne({ user_ref: employeeId })
        let employment_details = await sails.models.userempdetail.findOne({ user_ref: employeeId })
        let health_assessment = await sails.models.userhealthassessmentanswer_reader.find({ user_ref: employeeId });
        health_assessment = await populateQuestionRef(health_assessment, 'question_ref');
        let assessment_questions = [];
        // extracting question reference out of answers, to save DB call
        health_assessment = (health_assessment || []).map(record => {
            if (record.question_ref && record.question_ref.id) {
                assessment_questions.push(record.question_ref);
                record.question_ref = record.question_ref.id;
            }
            return record;
        });

        sails.log.info('got user health_assessment_questions, total', assessment_questions.length);
        let assessment_categories = _.groupBy(assessment_questions || [], (i) => i.category);

        let medical_assessment = await sails.models.usermedicalassessmentans.find({ user_ref: employeeId });

        //fetching user documents
        let filter = {
            doc_owner_id: employeeId,
            parent_doc_ref: null, // get only parent docs
            is_deleted_manually: { '!=': 1 }
        };

        sails.log.info('get User Docs for filter', filter);

        let totalPages = 2;
        let competencies = await sails.models.userdoc.find(filter);
        competencies = await populateDocumentChildren(competencies, { is_deleted_manually: { '!=': 1 } });
        if (competencies && competencies.length) {
            competencies = await translateUserDocIntoPages(competencies);
            sails.log.info('fetched document pages, total records', competencies.length);
        } else {
            totalPages = 1;
        }

        sails.log.info('got record, id', user ? user.id : undefined);
        let project_logo_file = await TokenUtil.getProjectLogo('parent_company', companyId);
        let fileName = moment().format('DD-MM-YYYY') + '-Employee-Details-' + encodeURI(user.first_name) + ' ' + encodeURI(user.last_name);
        let form_template = `pages/company-employee`;
        let date_line = `Date: ${momentTz(+user.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss)}`;
        let html = await sails.renderView(form_template, {
            title: `Employee Details`,
            user: user || {},
            contact_detail: contact_details || {},
            employment_detail: employment_details || {},
            project_logo_file,
            // user_docs: competencies,
            document_pages: competencies,
            health_assessment_answers: health_assessment,
            medical_assessments_answers: medical_assessment || [],
            assessment_categories,
            categories_names: Object.keys(assessment_categories),
            moment: moment,
            momentTz,
            timezone: 'UTC',
            dateFormat: 'DD/MM/YYYY',
            dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',
            type: 'pdf',
            numberToYesNo: (num) => {
                if (+num === 1) {
                    return 'Yes';
                } else if (+num === 0) {
                    return 'No';
                }
                else if (num === null) {
                    return '-';
                }
                return num;
            },
            createEmploymentTime: (epoch_ms) => {
                if (epoch_ms && !isNaN(+epoch_ms)) {
                    let m = moment(+epoch_ms);
                    let d = moment.duration(moment().diff(m));
                    return moment.isDuration(d) ? (
                        (d.years() ? d.years() + ' year' : '')
                        + ' '
                        + (d.months() ? d.months() + ' month' : '')
                        + ' '
                        + (!d.years() ? (d.days() ? d.days() + ' day' : '') : '')
                    ) : null;
                }
                return epoch_ms;
            },
            findIdInObj: (list, id, indentifier = 'id') => {
                let o = list.find(o => o[indentifier] === id);
                return o ? o : {};
            },
            sortCodeMask: (number) => {
                if (number) {
                    let str = number.toString();
                    let numberArr = str.match(/.{1,2}/g);
                    return numberArr.join('-');
                }
            },
            displayEmploymentCompany: (ed) => {
                return (ed && ed.employment_company) ? `(${ed.employment_company})` : '';
            },
            totalPages,
            layout: false
        });

        if (type === 'html') {
            sails.log.info('Rendering html view');
            return res.send(html);
        }

        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'employee-info',
            file_name: fileName,
            heading_line: 'Employee Details',
            project_line: '',
            date_line,
            logo_file: project_logo_file,
            responseType:'url'
        });
    }
    sails.log.info('Failed to find user');
    return ResponseService.errorResponse(res, sails.__('internal server error'));
};

const sendNotificationForDocumentSign = async (record, req) => {
    const receiverUser = await sails.models.user_reader.findOne({
        where: { id: record.employee_ref },
        select: ['id', 'first_name', 'last_name', 'email']
    });
    const message = `${getUserFullName(req.user, true)} has invited you to review and sign the document ${record.document_title}`
    const messageTitle = `${record.document_title} review required`
    const category = NOTIFICATION_CATEGORY.DOCUMENT_SIGN;
    const notificationData = {
        category,
        document_id: record.id,
    }
    let firebaseMsgData = {
        category: category,
        document_id: record.id.toString(),
    };
    try {
        await sendPushNotification({
            message,
            messageTitle,
            recipientUserInfo: receiverUser,
            submittedByUserInfo: req.user,
            category,
            notificationData,
            firebaseMsgData
        });
    } catch (error) {
        sails.log.info("Push notification not send: ", error);
    }
};

const cleanUpUserSavePayload = (user) => {
    if (user.first_name) {
        user.first_name = (user.first_name).toString().trim();
    }
    if (user.middle_name) {
        user.middle_name = (user.middle_name).toString().trim();
    }
    if (user.last_name) {
        user.last_name = (user.last_name).toString().trim();
    }
    if (user.profile_pic_ref && user.profile_pic_ref.id) {
        user.profile_pic_ref = user.profile_pic_ref.id;
    }
    if (user.parent_company && user.parent_company.id) {
        user.parent_company = user.parent_company.id;
    }
    return user;
};

const typeCorrectionIntoEmail = (email) => {
    // Replace .con with .com
    // Replace gail.com with gmail.com
    // Replace icluod with icloud
    // Replace icoud with icloud
    // Replace .comp with .com
    return email.replace(/\.con$/i, '.com')
        .replace(/gail\.com$/i, 'gmail.com')
        .replace(/icluod\.com$/i, 'icloud.com')
        .replace(/icoud\.com$/i, 'icloud.com')
        .replace(/\.comp$/i, '.com');
};
const validateBkupCode = (code, backup_codes = []) => {
    let has_valid_code = false;
    const code_index = backup_codes.findIndex(c => !c.used && bcrypt.compareSync(code, c.code));
    if (code_index !== -1) {
        backup_codes = backup_codes.filter((code, index) => index !== code_index);
        has_valid_code = true;
    }
    return {has_valid_code, updated_backup_codes: backup_codes};
};

module.exports = {

    signUp: async (req, res) => {
        /**
         * this is param checking if they are provided
         */
        if (!_.has(req.body, 'email') || !_.has(req.body, 'password')) {
            return ResponseService.errorResponse(res, "Invalid request");
        }
        let email = req.body.email.toString().toLowerCase();
        if (!email.match(EMAIL_REGEX)) {
            sails.log.info('Invalid email address provided', req.body.email);
            return ResponseService.errorResponse(res, "Invalid email address");
        }
        const domain = email.split('@')[1]

        const ssoConfiguration = await sails.models.companyssoconfiguration_reader.findOne({
            where: {
                domain: domain,
                enabled: true,
            },
        });

        if (ssoConfiguration && ssoConfiguration.providers) {
            sails.log.info('SSO is enabled', ssoConfiguration);
            return ResponseService.errorResponse(res, `SSO configuration is enabled with ${domain}. Please check SSO provider`, { sso_enabled: true });
        }

        let name = [];
        if (req.body.name) {
            name = (req.body.name || '').split(' ');
        }
        let user_onboard_status = (req.body.user_onboard_status ? req.body.user_onboard_status : getUpdatedOnboardStatus({}, 'competencies'));
        if (user_onboard_status && !user_onboard_status.competencies) {
            user_onboard_status.competencies = true;
        }
        let param = {
            email: typeCorrectionIntoEmail(email),
            password: req.body.password,
            first_name: (name.length ? name[0] : req.body.first_name).toString().trim(),
            //timezone: req.body.timezone,
            last_name: (name.length ? (name[1] ? name[1] : '') : req.body.last_name).toString().trim(),
            user_onboard_status: user_onboard_status,
            terms_accepted: req.body.terms_accepted || true,
            offers_subscribed: req.body.offers_subscribed || false,
        };

        let exists = await doesUsernameExist(param.email);
        if (exists) {
            sails.log.info(`user with given email: ${param.email} already exists`);
            return ResponseService.errorResponse(res, USER_ALREADY_EXISTS, exists);
        }

        let user = await sails.models.user.create(param);
        sails.log.info('Created new user for', user.email, 'id', user.id);
        sendVerifyEmailNotification(user).catch(sails.log.error);
        let platform = req.headers['platform'] || ('-');
        AccessLogService.logRequest({
            path: `${req.method}: ${req.path}`,
            action: 'user/signup',
            user_agent: req.headers['user-agent'],
            platform: platform,
            ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
            user_ref: user.id
        }).catch(sails.log.error);
        return ResponseService.successResponse(res, {
            email_pending: true,
            message: 'User Registration completed'
        });
    },

    status: async (req, res) => {
        let user = await TokenUtil.extendUserPayload(req.user);
        res.json({
            success: true,
            current: user,
            now: (new Date()),
        })
        //sails.models.user.find().then((users) => {        });

    },
    /**
     * this is used to authenticate user to our api using either email and password
     * POST /login
     * @param req
     * @param res
     */
    login: async function (req, res) {

        /**
         * this is param checking if they are provided
         */
        if (!_.has(req.body, 'email') || !_.has(req.body, 'password')) {
            return res.ok({ error: true, message: "No field should be empty." });
        }
        let platform = req.headers['platform'] || ((req.headers['accept-language'] && (req.headers['accept-language']).indexOf('mobile') !== -1) ? req.headers['accept-language'] : '-');
        if (isCallerDiscontinued(platform, true)) {
            sails.log.info('Discontinued caller, Blocking user from login', req.body.email);
            return ResponseService.errorResponse(res, UPDATE_YOUR_APP_TO_LATEST);
        }
        const data = await sails.models.user.findOne({
            email_to_change: req.body.email.toString().toLowerCase(),
        })

        if (data) {
            return ResponseService.errorResponse(res, "Email is not verified! Try to login with old email");
        }
        /**
         * check if the username matches any email or phoneNumber
         */

        let user = await sails.models.user.findOne({
            email: req.body.email.toString().toLowerCase(),
            is_active: 1,
        }).populate('profile_pic_ref').populate('parent_company').populate('user_roles');

        if (!user) {
            return ResponseService.errorResponse(res, USER_DOES_NOT_EXISTS);
        }

        if (!user.email_verified_on && !user.email_to_change) {
            sails.log.info('User email verification pending');
            return ResponseService.errorResponse(res, EMAIL_VERIFICATION_PENDING, { email_pending: true });
        }

        const validPassword = await bcrypt.compare(req.body.password, user.password);
        if (!validPassword) {
            return ResponseService.errorResponse(res, "Invalid credentials.");
        }

        let code = (req.body.code || '').toString();
        let validate_mfa = req.body.validate_mfa || false;
        let validate_bkup = req.body.validate_bkup || false;
        let is_mobile_platform = ((platform).indexOf('mobile') !== -1 && (platform).indexOf('web-mobile') == -1);

        let mfa_config = await sails.models.usersetting_reader.findOne({
            where: { name: 'multifactor_auth_config', user_ref: user.id },
            select: ['value']
        });

        let {two_fa_status = false, two_fa_devices = [], backup_codes = []} = mfa_config && mfa_config.value || {};
        let {key, iv = '', tag = ''} = two_fa_devices.find(d => d.enabled === true) || {};

        if(key && iv && tag) {
            key = decryptSymmetric(key, iv, tag);
        }
        const is_super_admin = (user.user_roles.findIndex(r => r.role === TokenUtil.ROLES.SUPER_ADMIN) !== -1);
        sails.log.info(`User has MFA? ${two_fa_status}, UserID: ${user.id}, Has active device: ${(key !== '')}, is_mobile: ${is_mobile_platform}, super-admin: ${is_super_admin}`);

        let is_mfa_check_applicable = (!is_mobile_platform && two_fa_status && key);

        if(is_mfa_check_applicable && validate_mfa === false) {
            sails.log.info(`Multifactor-auth enabled for user, ${user.id} requiring MFA code.`);
            return ResponseService.successResponse(res, { otp_required: true });
        }

        if(is_mfa_check_applicable && validate_mfa === true && validate_bkup === false && !authenticator.check(`${code}`, key)) {
            sails.log.info(`Multifactor-auth enabled for user, ${user.id}. Validating MFA code. Validation failed.`);
            return ResponseService.errorResponse(res, 'Invalid MFA code, Please try again with new MFA code.');
        }
        let reset_mfa;
        if(is_mfa_check_applicable && validate_mfa === true && validate_bkup === true) {
            sails.log.info(`Multifactor-auth enabled for user, ${user.id}. Validating a backup code.`);
            let {has_valid_code, updated_backup_codes} = validateBkupCode(`${code}${user.id}`, backup_codes);
            if(has_valid_code){
                reset_mfa = true;
                let two_fa_settings = { value: { two_fa_devices, two_fa_status, backup_codes: updated_backup_codes } };
                await sails.models.usersetting.updateOne({id: mfa_config.id}).set(two_fa_settings);
            } else {
                sails.log.info(`Multifactor-auth enabled for user, ${user.id}. Backup code validation failed.`);
                return ResponseService.errorResponse(res, 'Invalid backup code, Please try again with a valid backup code.');
            }
        }

        user._authorized_companies = await TokenUtil.getAuthorizedCompaniesCount(user.user_roles);
        let payload = {
            id: user.id,
            email: user.email,
            type: TokenUtil.tokenType.AUTH_TOKEN,
            platform,
        };
        TokenUtil.generateUserToken(payload, (tokenErr, tokenInfo, refreshToken) => {
            if (tokenErr) {
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, tokenErr);
            }
            sails.log.info(`login successful, id: ${user.id}, email: ${user.email} now:`, moment().format());
            AccessLogService.logRequest({
                path: `${req.method}: ${req.path}`,
                action: 'user/login',
                user_agent: req.headers['user-agent'],
                platform: platform,
                auth_token: tokenInfo.token,
                ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                user_ref: user.id
            }).catch(sails.log.error);

            // Assigning refresh token in http-only cookie
            TokenUtil.setRefreshTokenCookie(refreshToken, res, is_super_admin);
            return ResponseService.successResponse(res, {user, tokenInfo, ...(reset_mfa && {reset_mfa})});
        });
    },

    /**
     * this is used to request for another token when the other token is about
     * expiring so for next request call the token can be validated as true
     * SSO flow is also using it
     * GET /token
     * @param req
     * @param res
     */
    token: function (req, res) {
        let platform = req.headers['platform'] || ((req.headers['accept-language'] && (req.headers['accept-language']).indexOf('mobile') !== -1) ? req.headers['accept-language'] : '-');
        let payload = {
            id: req.user.id,
            email: req.user.email,
            type: TokenUtil.tokenType.AUTH_TOKEN,
            platform,
        };
        TokenUtil.generateUserToken(payload, (tokenErr, tokenInfo, refreshToken) => {
            if (tokenErr) {
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, tokenErr);
            }
            sails.log.info(`Created new token, user: ${req.user.id} expiresOn: ${tokenInfo.expiresOn}`);
            TokenUtil.extendUserPayload(req.user).then(user => {

                // Assigning refresh token in http-only cookie
                TokenUtil.setRefreshTokenCookie(refreshToken, res, req.is_super_admin);
                return ResponseService.successResponse(res, { user, tokenInfo });
            })
        });
    },

    profile: async (req, res) => {
        let user = await TokenUtil.extendUserPayload(req.user);
        user._authorized_companies = await TokenUtil.getAuthorizedCompaniesCount(user.user_roles);

        user.raw_uac = undefined;
        res.json({
            success: true,
            user: user,
        })
    },

    updateUser: async (req, res) => {

        sails.log.info('Request has ', req.user.id);
        let updatedUser = _.pick((req.body || {}), [
            'title',
            'first_name',
            'middle_name',
            'last_name',
            'dob',
            'country_code',
            'country',
            'gender',
            'marital_status',
            'sexual_orientation',
            'disability',
            'ethnicity',
            'subethnicity',
            'religion',
            'caring_responsibilities',
            'nin',
            'user_onboard_status',

            'profile_pic_ref',
            'profile_pic_expiry',
            'timezone',
            'parent_company',
            'terms_accepted',
            'offers_subscribed'
        ]);

        updatedUser = cleanUpUserSavePayload(updatedUser);
        // sails.log.info('update user, user_onboard_status', updatedUser.user_onboard_status, ' vs user', req.user.user_onboard_status)
        if (!updatedUser.user_onboard_status && (!req.user.user_onboard_status || !req.user.user_onboard_status.personal)) {
            updatedUser.user_onboard_status = getUpdatedOnboardStatus(req.user, 'personal');
        }
        sails.log.info('update user, timezone', updatedUser.timezone, 'parent_company', updatedUser.parent_company);
        let user = await sails.models.user.updateOne({ id: req.user.id }).set(updatedUser);
        createUserRevision(req.user.id, { personal: user }).catch(sails.log.error);
        let complete_user = await sails.models.user.findOne({
            id: user.id
        }).populate('profile_pic_ref').populate('parent_company').populate('user_roles');
        // await updateUserInductionsWithProfileChanges(req.user, ['personal'], {new_user: complete_user});
        sails.log.info('update successful', complete_user ? complete_user.id : null);
        return ResponseService.successResponse(res, { user: complete_user });
    },

    createShadowUser: async (req, res) => {

        sails.log.info('Request for shadow user creation, requested by:', req.user.id);
        let shadowUser = _.pick((req.body || {}), [
            'title',
            'first_name',
            'middle_name',
            'last_name',
            'dob',
            'country_code',
            'country',
            'gender',
            'marital_status',
            'sexual_orientation',
            'disability',
            'ethnicity',
            'subethnicity',
            'religion',
            'caring_responsibilities',
            'nin',
            'user_onboard_status',

            'profile_pic_ref',
            'profile_pic_expiry',
            // 'timezone',
            'parent_company',
        ]);
        shadowUser.email = getShadowUserEmail((new Date()).getTime());
        shadowUser.password = defaultUserPassword;
        shadowUser = cleanUpUserSavePayload(shadowUser);
        shadowUser.user_onboard_status = getUpdatedOnboardStatus({}, 'personal');
        let user = await sails.models.user.create(shadowUser);
        // createUserRevision(req.user.id, {personal: user}).catch(sails.log.error);
        TokenUtil.storeUACRecordsOrError([{
            user_ref: user.id,
            role: TokenUtil.ROLES.USER,
            resource: '',
            sub_resource: '',
            designation: null,
            flags: {}
        }]).catch(sails.log.error);
        sails.log.info('Created shadow user for', user.email, 'id', user.id, 'requested by:', req.user.id);
        return ResponseService.successResponse(res, {
            message: 'User creation completed',
            user,
        });
    },

    saveShadowUserProfileDetails: async (req, res) => {
        let shadowUserId = req.param('shadowUserId');
        let induct_on_project = +(req.query.induct_on || 0);
        sails.log.info(`Save shadow user details, user: ${shadowUserId} requested by:`, req.user.id);
        let data = _.pick((req.body || {}), [
            'contact_detail',
            'health_assessments',
            'medical_assessments',
            'employment_detail',
            'competency_ids',
            'update_user'
        ]);
        sails.log.info('Request has data ', Object.keys(data));
        if (!_.isArray(data.health_assessments) || !_.isArray(data.medical_assessments) || !_.isArray(data.competency_ids)) {
            sails.log.info('Invalid request Of Health Assessment Answers / Medical Assessment Answers / Competencies ', data.health_assessments, data.medical_assessments);
            return ResponseService.errorResponse(res, INVALID_REQUEST);
        }

        // Update Shadow User Block
        let update_user_body = _.pick((data.update_user || {}), [
            'parent_company',
            'country_code',
            'user_onboard_status',
            'profile_pic_ref',
            'profile_pic_expiry',
            'nin'
        ]);
        if (!update_user_body.country_code) {
            sails.log.warn('country_code not supplied for shadow user', update_user_body);
        }
        // Keeping this update limited to Shadow Users only via `email_verified_on: null` check.
        let updated_user = await sails.models.user.updateOne({
            id: shadowUserId,
            email_verified_on: null
        }).set(update_user_body);

        if (!updated_user || !updated_user.id) {
            sails.log.error('Request has invalid userId', shadowUserId);
            return ResponseService.errorResponse(res, INVALID_REQUEST);
        }

        sails.log.info('Saved user details of', shadowUserId);

        // Save other details
        let contact_detail = await saveContactDetail(updated_user.id, data.contact_detail);
        let health_assessments = await saveUserHealthAssessmentAnswers(updated_user.id, data);
        let medical_assessments = await saveUserMedicalAssessmentAnswers(updated_user.id, data);
        let employment_detail = await saveUserEmploymentDetail(updated_user.id, data.employment_detail, updated_user);


        sails.log.info('Saved other profile details of', shadowUserId);
        let induction = undefined;
        if (induct_on_project) {
            sails.log.info('creating default induction for project', induct_on_project);
            let project = await sails.models.project.findOne({ id: induct_on_project })
                .populate('declarations', { sort: 'id ASC' });
            let { project_logo_file, companyName } = await getCompanyInfo(project);
            project.logo_file_id = project_logo_file; // this needs to be coming form `parent company OR contractor` logo

            let health_assessment_answers = await sails.models.userhealthassessmentanswer.find({ user_ref: shadowUserId }).populate('question_ref');
            // Add needful info of user into it.
            updated_user.name = getUserFullName(updated_user);
            if (updated_user.profile_pic_ref) {
                updated_user.profile_pic_ref = await sails.models.userfile.findOne({ id: +updated_user.profile_pic_ref });
            }
            let induction_request = {
                project_ref: induct_on_project,
                user_ref: shadowUserId,
                creator_name: updated_user.name,
                // inductor_ref: req.user.id,
                confirm_detail_valid: true,
                accepted_declarations: (project.declarations || []).map(d => d.id),
                accepting_media_declaration: (project.has_media_content || false),
                user_doc_ids: (data.competency_ids || []),
                additional_data: {
                    contact_detail,
                    employment_detail,
                    health_assessment_answers,
                    medical_assessments_answers: medical_assessments,
                    platform_type: "web",
                    project,
                    user_info: updated_user, // @todo: populate parent_company if needed in future.
                    version: "v1"
                },
                comments: [{
                    name: updated_user.name,
                    note: "Created New Request",
                    origin: "system",
                    timestamp: (new Date()).getTime(),
                    user_id: updated_user.id
                }]
            };

            if (induction_request.additional_data && project && project.postcode) {
                induction_request.travel_time = await constructDistanceMatrix(induction_request, project.postcode);
            }
            let country_code = (project.custom_field && project.custom_field.country_code);
            induction_request = await attachUserDocument(induction_request, {
                user_ref: shadowUserId,
                last_name: updated_user.last_name,
                country_code,
                parent_company: project.parent_company,
            });
            induction = await sails.models.inductionrequest.create(induction_request);
            // @todo: spatel: send email to inductor?
        }

        return ResponseService.successResponse(res, {
            message: 'User saved successfully',
            induction,
            updated_user,
        });
    },

    getUserContactDetail: async (req, res) => {
        let contact_detail = await getUserContactDetail(req.user.id);
        return ResponseService.successResponse(res, { contact_detail });
    },

    getCompanyUserContactDetail: async (req, res) => {
        let userId = +(req.param('userId', 0));
        let contact_detail = await getUserContactDetail(userId, 'company');
        return ResponseService.successResponse(res, {contact_detail});
    },

    saveContactDetail: async (req, res) => {
        let contact_detail = await saveContactDetail(req.user.id, req.body);

        let personalData = _.pick((req.body || {}), ['timezone']);
        if (req.user.user_onboard_status && !req.user.user_onboard_status.address) {
            personalData = { ...personalData, user_onboard_status: getUpdatedOnboardStatus(req.user, 'address') };
        }
        let user = await sails.models.user.updateOne({ id: req.user.id }).set(personalData);
        createUserRevision(req.user.id, { personal: user, address: contact_detail }).catch(sails.log.error);

        let update_induction = req.param('update_induction', 'false').toString() === 'true';
        if (update_induction) {
            updateUserInductionsWithProfileChanges(req.user, ['contact'], {
                contact_detail
            }).catch(err => {
                sails.log.info(`[saveContactDetail]: Updating induction for user is ${req.user.id} failed `, err);
            });
        }
        req.user = user;
        return ResponseService.successResponse(res, { contact_detail, partial_user: { user_onboard_status: user.user_onboard_status } });
    },

    createSupportTicket: async (req, res) => {
        sails.log.info('creating support ticket from user');
        let supportTicket = _.pick((req.body || {}), [
            'name',
            'email',
            'phone_no',
            'query',
            'recaptcha_key'
        ]);

        if (!supportTicket.email || !supportTicket.query || !supportTicket.recaptcha_key) {
            sails.log.info('validation failed', supportTicket);
            return ResponseService.errorResponse(res, 'Validation failed', { extra: 'Required field is missing' });
        }

        let MOBILE_APP_REQUEST_KEY = sails.config.custom.MOBILE_APP_REQUEST_KEY;
        // run verification when config is absent or app key is not equal to captcha key
        if (!MOBILE_APP_REQUEST_KEY || !MOBILE_APP_REQUEST_KEY.length || MOBILE_APP_REQUEST_KEY !== supportTicket.recaptcha_key) {

            let recaptcha_url = "https://www.google.com/recaptcha/api/siteverify?";
            let response = await HttpService.makeGET(recaptcha_url, {
                secret: sails.config.custom.CAPTCHA_SECRET_KEY,
                response: supportTicket.recaptcha_key,
                remoteip: req.connection.remoteAddress
            });
            sails.log.info('Response from google captcha', response.data);
            if (response.error || !response.success || response.data['error-codes']) {
                return ResponseService.errorResponse(res, 'Validation failed', { extra: 'Captcha validation failed' });
            }
        }

        supportTicket.email = (supportTicket.email || '').trim().toLowerCase();
        // supportTicket.user_ref = req.user.id;
        sails.models.supportticket.create(supportTicket).exec(function createCallback(createError, support_ticket) {
            if (createError) {
                sails.log.info('Failed to create support ticket', createError);
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, createError);
            }
            sails.log.info('created successfully', support_ticket);
            ResponseService.successResponse(res, { support_ticket });
            sendSupportRequestCreateAlert(support_ticket);
            return true;
        });
    },

    getUserHealthQuestions: async (req, res) => {
        sails.log.info('get Health Assessment Questions of ', req.user.id);
        sails.models.userhealthassessmentquestion
            .find()
            .exec(function fetchCallback(fetchError, assessment_questions) {
                if (fetchError) {
                    sails.log.info('Failed to find Health Assessment Questions', fetchError);
                    return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
                }
                sails.log.info('query successful', assessment_questions ? assessment_questions.length : null);
                return ResponseService.successResponse(res, { assessment_questions: _.groupBy(assessment_questions || [], (i) => i.category) });
            });
    },

    getUserHealthAssessment: async (req, res) => {
        let health_assessments = await getUserHealthAssessment(req.user.id, (req.query.expand || '').toString().trim());
        return ResponseService.successResponse(res, { health_assessments });
    },

    getCompanyUserHealthAssessment: async (req, res) => {
        let userId = +(req.param('userId', 0));
        let health_assessments = await getUserHealthAssessment(userId, (req.query.expand || '').toString().trim());
        return ResponseService.successResponse(res, { health_assessments });
    },

    saveUserHealthAssessment: async (req, res) => {
        if (!_.isArray(req.body.health_assessments)) {
            sails.log.info('Invalid request User Health Assessment Answers ', req.body.health_assessments);
            return ResponseService.errorResponse(res, INVALID_REQUEST);
        }
        let health_assessments = await saveUserHealthAssessmentAnswers(req.user.id, req.body);
        if (req.user.user_onboard_status && !req.user.user_onboard_status.health_assessment) {
            let user = await sails.models.user.updateOne({ id: req.user.id }).set({
                user_onboard_status: getUpdatedOnboardStatus(req.user, 'health_assessment')
            });
            createUserRevision(req.user.id, { personal: user }).catch(sails.log.error);
            req.user = user;
        }
        let update_induction = req.param('update_induction', 'false').toString() === 'true';
        if (update_induction) {
            let questions_meta = await sails.models.userhealthassessmentquestion.find();
            updateUserInductionsWithProfileChanges(req.user, ['health'], {
                health_assessments: health_assessments,
                health_questions: questions_meta
            }).catch(err => {
                sails.log.info(`[saveUserHealthAssessment]: Updating induction for user is ${req.user.id} failed `, err);
            });
        }
        return ResponseService.successResponse(res, { health_assessments, partial_user: { user_onboard_status: req.user.user_onboard_status } });
    },

    getUserMedicalQuestions: async (req, res) => {
        sails.log.info('get Medical Assessment Questions List');
        try {
            let medicalAssessmentQue = await sails.models.inndexsetting_reader.findOne({
                where: { name: "medical_assessment_questions" },
            });

            if (medicalAssessmentQue && medicalAssessmentQue.id) {
                medicalAssessmentQue = _.chain(medicalAssessmentQue.value)
                    .sortBy('order')
                    .filter('is_active')
                    .value();
                sails.log.info('medicalAssessmentQue query successful, count:', medicalAssessmentQue ? medicalAssessmentQue.length : null);
                return ResponseService.successResponse(res, { medicalAssessmentQue });
            }

            sails.log.info('Medical assessment questions not found.');
            return ResponseService.errorResponse(res, 'User medical assessment questions not found.');
        } catch (fetchError) {
            sails.log.info('Failed to fetch medical assessment questions.', fetchError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
        }
    },

    getUserMedicalAssessment: async (req, res) => {
        let medical_assessments = await getUserMedicalAssessment(req.user.id);
        return ResponseService.successResponse(res, { medical_assessments });
    },

    getCompanyUserMedicalAssessment: async (req, res) => {
        let userId = +(req.param('userId', 0));
        let medical_assessments = await getUserMedicalAssessment(userId);
        return ResponseService.successResponse(res, { medical_assessments });
    },

    saveUserMedicalAssessment: async (req, res) => {
        if (!_.isArray(req.body.medical_assessments)) {
            sails.log.info('Invalid request User Medical Assessment Answers ', req.body.medical_assessments);
            return ResponseService.errorResponse(res, INVALID_REQUEST);
        }
        let medical_assessments = await saveUserMedicalAssessmentAnswers(req.user.id, req.body);
        if (req.user.user_onboard_status && !req.user.user_onboard_status.medical_assessments) {
            let user = await sails.models.user.updateOne({ id: req.user.id }).set({
                user_onboard_status: getUpdatedOnboardStatus(req.user, 'medical_assessments')
            });
            createUserRevision(req.user.id, { personal: user }).catch(sails.log.error);
            req.user = user;
        }
        let update_induction = req.param('update_induction', 'false').toString() === 'true';
        if (update_induction) {
            let medicalAssessmentQue = await sails.models.inndexsetting_reader.findOne({ where: { name: "medical_assessment_questions" } });
            updateUserInductionsWithProfileChanges(req.user, ['medical'], {
                medical_assessments: medical_assessments,
                medical_questions: medicalAssessmentQue.value || []
            }).catch(err => {
                sails.log.info(`[saveUserMedicalAssessment]: Updating induction for user is ${req.user.id} failed `, err);
            });
        }
        return ResponseService.successResponse(res, { medical_assessments, partial_user: { user_onboard_status: req.user.user_onboard_status } });
    },

    getUserEmploymentDetail: async (req, res) => {
        sails.log.info('get user_employment details of ', req.user.id);
        sails.models.userempdetail.findOne({ user_ref: req.user.id }).exec(function fetchCallback(fetchError, employment_detail) {
            if (fetchError) {
                sails.log.info('Failed to find user employment details', fetchError);
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
            }
            sails.log.info('query successful', employment_detail ? employment_detail.id : null);
            return ResponseService.successResponse(res, { employment_detail });
        });
    },

    getUserEmploymentDetailById: async (req, res) => {
        let userId = req.param('userId');
        sails.log.info('fetching user employment details of user: ', userId);
        let employment_detail = sails.models.userempdetail_reader.findOne({ user_ref: userId });
        sails.log.info('User employment details fetched successfully', employment_detail ? employment_detail.id : null);
        return ResponseService.successResponse(res, { employment_detail });
    },

    saveUserEmploymentDetail: async (req, res) => {
        let update_user = {};
        if (!req.body.employer_id) {
            sails.log.warn('saveUserEmploymentDetail does not have parent_company of', req.user.id, 'employer', req.body.employer);
        }
        //save user employer id in users table as well
        if (req.body.employer_id && req.body.employer_id > 0) {
            sails.log.info('Saving parent_company for user', req.user.id, 'employer', req.body.employer_id);
            update_user.parent_company = req.body.employer_id;
        }
        if (!req.body.country_code) {
            // figure out country code and update user if needed.
            let platform = req.headers['platform'] || ((req.headers['accept-language'] && (req.headers['accept-language']).indexOf('mobile') !== -1) ? req.headers['accept-language'] : '-');
            sails.log.warn('country code not supplied, platform:', platform);
        }

        if (req.body.country_code && req.user.country_code !== req.body.country_code) {
            // update user
            sails.log.info('update country code with', req.body.country_code);
            update_user.country_code = req.body.country_code;
        }
        if (Object.keys(update_user).length) {
            let user = await sails.models.user.updateOne({ id: req.user.id }).set(update_user);
            createUserRevision(req.user.id, { personal: user }).catch(sails.log.error);
            req.user = user;
        }
        let employment_detail = await saveUserEmploymentDetail(req.user.id, req.body, req.user);
        return ResponseService.successResponse(res, { employment_detail });
    },

    getUserTimeDetails: async (req, res) => {
        let userId = req.param('userId');
        let companyId = req.param('companyId');
        sails.log.info(`processing company project time-sheet request, for companyId: ${companyId} user: ${userId}`);
        let time_logs = await getUserCompanyTimeEvents(companyId, userId);
        return ResponseService.successResponse(res, { 'timeLogs': time_logs });
    },

    sendEmailVerificationMail: async (req, res) => {
        let userId = (+req.param('userId', 0));
        if (!userId) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        let filter = {
            email_verified_on: null,
            id: userId,
        };
        sails.log.info('Sending verification email, filter is ', filter);
        let users = await sails.models.user_reader.find({ select: ['id', 'first_name', 'email'], where: filter });
        if (users && users.length) {
            sails.log.info('Total records', users.length);
            for (let i = 0, len = users.length; i < len; i++) {
                let updatedUser = await sails.models.user.updateOne({ id: users[i].id }).set({
                    verify_email_token: (Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)),
                });
                if (updatedUser) {
                    await sendVerifyEmailNotification(updatedUser);
                }
            }
        }
        return ResponseService.successResponse(res, { users, message: 'Notification sent successfully.' });

    },

    getAllUsers: async (req, res) => {

        let users = await sails.models.user_reader.find({
            select: [
                'id', 'email', 'is_active',
                'first_name', 'middle_name', 'last_name',
                'createdAt', 'email_verified_on'
            ],
            sort: ['id DESC']
        });
        let user_roles_by_ref = await getRolesForAllUsers();
        for (let i = 0; i < users.length; i++) {
            users[i].user_roles = user_roles_by_ref[users[i].id] || [];
        }
        return ResponseService.successResponse(res, { allusers: users });
        /*
        // old version.
        sails.models.user_reader.find({
            select: [
                'id', 'email', 'is_active',
                'first_name', 'middle_name', 'last_name',
                'createdAt', 'email_verified_on'
            ],
            sort: ['id DESC']
        }).populate('user_roles').exec(function fetchCallback(fetchError, allusers) {
            if (fetchError) {
                sails.log.info('Failed to find users', fetchError)
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
            }
            return ResponseService.successResponse(res, {allusers});
        });*/
    },
    getAllUsersV2: async (req, res) => {
        let pageSize = +req.param('pageSize', 50);
        let pageNumber = +req.param('pageNumber', 0);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let searchTerm = decodeURIParam((req.param('q', '')).toString().trim());
        let fromDate = req.param('from') ? dayjs(req.param('from')).startOf('day').valueOf() : "";
        let toDate = req.param('to') ? dayjs(req.param('to')).endOf('day').valueOf() : "";
        let {records: users, total} = await getAllUsersFn(pageSize, (pageSize * pageNumber), sortKey, sortDir, { searchTerm, fromDate, toDate });
        let user_roles_by_ref = await getRolesForAllUsers(users);
        for (let i = 0; i < users.length; i++) {
            users[i].user_roles = user_roles_by_ref[users[i].id] || [];
            users[i].name = getUserFullName(users[i]);
            let {roles, uac, user_roles} = TokenUtil.addUserUACDetails(users[i]);
            users[i].roles = roles;
            users[i].uac = uac;
            users[i].user_roles = user_roles;
        }

        return ResponseService.successResponse(res, {
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            fromDate,
            toDate,
            totalCount: total,
            records: users,
              });
    },

    updateAdminAccess: async (req, res) => {
        sails.log.info("User Id is", req.body.id, 'mark site admin?', req.body.value);
        try {
            let allowed_projects = await getAllowedDefaultProjectsOfUser(req.body.id, false, false);
            let permissions = [];
            // Already a SITE-admin
            let already_permission = (allowed_projects || []).filter(uac => !uac.resource);
            sails.log.info('Existing permission of user', already_permission);
            if (req.body.value && already_permission.length) {
                // No-action needed.
                permissions = already_permission;
            }
            else if (req.body.value && !already_permission.length) {
                // Add create project permission.
                permissions = await storeUACRecordsOrError([{
                    user_ref: req.body.id,
                    role: ROLES.SITE_ADMIN,
                    resource: null,
                    sub_resource: '',
                    designation: null,
                    flags: {}
                }]);
            }

            else if (!req.body.value && already_permission.length) {
                // Remove all create project permission
                for (let i = 0; i < already_permission.length; i++) {
                    await removeUAC(already_permission[i].id, already_permission[i]);
                }
                permissions = already_permission;
            }

            let updatedUser = await sails.models.user.findOne({ id: req.body.id }).populate('user_roles');

            return ResponseService.successResponse(res, { message: `Admin Access Has Been Updated`, user: updatedUser });
        } catch (e) {

            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, { e });
        }

    },

    deactiveUser: async (req, res) => {
        sails.log.info('Deactivating user account.');
        let userId = +req.user.id;
        if (userId) {
            try {
                let userData = await sails.models.user.updateOne({ id: userId }).set({ is_active: 0 });
                if (userData) {
                    await sendAcntDeactReqNotifAdmin(userData);
                    sails.log.info('Deactivated user account.');
                    return ResponseService.successResponse(res, { message: "Deactivated user account." });
                }
            } catch (e) {
                sails.log.info('Failed to deactive user account', e);
                return ResponseService.errorResponse(res, 'Failed to deactive user account');
            }
        }
        sails.log.info('Failed to deactive user account.');
        return ResponseService.errorResponse(res, 'Failed to deactive user account.');
    },

    deleteUser: async (req, res) => {
        let userId = +req.param('userId', 0);
        sails.log.info('Deleting user account, userId:', userId);
        if (!userId) {
            sails.log.info('Failed to delete user, userId:', userId);
            return ResponseService.errorResponse(res, 'Failed to delete user by given ID');
        }
        //get user info
        let userInfo = await sails.models.user.findOne({ where: { id: userId } });
        if (!userInfo) {
            sails.log.info('Failed to delete user, Record not found, userId:', userId);
            return ResponseService.errorResponse(res, 'Failed to delete user by given ID, Record not found');
        }

        let profilePicRef = userInfo.profile_pic_ref;

        //delete contact details
        await sails.models.contactdetail.destroy({ user_ref: userId });
        sails.log.info('The user contact detail has been deleted.');

        //delete profile pic
        if (profilePicRef) {
            sails.log.info('Deleting user profile pic.');
            let deleted = await SharedService.deleteFileRecord(profilePicRef, userId);
            sails.log.info('The user profile pic has been deleted.');
        }

        //user_employment_detail
        await sails.models.contactdetail.destroy({ user_ref: userId });
        sails.log.info('The user employment detail has been deleted.');

        //user_employment_detail
        await sails.models.userempdetail.destroy({ user_ref: userId });
        sails.log.info('The user employment detail has been deleted.');

        //user_health_assessment_answer
        await sails.models.userhealthassessmentanswer.destroy({ user_ref: userId });
        sails.log.info('The user health assessment answer has been deleted.');

        await sails.models.userrole.destroy({ user_ref: userId });

        let deletedOneRecords = await sails.models.user.destroy({ id: userId });

        // @todo: alot more relations now need integration with this delete call.
        sails.log.info('The user has been deleted.', deletedOneRecords);
        return ResponseService.successResponse(res, { message: "The user has been deleted." });
    },

    getUserDocumentsByEmployer: async (req, res) => {
        sails.log.info('Employer Id', req.param('employerId'));
        let employer_id = req.param('employerId');
        let toBefilterd = req.body.toBefilterd;
        if (employer_id) {
            try {
                let employerInfo = await sails.models.createemployer.findOne({
                    where: { id: employer_id, has_company_portal: true },
                    select: ['id', 'name']
                });
                if (employerInfo) {
                    let empFilter = { employer: employerInfo.name }
                    if (toBefilterd) {
                        empFilter.id = req.body.employerUsersList;
                    }
                    let employees_details = await sails.models.userempdetail.find(empFilter);
                    employees_details = await populateUserRefs(employees_details, 'user_ref', []);
                    let data = [];
                    let record = {};
                    let userList = [];
                    employees_details.forEach(function (emp) {
                        userList.push(emp.user_ref.id);
                    });
                    let userDocs = await sails.models.userdoc.find({
                        where: {
                            doc_owner_id: userList,
                            // we need all docs of user here
                            // parent_doc_ref: null, // get only parent docs
                            is_deleted_manually: { '!=': 1 }
                        },
                        select: ['id', 'name', 'expiry_date', 'user_id', 'doc_owner_id']
                    });
                    employees_details.forEach(function (emp) {
                        record = {};
                        record.userDocuments = userDocs.filter(uDoc => uDoc.doc_owner_id == emp.user_ref.id);
                        record.job_role = emp.job_role;
                        record.name = getUserFullName(emp.user_ref);
                        record.user_id = (emp.user_ref && emp.user_ref.id);
                        data.push(record);
                    });

                    return ResponseService.successResponse(res, { data: data });
                }
            } catch (fetchError) {
                sails.log.info('Failed to fetch user employment details', fetchError);
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
            }
        }
        else {
            sails.log.info('Employer id is required.');
            return ResponseService.errorResponse(res, 'Employer id is required.');
        }
    },

    getUsersByEmployer: async (req, res) => {
        sails.log.info('Employer Id', req.param('employerId'));
        let employer_id = req.param('employerId');
        if (employer_id) {
            try {
                let employerInfo = await sails.models.createemployer.findOne({
                    where: { id: employer_id, has_company_portal: true },
                    select: ['name', 'country_code']
                });
                if (employerInfo) {
                    let employees_details = await sails.models.userempdetail.find({ employer: employerInfo.name });
                    employees_details = await populateUserRefs(employees_details, 'user_ref', []);

                    if (employees_details.length) {
                        let profilePicIds = employees_details.map((employee_details, index) => {
                            return employee_details.user_ref.profile_pic_ref
                        });

                        profilePicIds = profilePicIds.filter(function (el) {
                            return el != null;
                        });

                        let userfiles = await sails.models.userfile.find({
                            where: { id: profilePicIds },
                            select: ['id', 'file_url']
                        });

                        employees_details = employees_details.map((employee_details, index) => {
                            userfiles.forEach((userfile) => {
                                if (employee_details.user_ref.profile_pic_ref && (employee_details.user_ref.profile_pic_ref === userfile.id)) {
                                    employee_details.user_ref.profile_pic = userfile.file_url;
                                    return;
                                }
                            });
                            if (!employee_details.user_ref.profile_pic) {
                                employee_details.user_ref.profile_pic = 'https://ssl.gstatic.com/accounts/ui/avatar_2x.png';
                            }

                            return employee_details;
                        });
                    }

                    let employer_name = employerInfo.name;
                    let project_logo_file = {};
                    if (employer_name) {
                        project_logo_file = await TokenUtil.getProjectLogo('contractor', employer_name, employerInfo.country_code);
                    }
                    if (!employees_details) {
                        sails.log.info('Failed to fetch user employment details');
                        return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR);
                    }
                    return ResponseService.successResponse(res, { employees_details, employer_name, project_logo_file });
                } else {
                    sails.log.info('Employer detail not found.');
                    return ResponseService.errorResponse(res, 'Employer detail not found.');
                }
            } catch (fetchError) {
                sails.log.info('Failed to fetch user employment details', fetchError);
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
            }
        } else {
            sails.log.info('Employer id is required.');
            return ResponseService.errorResponse(res, 'Employer id is required.');
        }
    },

    storeUserSetting: async (req, res) => {
        let { validationError, payload } = saveUserSetting(req);
        if (validationError) {
            return ResponseService.errorResponse(res, 'Invalid Request.', { validationError });
        }

        let userSetting = _.pick((req.body || {}), [
            'name',
            'value',
        ]);
        sails.log.info('store user setting', userSetting.name);
        try {
            let record = await sails.models.usersetting.findOne({
                name: userSetting.name,
                user_ref: req.user.id
            });
            userSetting.user_ref = req.user.id;

            if (record && record.id) {
                sails.log.info('updating existing user setting', record.id);

                let user_setting = await sails.models.usersetting.updateOne({ id: record.id }).set(userSetting);
                return ResponseService.successResponse(res, { user_setting });
            } else {
                sails.log.info('creating user setting');
                let user_setting = await sails.models.usersetting.create(userSetting);
                return ResponseService.successResponse(res, { user_setting });
            }
        } catch (apiError) {
            sails.log.info('Failed to store user setting', apiError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), apiError);
        }
    },

    getUserSettings: async (req, res) => {
        let name = req.param('name');
        sails.log.info('get user settings', name);
        let filter = {
            user_ref: req.user.id
        };
        if (name) {
            filter.name = name;
        }
        let user_settings = await sails.models.usersetting.find({ where: filter, select: ['name', 'value'] });
        return ResponseService.successResponse(res, { user_settings });
    },

    deleteEmployerUser: async (req, res) => {
        try {

            let deletedOneRecords = await sails.models.userempdetail.destroyOne({
                user_ref: req.param('companyUserId')
            });

            const userOnBoardStatus = await sails.models.user.findOne({
                where: { id: req.param('companyUserId') },
                select: ['user_onboard_status'],
            });

            const updatedRecord = { ...userOnBoardStatus.user_onboard_status, employment: false }

            const updateUserBoardStatus = await sails.models.user.updateOne({ id: userOnBoardStatus.id })
                .set({ user_onboard_status: updatedRecord });

            return ResponseService.successResponse(res, {
                deleted: deletedOneRecords,
                updatedUserBoard: updateUserBoardStatus
            });

        } catch (error) {
            sails.log.info('Error while deleting', error);
            return ResponseService.errorResponse(res, sails.__('internal server error'), error);
        }

    },

    getUserCurrentProject: async (req, res) => {
        let userId = +req.param('userId',0);
        if (!userId || isNaN(userId)) {
            userId = req.user.id;
        }

        let [dailyLog] = await sails.models.userdailylog_reader.find({
            where: { user_ref: userId },
            select: ['project_ref'],
            limit: 1,
            sort: ['day_of_yr DESC']
        });

        if(dailyLog && dailyLog.project_ref) {
            let project_info = await sails.models.project_reader.findOne({
                select: ['postcode', 'name', 'description', 'is_active', 'disabled_on'],
                where: { id: +dailyLog.project_ref },
            });

            sails.log.info('query successful', project_info ? project_info.id : null);
            return ResponseService.successResponse(res, { project_info });
        }

        sails.log.info('Project not found.');
        return ResponseService.errorResponse(res, 'User project not found.');
    },

    validateUserProjectRelation: async (req, res) => {
        let userId = req.param('userId');
        let projectId = req.project.id; //req.param('projectId');
        if (!userId || !projectId) {
            return ResponseService.errorResponse(res, 'Invalid request.', {});
        }

        try {
            let user_info = await sails.models.user.findOne({
                select: ['first_name', 'last_name', 'parent_company'],
                where: { id: userId },
            }).populate('profile_pic_ref');

            let project_info = await sails.models.project.findOne({
                select: ['parent_company', 'project_category'],
                where: { id: projectId },
            });


            let profile_pic = '';
            if (user_info.profile_pic_ref && user_info.profile_pic_ref.file_url) {
                profile_pic = user_info.profile_pic_ref.file_url;
            }


            let allowed = false;
            if (user_info && user_info.id && project_info && project_info.id) {
                if (project_info.project_category === 'default') {
                    let induction = await sails.models.inductionrequest.find({
                        select: ['record_id', 'optima_badge_number'],
                        where: {
                            project_ref: projectId,
                            user_ref: userId,
                            status_code: [2, 6]
                        }
                    }).limit(1);

                    sails.log.info('induction', induction);
                    if (induction.length) {
                        allowed = true;
                    }
                }
                else if (+user_info.parent_company === +project_info.parent_company) {
                    allowed = true;
                }
            }

            sails.log.info(`User: ${userId} allowed to use innTime? ${allowed} Project(${projectId}) category: ${project_info.project_category}`);
            return (
                allowed
                    ?
                    ResponseService.successResponse(res, {
                        user_details: {
                            'first_name': user_info.first_name,
                            'last_name': user_info.last_name,
                            'profile_pic': profile_pic
                        }, allowed
                    })
                    :
                    ResponseService.errorResponse(res, { user_details: {}, allowed })
            );

        } catch (fetchError) {
            sails.log.info('Failed to verify user project details', fetchError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
        }
    },

    downloadCompanyEmployeeInfo: async (req, res) => {
        let employeeId = +req.param('employeeId');
        let companyId = +req.param('companyId');
        let updatedAt = +req.param('updatedAt');
        let type = 'pdf';

        sails.log.info('Fetch employee information, id:', employeeId);
        let whereClause = {
            id: employeeId,
            updatedAt: updatedAt
        };

        return await processToDownloadEmployeeInfo(req, res, whereClause, employeeId, companyId, type);
    },

    downloadCompanyEmployeeInfoV1: async (req, res) => {
        let employeeId = +req.param('employeeId');
        let companyId = +req.body.companyId || 0;
        let createdAt = +req.body.createdAt || 0;
        let type = req.body.type;

        sails.log.info('Fetch employee information, id:', employeeId);
        let whereClause = {
            id: employeeId,
            createdAt: createdAt
        };

        return await processToDownloadEmployeeInfo(req, res, whereClause, employeeId, companyId, type);
    },

    fetchUsersByEmployer: async (req, res) => {
        sails.log.info('Employer Id', req.param('employerId'));
        let employer_id = req.param('employerId');

        let employerInfo = await sails.models.createemployer.findOne({
            where: { id: employer_id },
            select: ['name']
        });

        let usersInfo = await sails.models.userempdetail.find({
            where: {
                employer: employerInfo.name
            },
            select: ['user_ref']
        });
        usersInfo = await populateUserRefs(usersInfo, 'user_ref', []);

        let usersProfilePicIds = [];
        let employer_users = (usersInfo || []).reduce((iu, userInfo) => {
            iu.push(userInfo.user_ref);
            if (userInfo.user_ref.profile_pic_ref) {
                usersProfilePicIds.push(userInfo.user_ref.profile_pic_ref);
            }
            return iu
        }, []);

        sails.log.info('User profile pic Ids count ', usersProfilePicIds.length);

        employer_users = await attachProfilePicWithUsersInfo(employer_users, usersProfilePicIds);

        return successResponse(res, { employer_users: employer_users });
    },

    getUsersById: async (req, res) => {
        let userIds = req.body.user_ids;
        //let select_fields = req.body.select_fields;
        let filter = { id: _uniq(userIds) };
        let projectId = req.body.projectId;
        if (projectId > 0) {
            //check if user has only site management access on project
            let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
            sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);
            if (hasOnlySiteManagement) {
                filter.parent_company = req.user.parent_company;
            }
        }

        //sails.log.info("userIds ", userIds, "select_fields ", select_fields);
        let users = await sails.models.user.find({
            where: filter,
            select: ['id', 'email', 'first_name', 'last_name']
        });

        sails.log.info("Found users count: ", users.length);

        ResponseService.successResponse(res, { users });
    },

    getProjectActiveUsers: async (req, res) => {
        let project_id = +req.param('projectId');
        let onlyApprovedInductionUsers = req.param('approved-induction', 'false');
        let blacklistedInductions = await sails.models.inductionrequest_reader.find({
            where: {
                project_ref: project_id,
                status_code: [4, 5]
            },
            select: ['user_ref']
        });
        let blackListedUserIds = (blacklistedInductions || []).map(induction => induction.user_ref);
        sails.log.info(`found ${blackListedUserIds.length} blacklisted users.`);

        //include logged-in user into blacklisted arr to exclude from possible sender list
        blackListedUserIds.push(req.user.id);

        let rawResult = await sails.sendNativeQuery(
            `SELECT
                    additional_data->'employment_detail'->'employer' as employer,
                    additional_data->'user_info'->'parent_company' as employer_id,
                    user_ref
                FROM induction_request
                WHERE
                    status_code IN ($1, $2) AND
                    project_ref = $3 AND
                    user_ref <> $4`,
            [2, 6, project_id, req.user.id]
        );

        let approved_induction_userId = [];
        let employer_users = [];
        sails.log.info("Found approved induction record. Length: ", rawResult.rows.length);
        let rows = (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        rows.map(row => {
            row.employer_id = (row.employer_id && HttpService.typeOf(row.employer_id, 'object')) ? row.employer_id.id : row.employer_id;
            if (row.employer && row.employer_id && row.user_ref) {
                approved_induction_userId.push(row.user_ref);

                let employer = employer_users.find(item => item.employer === row.employer);
                let employerIndex = employer_users.findIndex(item => item.employer === row.employer);
                if (employerIndex != -1 && employer) {
                    employer.users.push(row.user_ref);
                    employer_users[employerIndex] = employer;
                } else {
                    employer_users.push({
                        employer: row.employer,
                        employer_id: row.employer_id,
                        users: [row.user_ref]
                    })
                }
            }
            return row;
        });

        if (onlyApprovedInductionUsers === 'false') {

           const {today_visited_userId,last30_visited_userId} = await processVisitingRecords([project_id],blackListedUserIds);
            let usersInfo = await sails.models.user.find({
                where: { 'id': _uniq([...approved_induction_userId, ...last30_visited_userId, ...today_visited_userId]) },
                select: ['email', 'first_name', 'middle_name', 'last_name', 'parent_company']
            }).populate('parent_company');


            let usersEmpInfo = await sails.models.userempdetail_reader.find({
                where: { 'user_ref': _uniq([...approved_induction_userId, ...last30_visited_userId, ...today_visited_userId]) },
                select: ['user_ref', 'job_role']
            });

            usersInfo = (usersInfo || []).map(user => {
                let empInfo = (usersEmpInfo || []).find(userEmp => userEmp.user_ref == user.id);
                user.job_role = (empInfo && empInfo.job_role) ? empInfo.job_role : null;
                return user;
            })

            let visitedUsersByEmployer = [];
            usersInfo.map(user => {
                if (user.parent_company && user.parent_company.id && _uniq([...last30_visited_userId, ...today_visited_userId]).includes(user.id)) {
                    let employer = visitedUsersByEmployer.find(item => item.employer_id === user.parent_company.id);
                    let employerIndex = visitedUsersByEmployer.findIndex(item => item.employer_id === user.parent_company.id);
                    if (employerIndex != -1 && employer) {
                        employer.users.push(user);
                        visitedUsersByEmployer[employerIndex] = employer;
                    } else {
                        visitedUsersByEmployer.push({
                            employer: user.parent_company.name,
                            employer_id: user.parent_company.id,
                            users: [user]
                        })
                    }
                }
                return user;
            });

            (employer_users || []).map(employer => {
                let userIds = _uniq(employer.users);
                employer.users = (usersInfo || []).filter(user => userIds.includes(user.id));
                return employer;
            });

            let approved_induction_user = ((usersInfo || []).filter(user => approved_induction_userId.includes(user.id))).sort((a, b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));
            let today_visited_user = ((usersInfo || []).filter(user => today_visited_userId.includes(user.id))).sort((a, b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));
            let last30_visited_user = ((usersInfo || []).filter(user => last30_visited_userId.includes(user.id))).sort((a, b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));


            sails.log.info("Found active users count: ", usersInfo.length);
            return ResponseService.successResponse(res, { approved_induction_user, approved_induction_company: employer_users, visited_users_by_employer: visitedUsersByEmployer, today_visited_user, last30_visited_user });
        }

        //only approve induction users
        let usersInfo = await sails.models.user.find({
            where: { 'id': _uniq([...approved_induction_userId]) },
            select: ['email', 'first_name', 'last_name', 'parent_company']
        }).populate('parent_company');

        let approved_induction_user = ((usersInfo || []).filter(user => approved_induction_userId.includes(user.id))).sort((a, b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));
        sails.log.info("Found active users count: ", usersInfo.length);
        return ResponseService.successResponse(res, { approved_induction_user });
    },

    addDocumentForSign: async (req, res) => {
        sails.log.info("Add/Send new document for sign.");
        let requestBody = _.pick((req.body || {}), [
            'document_title',
            'doc_file_ref',
            'employee_ref'
        ]);

        let { validationError, payload } = addDocument(requestBody);
        if (validationError) {
            return ResponseService.errorResponse(res, 'Invalid Request.', { validationError });
        }
        sails.log.info("Document sign request is valid.");

        payload.user_ref = req.user.id;
        payload.status = false;

        const record = await sails.models.userdocumentsign.create(payload);
        sails.log.info("Document sign request has been added, Sending push notification to employee now.");

        await sendNotificationForDocumentSign(record, req);

        return ResponseService.successResponse(res, { document: record });
    },

    resendSignDocument: async (req, res) => {
        let documentId = +req.param('id');
        let record = await sails.models.userdocumentsign_reader.findOne({ id: documentId, status: false });

        if (!record) {
            return ResponseService.errorResponse(res, { document: "Document not found or already signed." });
        }
        let payload = {
            resend_logs: [moment().valueOf(), ...(record.resend_logs)]
        }
        await sails.models.userdocumentsign.updateOne({ id: documentId }).set(payload)
        await sendNotificationForDocumentSign(record, req);
        return ResponseService.successResponse(res, { document: "Document Resend successfully" });
    },

    submitDocumentOfSign: async (req, res) => {
        const documentId = +req.param('id');
        let requestBody = _.pick((req.body || {}), [
            'sign'
        ]);

        let { validationError, payload } = signedDocument(requestBody);
        if (validationError) {
            return ResponseService.errorResponse(res, 'Invalid Request.', { validationError });
        }
        payload.status = true;

        await sails.models.userdocumentsign.updateOne({ id: documentId, status: false }).set(payload);
        let record = await sails.models.userdocumentsign_reader.findOne({
            where: {
                id: documentId
            },
            select: ['doc_file_ref', 'sign']
        })
            .populate('doc_file_ref')
            .populate('employee_ref');

        let merger = new PDFMerger();
        let documentPDFBuffer = await fetchUrlAs(record.doc_file_ref.file_url, 'arraybuffer');
        merger.add(documentPDFBuffer.data);
        sails.log.info("Fetching existing PDF as buffer.");

        //generate sign pdf
        let form_template = `pages/document-signed-page`;
        let html = await sails.renderView(form_template, {
            title: 'Signed Document',
            employee_name: getUserFullName(record.employee_ref),
            sign: record.sign.sign,
            signed_at: moment(+record.sign.signed_at).format('DD-MM-YYYY HH:mm:ss'),
            layout: false
        });

        sails.log.info(html);
        let pdfBuffer = await instantPdfGenerator(req, res, html, 'user-doc-sign', 'Signed-Document', req.headers['user-agent'], { format: 'A4' }, 'pdfBuffer');

        sails.log.info("Generating signed PDF.");

        merger.add(pdfBuffer);
        let mergedPdf = await merger.saveAsBuffer(); //save under given name and reset the internal document
        sails.log.info("Both PDFs has been merged.");

        let outcome = await SharedService.s3Uploader(record.doc_file_ref.name, mergedPdf, 'application/pdf');
        sails.log.info("Merged PDF path from s3: ", outcome.public_url);

        await sails.models.userfile.updateOne({ id: record.doc_file_ref.id }).set({
            'file_url': outcome.public_url,
        });
        return ResponseService.successResponse(res, { document: "Document Signed successfully" });
    },

    getDocumentOfSign: async (req, res) => {
        let documentId = +req.param('documentId');
        let employeeId = +req.param('employeeId');
        let unsignedOnly = (req.param('all', 'false') === 'false');
        sails.log.info("Requested to get all documents? ", !unsignedOnly);
        let documentForSign = documentId ?
            await sails.models.userdocumentsign_reader.findOne({ id: documentId, employee_ref: employeeId }).populate('doc_file_ref').populate('employee_ref') :
            await sails.models.userdocumentsign_reader.find({
                where: {
                    employee_ref: employeeId,
                    ...(unsignedOnly ? { status: false } : {})
                },
                sort: ['createdAt DESC']
            }).populate('doc_file_ref').populate('employee_ref');

        if (!documentForSign) {
            return errorResponse(res, `NO document found for employee ${employeeId}`);
        }

        return ResponseService.successResponse(res, { document: documentForSign });
    },


    updateUserEmpDetail: async (req, res) => {
        let employmentId = +req.param('empDetailId');
        let userId = +req.param('userId');
        let filter = {
            id: employmentId,
            user_ref: userId
        };
        sails.log.info(`Updating user employment detail with filter: `, filter);
        let payload = _.pick((req.body || {}), [
            'comment',
        ]);
        let updated_employment_detail = await sails.models.userempdetail.updateOne(filter).set(payload);


        sails.log.info('updated successfully', updated_employment_detail ? updated_employment_detail.id : null);
        ResponseService.successResponse(res, { user_employment_detail: updated_employment_detail });
    },

    saveUsrEmplAndPsnlDetail: async (req, res) => {
        let employmentData = req.body.employment_data;
        let personalData = req.body.personal_data;

        let record = await sails.models.inndexsetting_reader.findOne({ name: 'exclusion_by_country_code' });
        let validatorList = {};
        if (record && record.value && personalData && personalData.country_code) {
            validatorList.activeExclusions = (record.value[personalData.country_code] || {}).exclude || [];
            validatorList.globalExclusions = (record.value['ALL'] || {}).exclude || [];
            validatorList.activeMandatoryItems = (record.value[personalData.country_code] || {}).mandatory || [];
            validatorList.globalMandatoryItems = (record.value['ALL'] || {}).mandatory || [];
            validatorList.activeOptionalItems = (record.value[personalData.country_code] || {}).optional;
            validatorList.globalOptionalItems = (record.value['ALL'] || {}).optional;
        }
        let { validationError: empValidationErr, payload: employmentPayload } = userEmploymentValidate(employmentData, validatorList);
        if (empValidationErr) {
            return ResponseService.errorResponse(res, sails.__('validation error message'), { validationError: empValidationErr });
        }
        let { validationError, payload: personalPayload } = userPersonalDetailValidate(personalData);
        if (validationError) {
            return ResponseService.errorResponse(res, sails.__('validation error message'), { validationError });
        }

        personalPayload = { ...personalPayload, user_onboard_status: getUpdatedOnboardStatus(req.user, 'employment') };
        let user = await sails.models.user.updateOne({ id: req.user.id }).set(personalPayload);
        req.user = user;
        let employment_detail = await saveUserEmploymentDetail(req.user.id, employmentPayload, req.user);
        createUserRevision(req.user.id, { personal: user, employment: employment_detail }).catch(sails.log.error);
        let extendedUser = await sails.models.user.findOne({
            id: user.id
        }).populate('profile_pic_ref').populate('parent_company').populate('user_roles');
        return ResponseService.successResponse(res, { employment_detail, user: extendedUser });
    },
};
