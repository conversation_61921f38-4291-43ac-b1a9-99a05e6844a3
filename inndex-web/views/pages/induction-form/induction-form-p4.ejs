<div class="wrapper last">
    <% if (type === 'html') { %>
        <%- include('./form-header'); -%>
    <% } %>
    <section class="padding-sm pd-top-25" style="page-break-inside: avoid;">
        <% if(induction_request.induction_question_answers.additional_qa && induction_request.induction_question_answers.additional_qa.length) { %>
            <div class="table">
                <h2>
                    <i class="fas fa-info-circle"></i> <%= induction_request.induction_question_answers.additional_qa_title %>
                </h2>
                <table class="table wd-60 bg-highlight">
                    <tbody>
                    <% induction_request.induction_question_answers.additional_qa.forEach(function(item, i){ %>
                        <tr>
                            <td class="no-dot">
                                <span><b>Q.</b> <%= item.question %> </span><br>
                            </td>
                            <% if(item.ans_field_type === 'date' && item.ans_epoch) { %>
                                <td class="no-dot text-right">
                                    <p style="white-space: pre-line"><b>A.</b> <%= moment(+item.ans_epoch).format('DD/MM/YYYY') %></p>
                                </td>
                            <% } else { %>
                                <td class="no-dot text-right">
                                    <p style="white-space: pre-line"><b>A.</b> <%= item.answer %></p>
                                </td>
                            <% } %>
                        </tr>
                        <% if(induction_request.induction_question_answers.additional_qa[i].sub_questions && induction_request.induction_question_answers.additional_qa[i].sub_questions.length) { %>
                            <% induction_request.induction_question_answers.additional_qa[i].sub_questions.forEach(function(subItem, j) { %>
                                <% if((induction_request.induction_question_answers.additional_qa[i].sub_questions[j].que_condition).toString().trim().toLowerCase() === ('If ' + induction_request.induction_question_answers.additional_qa[i].answer).toString().trim().toLowerCase()) { %>
                                    <tr>
                                        <td class="no-dot">
                                            <span><b>Q.</b> <%= subItem.question %> </span><br>
                                        </td>
                                        <% if(subItem.ans_field_type === 'date' && subItem.ans_epoch) { %>
                                            <td class="no-dot text-right">
                                                <p style="white-space: pre-line"><b>A.</b> <%= moment(+subItem.ans_epoch).format('DD/MM/YYYY') %></p>
                                            </td>
                                        <% } else { %>
                                            <td class="no-dot text-right">
                                                <p style="white-space: pre-line"><b>A.</b> <%= subItem.answer %></p>
                                            </td>
                                        <% } %>
                                    </tr>
                                <% } %>
                            <% }); %>
                        <% } %>
                    <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <div class="table-outer">
            <% if(induction_request.induction_answers.length > 0) { %>
                <div class="table">
                    <h2><i class="fas fa-question-circle"></i> <%= project.custom_field.induction_phrase_singlr %> Quiz </h2>
                    <table class="table wd-60 bg-highlight">
                        <tbody>
                        <% induction_request.induction_answers.forEach(function(answer, i){ %>
                            <tr>
                                <td class="no-dot">
                                    <span><b>Q.</b> <%= induction_request.induction_question_answers.questions[i].question %> </span><br>

                                </td>
                                <td class="no-dot text-right">
                                    <span><b>A.</b> <%= answer %></span>
                                </td>
                            </tr>
                        <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } %>
            <% if(induction_request.declarations && induction_request.declarations.supervisor && induction_request.declarations.supervisor.length) { %>
                <h2><i class="fas fa-binoculars"></i> Supervisor Declaration</h2>
                <table class="table decleration">
                    <tbody>
                    <% induction_request.declarations.supervisor.forEach(function(declaration, i){ %>
                        <tr>
                            <td class="no-dot"><%- (i + 1) + '.' %> <%= declaration.question %></td>
                            <td class="no-dot text-right">
                                <img style="max-width: 18px;"
                                     src="data:image/png;base64,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"/>
                            </td>
                        </tr>
                    <% }); %>
                    </tbody>
                </table>
            <% } %>

            <% if(induction_request.declarations && induction_request.declarations.operator && induction_request.declarations.operator.length) { %>
                <h2>
                    <svg style="margin-left: 5px;max-height: 18px; vertical-align: middle;" height="16px;"
                         viewBox="0 0 512 512.001" width="16px" xmlns="http://www.w3.org/2000/svg">
                        <g>
                            <circle cx="256" cy="271" r="15"/>
                            <path d="m256 0c-140.958 0-256 115.049-256 256 0 140.961 115.049 256 256 256 140.96 0 256-115.049 256-256 0-140.958-115.049-256-256-256zm0 61c97.323 0 178.219 71.668 192.692 165h-52.019c-32.982 0-64.318-13.235-85.973-36.311-29.6-31.546-79.797-31.556-109.4-.001-21.655 23.077-52.991 36.312-85.974 36.312h-52.018c14.473-93.332 95.369-165 192.692-165zm0 255c-24.812 0-45-20.186-45-45s20.188-45 45-45c24.814 0 45 20.186 45 45s-20.186 45-45 45zm-181.58 11.097c17.17-6.736 29.568-11.097 46.58-11.097 54.688 0 97.219 48.734 88.981 103.49h-.035c-1.224 8.381-3.632 16.502-7.105 24.145-58.759-16.67-106.31-60.262-128.421-116.538zm234.751 116.534c-3.568-7.845-5.924-15.965-7.117-24.141h-.035c-9.599-63.805 49.301-116.452 110.354-100.936 6.436 1.636 14.49 4.34 25.206 8.544-22.109 56.271-69.655 99.861-128.408 116.533z"/>
                        </g>
                    </svg>
                    Plant/Machinery Operator Declaration
                </h2>
                <table class="table wd-70 decleration">
                    <tbody>
                    <% induction_request.declarations.operator.forEach(function(declaration, i){ %>
                        <tr>
                            <td class="no-dot"
                                colspan="<%= (declaration.type === 'radio') ? 11 : 6 %>"><%- (i + 1) + '.' %> <%= declaration.question %></td>
                            <td colspan="<%= (declaration.type === 'radio') ? 1 : 6 %>"
                                class="no-dot text-right bold <%= declaration.type === 'radio' ? 'capital' : '' %> <%= (declaration.type === 'radio' && declaration.answer && declaration.answer.toString().toLowerCase() === 'no') ? 'text-danger' : '' %>"
                                style="width: <%= (declaration.type === 'radio') ? 10 : 50 %>%;">
                                <%= declaration.answer %>
                            </td>
                        </tr>
                    <% }); %>
                    </tbody>
                </table>
            <% } %>

            <% if (!is_shadow_user) { %>
                <h2><i class="fas fa-file-signature"></i> Declaration</h2>
                <table class="table decleration">
                    <tbody>
                    <% declarations.forEach(function(declaration, i){ %>
                        <tr>
                            <td class="no-dot"><%- (i + 1) + '.' %> <%= declaration.content %></td>
                            <td class="no-dot text-right">
                                <img style="max-width: 18px;"
                                     src="data:image/png;base64,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"/>
                            </td>
                        </tr>
                    <% });
                        var last_index = declarations.length;
                    %>
                    <% if(numberToYesNo(induction_request.confirm_detail_valid) === 'Yes') { %>
                        <tr>
                            <td class="no-dot"><%- (last_index = last_index + 1) + '.' %> I confirm that I have reviewed
                                all information provided in my profile and on this form and agree it is correct and up
                                to date.
                            </td>
                            <td class="no-dot text-right">
                                <img style="max-width: 18px;"
                                     src="data:image/png;base64,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"/>
                            </td>
                        </tr>
                    <% } %>
                    <% if(project.has_media_content && induction_request.accepting_media_declaration) { %>
                        <tr>
                            <td class="no-dot"><%- (last_index = last_index + 1) + '.' %> <%= project.media_declaration_content %>
                                <% if(project.media_file_ids && project.media_file_ids.length && project.media_file_ids[0].id){ %>
                                    <i class="small">(<%= project.media_file_ids[0].name %>)</i>
                                <% } %>
                            </td>
                            <td class="no-dot text-right">
                                <img style="max-width: 18px;"
                                     src="data:image/png;base64,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"/>
                            </td>
                        </tr>
                    <% } %>
                    </tbody>
                </table>
            <% } %>
        </div>

        <% if(induction_request.comments && induction_request.comments.filter(c => c.origin != 'system').length) { %>
            <div class="table-outer">
                <h2><i class="fas fa-comment"></i> Comments</h2>
                <table class="table wd-80 bg-highlight">
                    <tbody>

                    <% induction_request.comments.forEach(function(comment, i){ %>
                        <% if(comment.origin !== 'system' && comment.origin !== 'rams_briefing'){ %>
                            <tr>
                                <td class="no-dot">
                                    <b><%= comment.name %></b>:<i><%= comment.note %></i>
                                </td>
                                <td class="no-dot text-right">
                                    <%= momentTz(+comment.timestamp).tz(timezone).format(dateTimeFormat) %>
                                </td>
                            </tr>
                        <% } %>
                        <% if(comment.origin == 'rams_briefing') { %>
                            <tr>
                                <td class="no-dot" style="width: 40%">
                                    <i><%= comment.note %></i>
                                </td>
                                <td class="no-dot text-left" style="width: 60%">
                                    <% if(comment.signature) { %>
                                        <% let sign = comment.signature.startsWith('data:image/png;base64,') ? comment.signature : 'data:image/png;base64,' + comment.signature; %>
                                        <img height="30" width="100" src="<%= sign %>"/>
                                    <% } %>
                                </td>
                            </tr>
                        <% } %>
                    <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>

        <table class="table wd-50">
            <tbody>
            <tr>
                <td class="no-dot bold" style="padding-bottom: 0;">Name:
                    <b><%- `${user.first_name ? user.first_name : ''} ${user.middle_name ? user.middle_name : ''} ${user.last_name ? user.last_name : ''}`.trim() %></b>
                </td>
                <td class="no-dot text-right bold">
                    Submitted: <%- momentTz(+induction_request.createdAt).tz(timezone).format(dateTimeFormat) %></td>
            </tr>
            <% if(induction_request.user_sign && induction_request.user_sign.length){ %>
                <tr>
                    <td class="no-dot bold sign-row" style="padding-top: 0;" colspan="2">
                        <span>Signature:</span>
                        <img class="sign" style="max-height: 40px" src="<%- induction_request.user_sign %>"/>
                    </td>
                </tr>
            <% } %>
            <% if(inductor){ %>
                <tr>
                    <td class="no-dot bold">Inductor:
                        <b><%- (inductor.name && inductor.name.length) ? inductor.name : `${inductor.first_name ? inductor.first_name : ''} ${inductor.middle_name ? inductor.middle_name : ''} ${inductor.last_name ? inductor.last_name : ''}`.trim() %></b>
                    </td>
                    <td class="no-dot text-right bold">
                        <%- status_message || "Approved" %>: <%- accepted_at ? momentTz(+accepted_at).tz(timezone).format(dateTimeFormat) : '' %></td>
                </tr>
            <% } %>
            <% if(induction_request.induction_slot && induction_request.induction_slot.seconds){ %>
                <tr>
                    <td class="no-dot bold"><%= project.custom_field.induction_phrase_singlr %> Booking:
                        <%- induction_request.induction_slot.seconds ? momentTz.unix(+induction_request.induction_slot.seconds).tz(timezone).format(dateTimeFormat) : '' %>
                        <%- (induction_request.induction_slot.location) ? `(${induction_request.induction_slot.location})` : '' %>
                    </td>
                    <td class="no-dot text-right bold"></td>
                </tr>
            <% } %>
            <% (induction_request.re_review_logs || []).forEach(function(review_log, i){ %>
                <tr>
                    <td class="no-dot bold">Re-review By: <b><%- (review_log.reviewer_name) %></b> <br/>
                        <small style="display: inline-block;padding-left: 20px;"> <%- (review_log.comment_lines || []).join('<br/>') %> </small>
                    </td>
                    <td class="no-dot text-right bold">Date &
                        Time: <%- review_log.reviewed_on ? momentTz(+review_log.reviewed_on).tz(timezone).format(dateTimeFormat) : '' %></td>
                    </td>
                </tr>
            <% }); %>
            </tbody>
        </table>

        <% if (!is_shadow_user) { %>
            <table class="table hide-in-popup">
                <tbody>
                <% if (project && project.further_policies) { %>
                    <% project.further_policies.forEach(function(further_policy, i){ %>
                        <tr>
                            <% if (!further_policy.is_text && further_policy.policy_ref && further_policy.policy_ref.length) { %>
                                <td class="no-dot wrap-html small">
                                    <% further_policy.policy_ref.forEach(function(file, i) { %>
                                        <b><%= further_policy.policy_name %> policy:</b> <a href="<%= file.file_url %>"
                                                                                            target="_blank"><%= file.name %></a>
                                    <% }); %>
                                </td>
                            <% } else { %>
                                <td class="no-dot wrap-html small">
                                    <b><%= further_policy.policy_name %> policy:</b>
                                    <i><%- (further_policy.policy || '').replace(/(<([^>]+)>)/ig, '') %></i>
                                </td>
                            <% }; %>
                        </tr>
                    <% }); %>
                <% } %>
                </tbody>
            </table>
        <% } %>

        <div class="table-outer">
            <% if (!is_shadow_user && cscs_info && cscs_info.fetchedAt) { %>
                <h2><i class="fas fa-file-signature"></i> Competency Verification</h2>
                <table class="table decleration">
                    <tbody>
                        <tr>
                            <td class="no-dot">1. <b>CSCS</b></td>
                        </tr>
                        <% if(cscs_info.isValid) { %>
                            <tr>
                                <td class="no-dot">
                                    <div style="display: inline-block;    vertical-align: top;    padding-right: 10px;">
                                        <img style="max-width: 18px;"
                                             src="data:image/jpeg;base64,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"/>
                                    </div>
                                    <div style="display: inline-flex; width: calc(100% - 36px);">
                                        <div style="flex: 1 1 auto;">
                                            <div class="bold" style="color: #28a745;">Validated by CSCS Smart Check on <%= momentTz(+cscs_info.fetchedAt).tz(timezone).format(dateFormat) %> at <%= momentTz(+cscs_info.fetchedAt).tz(timezone).format(timeFormat) %></div>
                                            <ul style="list-style: none;padding: 0;margin-top: 5px;font-size: 12px;display: inline-block;">
                                                <li><label class="bold">Card type:</label> <%= cscs_info.cardType %></li>
                                                <li><label class="bold">Scheme:</label> <%= cscs_info.scheme %> <% if(cscs_info.schemeId) { %>(<%= cscs_info.schemeId %>) <% } %></li>
                                                <li><label class="bold">Qualifications:</label> <%= (cscs_info.qualifications || []).join(', ') %></li>
                                                <li><label class="bold">Date of expiry:</label> <%= cscs_info.dateOfExpiry ? momentTz(cscs_info.dateOfExpiry, 'YYYY-MM-DD').format(dateFormat) : '' %></li>
                                                <li><label class="bold">Registration number:</label> <%= cscs_info.docNumber %></li>
                                            </ul>
                                        </div>
                                        <div style="max-width: 150px; flex: 1 1 auto;">
                                            <img style="max-height: 100px;" src="<%= cscs_info.photo %>" />
                                        </div>

                                    </div>
                                </td>
                            </tr>
                        <% } else{ %>
                        <tr>
                            <td class="no-dot">
                                <div style="display: inline-block;    vertical-align: top;    padding-right: 10px;">
                                    <img style="max-width: 18px;"
                                         src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYEAYAAACw5+G7AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAABgAAAAYADwa0LPAAAAB3RJTUUH5gQMCBkPtjOudgAAAiFJREFUWMPVWMFqwkAQfTEB/QEreu6xWIRexINX7bFYeumXlPoH0qPeS/0NEfSsoNJTvQtq2kMSpMRMD+Misaa7iamte3nssjt5b3Z2drIaYmpEAKDr3MtkQABwfg4NANJpHp9Oefz1VUsAwOdnXN+PSPj6mvHlhdE0GYl+RttmHI8Z6/UjES4UGHs9NaJh0TTJA4Db2/iIbwz6PRc38V30PMZW60CP3935Df428T3oAcDTU0jiIlQc58+I7xVSqykK6Pflhg3DMIiIkslkMgoxsU7YUVn38SEhLrKKnLjjDIfDIZFtz2azGZHrlkqlkpyImCfWCTtEuq7rqjvx+BggoN2WC0ilUqktActarVYrItteLBaLYCFb4jxvu47tCLtqAqbTHeK6Hi6PE7lusVgsEtn2fD6fbwlZlmmaJpHrlsvlsvq80FnKAwDD2AjI5aIermDPcj9oXDXkft6JfH6T56+uDs0SwZ6Ow+NBAu7vE1yr8PBJNQ0A1usN9Wz2NEPo4uL0D7Fo/qryv6fRt7dvIcUTqlW5AL5wHGcwGAziuMjYjvJFRgDw8BB4NnhCp6Mq5PBSIgzx93fp4eYturw8bvmsEPMEADc3UgF+IeI/4K/L6UZDmfh+IbUao2Ud1+PNZmTi+4Xk82y42/0d4stl6FCJLIgAoFJhfH72E5CFhNjJ0UiaVSRNi1dQIsG93WeVszMeF88qkwk/q7juod/9AiNJ7S+bVn92AAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIyLTA0LTEyVDA4OjI1OjE1KzAwOjAwsXbCYQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMi0wNC0xMlQwODoyNToxNSswMDowMMAret0AAAAASUVORK5CYII="/>
                                </div>
                                <div style="display: inline-flex; width: calc(100% - 36px);">
                                    <div style="flex: 1 1 auto;">
                                        <div class="bold" style="color: #dc3545;">Unable to verify by CSCS Smart Check</div>
                                        <ul style="list-style: none;padding: 0;margin-top: 5px;font-size: 12px;display: inline-block;">
                                            <li>We have been unable to verify the submitted competency, please ask the worker to re-submit a valid CSCS competency.</li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } %>
        </div>
    </section>
    <% if (type === 'html') { %>
        <%- include('./form-footer', {totalPages: 4, currentPageNo: 4}); -%>
    <% } %>
</div>
