
const isFieldExcluded = (field, validatorList = {}) => {
    if (validatorList.activeMandatoryItems?.includes(field)) return false;
    if (!validatorList.activeExclusions?.includes(field) && validatorList.globalMandatoryItems?.includes(field)) return false;
    if (validatorList.activeExclusions?.includes(field) || validatorList.globalExclusions?.includes(field)) return true;
    return false;
};

const isVisiblePostCode = (identifier, validatorList) => {
    if(validatorList?.visibility[identifier]) return validatorList.visibility[identifier];
    if(validatorList?.globalVisibility[identifier]) return validatorList.globalVisibility[identifier];
    return {type:'postcode-lookup'};
}

const profileMedAssessmentExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.medical-assessment', validatorList);
};

const profileHealthAssessmentExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.health-assessment', validatorList);
};

const inductionHealthAssessmentExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.health-assessment', validatorList);
};

const profileEmplymentNinExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.employment.nin', validatorList);
};


const profileEmplymentMinWageExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.employment.min_wage', validatorList);
};

const isProfileEmploymentEmpNbrMandatory = (validatorList = {}) => {
    return isFieldExcluded('profile.employment.employee_number', validatorList);
};


const profileEmplymentTypeExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.employment.employment_type', validatorList);
};


const profileEmplymentStartDateExcluded = (validatorList = {}) => {
    return isFieldExcluded('profile.employment.start_date', validatorList);
};

const checkProjectPostcodeLookup = (validatorList = {}) => {
    return isVisiblePostCode(`project.postcode`, validatorList);
}

module.exports = {

    profileMedAssessmentExcluded,

    profileHealthAssessmentExcluded,

    inductionHealthAssessmentExcluded,

    profileEmplymentNinExcluded,

    profileEmplymentMinWageExcluded,

    profileEmplymentTypeExcluded,

    profileEmplymentStartDateExcluded,

    isProfileEmploymentEmpNbrMandatory,

    checkProjectPostcodeLookup
};
