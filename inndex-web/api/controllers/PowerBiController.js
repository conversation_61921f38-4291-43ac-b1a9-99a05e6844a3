const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
dayjs.extend(utc);
const url = require('url');
const { v4: uuid4 } = require('uuid');

const {
    fall_back_timezone,
} = sails.config.constants;
const {
    ResponseService: {
        errorObject,
        sendResponse,
        errorResponse,
        successResponse
    },
    HttpService: {
        makeGET,
        makePOST,
        fetchUrlAs,
    },
    SharedService: {
        s3UploaderWithExpiry
    }
} = require('./../services');

const dashboardToolTitle = {
    "close_call": "Close Call",
    "close_call_company": "Close Call",
    "good_call": "Good Call",
    "good_call_company": "Good Call",
    "incident_reports": "Incident Report",
    "incident_reports_company": "Incident Report",
    "observation": "Observation",
    "observation_company": "Observation",
    "inspections": "Inspection",
    "inspections_company": "Inspection",
    "usage_reports": "Usage Report",
    "permits": "Permit",
    "toolbox_talks": "Toolbox Talks",
}

const singularPhraseKeys = {
    "Close Call": "cc_phrase_singlr",
    "Good Call": "gc_phrase_singlr",
    "Observation": "obrs_phrase_singlr",
    "Permit": "permit_phrase_singlr",

    "Collection Note": "cn_phrase_singlr",
    "Daily Activity": "da_phrase_singlr",
    "Delivery Note": "dn_phrase_singlr",
    "Inductions": "induction_phrase",
    "Task Briefing": "tb_phrase_singlr",
    "ITP": "qcl_phrase_singlr",
    "Work Package Plan": "wpp_phrase_singlr",
    "RAMS": "rams_phrase_singlr",
    "POWRA": "powra_phrase_singlr",
    "Take 5": "take5_phrase_singlr",
    "Assets Management": "assets_phrase_singlr",
    "Induction": "induction_phrase_singlr"
};

const {
    POWERBI_APIs,
} = sails.config.constants;

const {
    POWERBI_INNDEX_TENANT_ID,
    POWERBI_CLIENT_ID,
    POWERBI_CLIENT_SECRET,
    POWERBI_GROUP_ID,
    POWERBI_IDENTITY_ROLE,
    POWERBI_IDENTITY_COMPANY_ROLE,
} = sails.config.custom;

const getAccessToken = async () => {
    //check if access token in DB else generate token and save in database
    let record = await sails.models.inndexsetting_reader.findOne({name: POWERBI_APIs.INNDEX_SETTING_NAME});
    let accessToken = '';
    let isTokenExist = false;
    if (record && record.value && record.value.hasOwnProperty('access_token')) {
        isTokenExist = true;
        accessToken = record.value.access_token;
        if (record.value.token_expire_at < dayjs().valueOf()) {
            isTokenExist = false;
        }
    }

    if (!accessToken || !isTokenExist) {
        let accessTokenResponse = await makePOST(`${POWERBI_APIs.AUTH_SERVER_URL}/${POWERBI_INNDEX_TENANT_ID}/${POWERBI_APIs.OAUTH_TOKEN_URL}`, new url.URLSearchParams({
            "grant_type": POWERBI_APIs.GRANT_TYPE,
            "client_id": POWERBI_CLIENT_ID,
            "client_secret": POWERBI_CLIENT_SECRET,
            "scope": POWERBI_APIs.SCOPE
        }));
        sails.log.info("getting new access token");
        let {error, error_description} = accessTokenResponse;
        if (error) {
            sails.log.error(error_description);
            return {errorMessage: error_description};
        }

        if (!accessTokenResponse) {
            sails.log.error("error_description");
            return {errorMessage: error_description};
        }

        if (record.id) {
            record.value.token_expire_at = dayjs().add(accessTokenResponse.data.expires_in - 99, 'second').valueOf(); //assume expire before 99s
            record.value.access_token = accessTokenResponse.data.access_token;
            await sails.models.inndexsetting.updateOne({
                id: record.id,
                name: POWERBI_APIs.INNDEX_SETTING_NAME
            }).set({value: record.value});
        } else {
            await sails.models.inndexsetting.create({
                name: POWERBI_APIs.INNDEX_SETTING_NAME,
                value: {
                    token_expire_at: dayjs().add(accessTokenResponse.data.expires_in - 99, 'second').valueOf(), //assume expire before 99s
                    access_token: accessTokenResponse.data.access_token
                }
            });
        }

        return accessTokenResponse.data.access_token;
    }

    return accessToken;
};

const getDataSourcesName = async (reportDatasetId, accessToken) => {
    let dataSourcesResponse = await makeGET(`${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/datasets/${reportDatasetId}/datasources`, {}, {
        'Authorization': `Bearer ${accessToken}`
    });

    let dataSourcesName = [];
    let dataSourcesArr = (dataSourcesResponse.data.value || []).reduce((arr, source) => {
        if (source.datasourceType === 'AnalysisServices' && source.connectionDetails.database) {
            dataSourcesName.push(source.connectionDetails.database)
            arr.push({
                "datasourceType": source.datasourceType,
                "connectionDetails": source.connectionDetails
            });
        }
        return arr;
    }, []);
    return {dataSourcesName, dataSourcesArr};
}

const getReportDetail = async (accessToken, toolName) => {
    //Fetch list of Reports and find report by toolName
    sails.log.info("fetch all power bi reports.");
    let reports = await makeGET(`${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/reports`, {}, {
        'Authorization': `Bearer ${accessToken}`
    });

    if (reports.error) {
        sails.log.error(reports.message);
        return errorObject(reports.message);
    } else if(reports.data.value && !reports.data.value.length) {
        sails.log.info("No report found or group id is invalid.");
        return errorObject("No report found or group id is invalid.");
    }

    sails.log.info(`fetch power bi report for tool ${toolName}.`);
    return (reports.data.value || []).find(item => item.name === toolName);
}

const getReportEmbedInfo = async (req, toolName, username) => {
    sails.log.info("get power bi access token.", `username: ${username}`);
    let accessToken = await getAccessToken();

    if (accessToken.errorMessage) {
        return errorObject(accessToken.errorMessage);
    }

    let identityRole = (['usage_reports', 'inspections_company'].includes(toolName)) ? POWERBI_IDENTITY_COMPANY_ROLE :  POWERBI_IDENTITY_ROLE;
    let reportDetail = await getReportDetail(accessToken, toolName);
    if (!reportDetail) {
        sails.log.info(`No report found for tool ${toolName}`);
        return errorObject(`No report found for tool ${toolName}`);
    }

    //Generate an Embed Token
    sails.log.info(`generate power bi embed token.`);
    let body = {
        "datasets": [{"id": reportDetail.datasetId, "xmlaPermissions": "ReadOnly"}],
        "reports": [{"id": reportDetail.id}]
    };

    body.identities = [];
    if (username && toolName !== 'inspections_company') {
        body.identities = [{
            "username": username,
            "roles": [identityRole],
            "datasets": [reportDetail.datasetId]
        }];
    }

    let {dataSourcesName, dataSourcesArr} = await getDataSourcesName(reportDetail.datasetId, accessToken);
    let sourcesDatasetIds = [];
    if (dataSourcesName.length) {
       let datasets = await makeGET(`${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/datasets`, {}, {
           'Authorization': `Bearer ${accessToken}`
       });

       (datasets.data.value).forEach((dataset) => {
           if (dataSourcesName.includes(dataset.name)) {
               sourcesDatasetIds.push(dataset.id);
           }
       });

        const base64IdentityBlob = Buffer.from(accessToken).toString('base64');

        body.datasourceIdentities = [{
            "datasources": dataSourcesArr,
            "identityBlob": base64IdentityBlob
        }];

        (sourcesDatasetIds).forEach((datasetId) => {

            body.datasets.push({
                "id": datasetId,
                "xmlaPermissions": "ReadOnly"
            });

            body.identities.push({
                "username": username,
                "roles": [identityRole],
                "datasets": [ datasetId ]
            });
        })
    }

    sails.log.info("power-bi embed: ", body, `${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/reports/${reportDetail.id}/GenerateToken`);
    let embedTokenResponse = await makePOST(`${POWERBI_APIs.API_URL}/GenerateToken`,
        body, {
            'Authorization': `Bearer ${accessToken}`
        });

    if (embedTokenResponse.error) {
        sails.log.error(embedTokenResponse.error.message);
        return errorObject(embedTokenResponse.error.message);
    }

    return {
        embedToken: embedTokenResponse.data.token,
        embedUrl: reportDetail.embedUrl,
        reportId: reportDetail.id
    }
}

const isDashboardOnline = () => {
    // Get the current time
    const now = dayjs().tz(fall_back_timezone);

    // Set the start and end times for your range (7pm to 7am)
    const start = dayjs().tz(fall_back_timezone).hour(19).minute(0).second(0); // 7pm
    const end = dayjs().tz(fall_back_timezone).hour(7).minute(0).second(0);    // 7am next day

    // Check if the current time is between 7pm to 7am
    if (now.isAfter(start) || now.isBefore(end)) {
        sails.log.info("Dashboards are currently offline.", start, end, now);
        return false;
    }
    sails.log.info("Dashboards are currently online.", start, end, now);

    return true;
}

const exportDashboardToolReport = async (req, res) => {
    let reqBody = req.body;
    let portalId = +req.param('projectId') || +req.param('companyId') || 0;
    let portalType = +req.param('projectId') ? 'project' : 'company';
    let toolName = req.param('toolName') || '';
    sails.log.info(`Power Bi export ${portalType} ${portalId}: get access token.`);
    let accessToken = await getAccessToken();

    if (!accessToken || accessToken.errorMessage) {
        sails.log.info(`Power Bi export ${portalType} ${portalId}: Something went wrong while generating access token.`);
        return errorResponse(res, `Something went wrong while generating access token.`);
    }


    let identityRole = (['usage_reports', 'inspections_company'].includes(toolName)) ? POWERBI_IDENTITY_COMPANY_ROLE :  POWERBI_IDENTITY_ROLE;
    let reportDetail = await getReportDetail(accessToken, toolName);
    if (!reportDetail) {
        sails.log.info(`Power Bi export ${portalType} ${portalId}: No report found for tool ${toolName}`);
        return errorObject(`No report found for tool ${toolName}`);
    }

    let body = {
        "format": "PDF",
        "powerBIReportConfiguration": {}
    };

    if (reqBody && reqBody.bookmarkState) {
        body.powerBIReportConfiguration.defaultBookmark = {
            state: reqBody.bookmarkState
        }
    }

    body.powerBIReportConfiguration.identities = [];
    if (portalId && toolName !== 'inspections_company') {
        body.powerBIReportConfiguration.identities = [{
            "username": (+reqBody.toolId) ? `project_ref=${portalId}&ib_ref=${+reqBody.toolId}` : portalId,
            "roles": [identityRole],
            "datasets": [reportDetail.datasetId]
        }];
    }

    let {dataSourcesName, dataSourcesArr} = await getDataSourcesName(reportDetail.datasetId, accessToken);
    let sourcesDatasetIds = [];
    if (dataSourcesName.length) {
        let datasets = await makeGET(`${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/datasets`, {}, {
            'Authorization': `Bearer ${accessToken}`
        });

        (datasets.data.value).forEach((dataset) => {
            if (dataSourcesName.includes(dataset.name)) {
                sourcesDatasetIds.push(dataset.id);
            }
        });

        (sourcesDatasetIds).forEach((datasetId) => {
            body.powerBIReportConfiguration.identities.push({
                "username": portalId,
                "roles": [identityRole],
                "datasets": [ datasetId ]
            });
        })
    }

    sails.log.info(`Power Bi export api body: `, body);
    let exportToResponse = await makePOST(`${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/reports/${reportDetail.id}/exportTo`,
        body, {
            'Authorization': `Bearer ${accessToken}`
        });

    if(!exportToResponse.success || !exportToResponse.data || !exportToResponse.data.id) {
        sails.log.info(`Power Bi export ${portalType} ${portalId}: unable to generate reports.`);
        return errorResponse(res, `Something went wrong while generating report, please try later`);
    }

    let resourceLocation = `${POWERBI_APIs.API_URL}/groups/${POWERBI_GROUP_ID}/reports/${exportToResponse.data.reportId}/exports/${exportToResponse.data.id}/file`;
    if (exportToResponse.data.percentComplete === 100) {
        let fileResponse = await fetchUrlAs(resourceLocation, 'arraybuffer', {}, {
            'Authorization': `Bearer ${accessToken}`
        });
        if (fileResponse.success && fileResponse.status === 200) {
            sails.log.info(`Power Bi export ${portalType} ${portalId}: report available to download.`);
            let fileName = `${portalType}-${uuid4()}-${dashboardToolTitle[toolName]}-Report.pdf`;

            let s3UploadResponse = await s3UploaderWithExpiry(`powerbi-report/pdf/${dayjs().add(10, 'seconds').valueOf()}/${fileName}`, fileResponse.data, 'application/pdf', 'public-read', 1);
            sails.log.info(`Power Bi export ${portalType} ${portalId}: uploading file to s3.`);

            if (s3UploadResponse.Location || s3UploadResponse.location) {
                return successResponse(res, {
                    report_file: {
                        file_url: s3UploadResponse.public_url || s3UploadResponse.Location,
                        file_name: fileName
                    }
                });
            }
        }
    } else {
        let portal_name = '';
        let dashboardTitle = dashboardToolTitle[toolName];
        if (portalType === 'project') {
            let project = await sails.models.project_reader.findOne({
                select: ['name', 'custom_field'],
                where: {id: portalId}
            });
            portal_name = project.name;
            let singularPhraseKey = singularPhraseKeys[toolName];
            dashboardTitle = (project.custom_field[singularPhraseKey]) ? project.custom_field[singularPhraseKey] : dashboardTitle;
        } else {
            let employer = await sails.models.createemployer_reader.findOne({
                select: ['name'],
                where: {id: portalId}
            });
            portal_name = employer.name;
        }

        const expireEpoch = dayjs.utc(exportToResponse.data.expirationTime).valueOf(); // Milliseconds since epoch

        let createBody = {
            "emails": [],
            "file_location": resourceLocation,
            "expiration_time": (expireEpoch > 1) ? expireEpoch : dayjs().add(5, 'hours').valueOf(),
            "status": 0,
            "additional_detail": {
                "portal_type": portalType, "portal_id": portalId, portal_name, "tool_name": dashboardTitle
            }
        }

        let exportQueueRecord = await sails.models.powerbireportexportqueue.create(createBody);
        sails.log.info(`Power Bi export ${portalType} ${portalId}: queue created, id: ${exportQueueRecord.id}`);
        return successResponse(res, {queue_item_id: exportQueueRecord.id});
    }

    sails.log.info(`Power Bi export ${portalType} ${portalId}: unable to generate reports.`);
    return errorResponse(res, `Something went wrong while generating report, please try later`);
}

const updateExportQueue = async (req, res) => {
    let portalId = +req.param('projectId') || +req.param('companyId') || 0;
    let portalType = +req.param('projectId') ? 'project' : 'company';
    let id = +req.params.id;
    let emails = req.body.emails || [];

    sails.log.info(`Power Bi export ${portalType} ${portalId}: updating export job with id:  ${id}`);
    if (emails && !emails.length) {
        sails.log.info(`Power Bi export ${portalType} ${portalId}: Invalid request, atleast one email address is required.`);
        return errorResponse(res, `Invalid request, atleast one email address is required.`);
    }

    let exportQueueItem = await sails.models.powerbireportexportqueue.updateOne(id).set({
        "emails": emails,
    });

    sails.log.info(`Power Bi export ${portalType} ${portalId}: emails have been added with export job.`);
    return successResponse(res, {'message': 'The report will be sent shortly to the provided email address(es).', exportQueueItem});
}

module.exports = {
    getProjectToolReport: async (req, res) => {
        let toolId = +req.param('toolId');
        sails.log.info("toolId: ", toolId);
        /*let dashboardStatusFlag = isDashboardOnline();
        if (!dashboardStatusFlag) {
            return errorResponse(res, 'Dashboards are currently available between 7am - 7pm (UK)', {dashboard_offline: true});
        }*/

        let projectId = +req.param('projectId');
        let toolName = req.param('toolName');
        if (!toolName) {
            sails.log.info('Tool name is required to generate the report.');
            return errorResponse(res, `Tool name is required to generate the report, ${projectId}`);
        }
        let username = projectId;
        if (+toolId) {
            username = `project_ref=${projectId}&ib_ref=${toolId}`;
        }

        let data = await getReportEmbedInfo(req, toolName, username);
        if (!data.embedUrl) {
            return sendResponse(res, data);
        }

        sails.log.info(`got required info to generate ${toolName} report for project ${projectId}.`);
        return successResponse(res, {report_meta: data});
    },

    getCompanyToolReport: async (req, res) => {
        let companyId = +req.param('companyId');
        let toolName = req.param('toolName');
        if (!toolName) {
            sails.log.info('Tool name is required to generate the report.');
            return errorResponse(res, `Tool name is required to generate the report, ${companyId}`);
        }
        let data = await getReportEmbedInfo(req, toolName, companyId);
        if (!data.embedUrl) {
            return sendResponse(res, data);
        }

        sails.log.info(`got required info to generate ${toolName} report for company ${companyId}.`);
        return successResponse(res, {report_meta: data});
    },

    getSuperAdminToolReport: async (req, res) => {
        let toolName = req.param('toolName');
        if (!toolName) {
            sails.log.info('Tool name is required to generate the report.');
            return errorResponse(res, `Tool name is required to generate the report`);
        }

        let data = await getReportEmbedInfo(req, toolName);
        if (!data.embedUrl) {
            return sendResponse(res, data);
        }

        sails.log.info(`got required info to generate ${toolName} report.`);
        return successResponse(res, {report_meta: data});
    },

    exportProjectToolReport: exportDashboardToolReport,

    updateProjectExportQueue: updateExportQueue,

    exportCompanyToolReport: exportDashboardToolReport,

    updateCompanyExportQueue: updateExportQueue,
}
