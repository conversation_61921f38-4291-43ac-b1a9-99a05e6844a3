module.exports = {
    tableName: 'project_timesheet',

    attributes: {

        user_ref: {
            model: 'user'
        },
        project_ref: {
            model: 'project',
        },
        day_of_yr: {
            type: 'ref',
            columnType: 'date',
        },
        status: {
            type: 'number',
            defaultsTo: 1,
        },
        modified_by: {
            model: 'user'
        },

        actual_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        day_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        night_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        // 1 - day-hours, 2 - night-hours, 3 split-shift, 4 - holiday, 5 - sick
        hours_state: {
            type: 'number',
            defaultsTo: 1,
        },
        travel_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        overtime_seconds: {
            type: 'number',
            columnType: 'int',
            allowNull: true
        },
        training_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        manager_auth_seconds: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        price_work_amount: {
            type: 'string',
            columnType: 'float',
            allowNull: true
        },

        comments: {
            type: 'json',
            defaultsTo: []
        },
        change_logs: {      // Not being used, instead we would use audit log service.
            type: 'json',
            defaultsTo: []
        },
        weekly_timesheet_ref: {
            model: 'projectweeklytimesheet'
        }
    },

    //attributes methods
    customToJSON: function () {
        if(this.actual_seconds){
            this.actual_seconds = +this.actual_seconds;
        }
        if(this.day_seconds){
            this.day_seconds = +this.day_seconds;
        }
        if(this.night_seconds){
            this.night_seconds = +this.night_seconds;
        }
        if(this.travel_seconds){
            this.travel_seconds = +this.travel_seconds;
        }
        if(this.overtime_seconds){
            this.overtime_seconds = +this.overtime_seconds;
        }
        if(this.training_seconds){
            this.training_seconds = +this.training_seconds;
        }
        if(this.manager_auth_seconds){
            this.manager_auth_seconds = +this.manager_auth_seconds;
        }
        if(this.price_work_amount){
            this.price_work_amount = +this.price_work_amount;
        }
        return _.omit(this, []);
    },
};
