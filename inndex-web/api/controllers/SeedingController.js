/**
 * Created by spatel on 1/12/18.
 */

const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
dayjs.extend(utc);
const ExcelJS = require('exceljs');
const fs = require('fs');
const {
    dbDateFormat_YYYY_MM_DD,
    PROJECT_SETTING_KEY: {SMART_SHEET_APP_INFO},
    ASSET_TYPES,
} = sails.config.constants;
const {
    OptimaSyncService: {callOptimaBoxGateway,},
    UserRevisionService: {createUserRevision, getLatestUserRevision},
    TokenUtil,
    DataProcessingService: {
        getUserFullName,
        attachUserDocument,
        deriveDistanceMatrixRegion,
        getDistanceMatrix,
        populateUserRefs,
        populateProjectRefs,
        getHourlyWeatherLogs,
        briefingToolTables
    },
    DataToSeedService,
    HttpService: {typeOf, triggerLambdaFn},
    ExcelService: {
        streamExcelDownload,
    },
    RekognitoService: {
        triggerCreateCollection
    }
} = require('./../services');
const ResponseService = require('./../services/ResponseService');
const TimeLogService = require('./../services/TimeLogService');
const QRCode = require('qrcode');
const SharedService = require('./../services/SharedService');
const {attachLocationKey, DEFAULT_COUNTY_CODE_GB, postcodeToLatLong} = require('./../services/WeatherSyncService');
const _get = _.get;
const _uniq = require('lodash/uniq');
const _groupBy = _.groupBy;
const moment = require('moment');

const path = require("path");
let meta_file_path = path.join(process.cwd(), sails.config.constants.COUNTRIES_META_FILE);
let { countries, nationalities } = JSON.parse(fs.readFileSync(meta_file_path, 'utf-8'));
const { getCoordinates, calculateDistance, updateTravelTime} = require('../services/DataProcessingService');
const { runReadQuery } = require('../sql.fn/core.fn');

const getUniqueId = () => {
    let timestamp = Date.now();
    let random = Math.floor(Math.random() * 10000);
    return parseInt(`${timestamp}${random}`);
};

const isValidRequest = (req) => {
    let token = (sails.config.custom.SECURE_AUTH_KEY || moment().valueOf() + '-yash');
    sails.log.info('Validation token ', req.param('t'));
    return (req.param('t') && req.param('t') === token);
};

const testDataForStdTemplate = {
    "title": "Induction Form",
    "user": {
        "createdAt": 1539630836707,
        "updatedAt": 1544294372511,
        "id": 7,
        "title": "Mr",
        "first_name": "Yash FakeId",
        "middle_name": null,
        "last_name": "Patel",
        "dob": "2009-10-16",
        "country": "India",
        "gender": "Male",
        "nin": "N.a.",
        "email": "<EMAIL>",
        "is_active": 1,
        "email_verified_on": "*********5029",
        "verify_email_token": null,
        "user_onboard_status": {
            "personal": true,
            "competencies": true,
            "address": true,
            "health_assessment": true,
            "employment": true
        },
        "profile_pic_expiry": "1605719929376",
        "profile_pic_ref": {
            "createdAt": 1542561529170,
            "updatedAt": 1542561529170,
            "id": 41,
            "file_mime": "image/jpeg",
            "name": "Y-80387-Y (1).jpg",
            "file_url": "https://inductme-uploads.s3.amazonaws.com/d602ffa3-046b-4234-8865-cbaae1f31ae5.jpg",
            "user_id": 7
        },
        "name": "Yash FakeId Patel"
    },
    "contact_detail": {
        "createdAt": 1541100908086,
        "updatedAt": 1544118688748,
        "id": 2,
        "house_no": "1",
        "street": "Main St.",
        "city": "Indore",
        "post_code": "452001",
        "home_no": "",
        "mobile_no": "**********",
        "emergency_contact": "Sandeep P",
        "emergency_contact_no": "**********",
        "user_ref": 7
    },
    "employment_detail": {
        "createdAt": 1542046781600,
        "updatedAt": 1544118893283,
        "id": 2,
        "employer": "L&T Infra Ltd",
        "reporting_to": "Sandeep",
        "job_role": "Site Engg.",
        "type_of_employment": "Other (Full Time)",
        "operative_type": "Senior Management",
        "months_with_employer": "P7M",
        "start_date_with_employer": "1544558254057",
        "earn_mlw_e783": 1,
        "user_ref": 7
    },
    "project": {
        "project_users": [
            {
                "createdAt": 1544558254057,
                "updatedAt": 1545757362968,
                "id": 85,
                "email": "<EMAIL>",
                "project_role": ["inductor"],
                "project_id": 10001,
                "user_ref": 1
            }
        ],
        "declarations": [
            {
                "createdAt": 1543432064580,
                "updatedAt": 1545757362981,
                "id": 38,
                "content": "Kindly accept this to accept our TOS....",
                "project_id": 10001,
                "edited_by": null
            }
        ],
        "createdAt": 1543432064561,
        "updatedAt": 1545757362947,
        "id": 10001,
        "name": "Office repairing site",
        "description": "Don't have any for this",
        "company_name": null,
        "contractor": "Yash Infra",
        "main_contact_name": "Yash Patel",
        "main_contact_number": "**********9",
        "is_active": 1,
        "has_c_lens_policy": 1,
        "c_lens_policy": "<pre>In the Railway environment and for personal track safety (PTS), it is essential that any personnel who wear contact lenses must abide by the following directive.\n\nA pair of prescription spectacles of equal strength and clarity must be carried at all times. This enables the operative to carry on working safely if a contact lens is lost or damaged.\nAll contact lens wearers must make themselves known to the Controller of Site Safety / Safe Work Leader (COSS/SWL) at the pre-work briefing. Failure to bring along the requisite pair of spectacles will result in the operative not being allowed on site.</pre>",
        "has_d_and_a_policy": 0,
        "d_and_a_policy": null,
        "has_working_hr_agreement": 1,
        "working_hr_agreement": "<p>I fully accept and will abide by the site working hours agreement of 12 hours door to door.</p>",
        "disabled_on": null,
        "template_identifier": "std-induct-me",
        "logo_file_id": {
            "createdAt": 1543432041011,
            "updatedAt": 1543432041011,
            "id": 69,
            "file_mime": "image/png",
            "name": "yash12010.qr.png",
            "file_url": "https://inductme-uploads.s3.eu-west-2.amazonaws.com/d3236db1-bc5a-45c1-92c1-ec7d983c67e4.png",
            "user_id": 7
        },
        "created_by": {
            "createdAt": 1539630836707,
            "updatedAt": 1544294372511,
            "id": 7,
            "title": "Mr",
            "first_name": "Yash FakeId",
            "middle_name": null,
            "last_name": "Patel",
            "dob": "2009-10-16",
            "country": "India",
            "gender": "Male",
            "nin": "N.a.",
            "email": "<EMAIL>",
            "is_active": 1,
            "email_verified_on": "*********5029",
            "verify_email_token": null,
            "user_onboard_status": {
                "personal": true,
                "competencies": true,
                "address": true,
                "health_assessment": true,
                "employment": true
            },
            "profile_pic_expiry": "1605719929376",
            "profile_pic_ref": 41,
            "name": "Yash FakeId Patel"
        },
        "edited_by": 7
    },
    "inductor": {
        "createdAt": 1539630836707,
        "updatedAt": 1544294372511,
        "id": 7,
        "title": "Mr",
        "first_name": "Yash FakeId",
        "middle_name": null,
        "last_name": "Patel",
        "dob": "2009-10-16",
        "country": "India",
        "gender": "Male",
        "nin": "N.a.",
        "email": "<EMAIL>",
        "is_active": 1,
        "email_verified_on": "*********5029",
        "verify_email_token": null,
        "user_onboard_status": {
            "personal": true,
            "competencies": true,
            "address": true,
            "health_assessment": true,
            "employment": true
        },
        "profile_pic_expiry": "1605719929376",
        "profile_pic_ref": 41,
        "name": "Yash FakeId Patel"
    },
    "induction_request": {
        "medications": [],
        "createdAt": 1545757443530,
        "updatedAt": 1546187884104,
        "id": 77,
        "record_id": "1",
        "travel_time": {
            "to_work": "PT20M",
            "to_home": "PT20M"
        },
        "travel_method": "Train",
        "vehicle_reg_number": null,
        "fit_undertake_role": 1,
        "fit_to_work": 1,
        "confirm_detail_valid": 1,
        "comply_hour_agreement": 1,
        "site_directive_selection": "I do wear contact lenses and agree to abide to the directive",
        "accept_drug_alcohol_pol": null,
        "reportable_medical_conditions": "no",
        "rmc_detail": null,
        "on_long_medication": "no",
        "any_side_effects": "n/a",
        "any_side_effect_detail": null,
        "user_doc_ids": [],
        "accepted_declarations": [
            38
        ],
        "status_code": 1,
        "comments": [
            {
                "timestamp": 1545757443099,
                "user_id": 7,
                "name": "Yash FakeId  Patel",
                "origin": "system",
                "note": "Created New Request"
            },
            {
                "timestamp": 1546187724645,
                "note": "try again",
                "user_id": 7,
                "name": "Yash FakeId Patel",
                "origin": "admin"
            },
            {
                "timestamp": 1546187884023,
                "user_id": 7,
                "name": "Yash FakeId Patel",
                "origin": "system",
                "note": "Updated Existing Request"
            }
        ],
        "project_ref": 10001,
        "user_ref": 7,
        "inductor_ref": {
            "createdAt": 1539630836707,
            "updatedAt": 1544294372511,
            "id": 7,
            "title": "Mr",
            "first_name": "Yash FakeId",
            "middle_name": null,
            "last_name": "Patel",
            "dob": "2009-10-16",
            "country": "India",
            "gender": "Male",
            "nin": "N.a.",
            "email": "<EMAIL>",
            "is_active": 1,
            "email_verified_on": "*********5029",
            "verify_email_token": null,
            "user_onboard_status": {
                "personal": true,
                "competencies": true,
                "address": true,
                "health_assessment": true,
                "employment": true
            },
            "profile_pic_expiry": "1605719929376",
            "profile_pic_ref": 41,
            "name": "Yash FakeId Patel"
        },
        "edited_by": 7,
        "user_docs": [],
        "status_message": "Pending"
    },
    "has_medications": false,
    "project_logo_file": {
        "createdAt": 1543432041011,
        "updatedAt": 1543432041011,
        "id": 69,
        "file_mime": "image/png",
        "name": "yash12010.qr.png",
        "file_url": "https://inductme-uploads.s3.eu-west-2.amazonaws.com/d3236db1-bc5a-45c1-92c1-ec7d983c67e4.png",
        "user_id": 7
    },
    "user_docs": [],
    "declarations": [
        {
            "createdAt": 1543432064580,
            "updatedAt": 1545757362981,
            "id": 38,
            "content": "Kindly accept this to accept our TOS....",
            "project_id": 10001,
            "edited_by": null
        }
    ],
    "health_assessment_answers": [
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 71,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 1
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 72,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 2
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 73,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 3
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 74,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 4
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 75,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 5
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 76,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 6
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 77,
            "answer": "n/a",
            "user_ref": 7,
            "question_ref": 7
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 78,
            "answer": "n/a",
            "user_ref": 7,
            "question_ref": 8
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 79,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 9
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 80,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 10
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 81,
            "answer": "0",
            "user_ref": 7,
            "question_ref": 11
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 82,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 12
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 83,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 13
        },
        {
            "createdAt": 1544294371835,
            "updatedAt": 1544294371835,
            "id": 84,
            "answer": "1",
            "user_ref": 7,
            "question_ref": 14
        }
    ],
    "assessment_categories": {
        "HAVS": [
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 1,
                "category": "HAVS",
                "question": "Do you suffer from tingling and numbness in fingers which effects everyday tasks?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 2,
                "category": "HAVS",
                "question": "Do you suffer from loss of strength in hands which might affect your ability to do your work safely?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 3,
                "category": "HAVS",
                "question": "Do your fingers go white (blanching) and become red and painful on recovery, reducing ability to work in cold or damp conditions?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            }
        ],
        "Dermititis/Skin Conditions": [
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 4,
                "category": "Dermititis/Skin Conditions",
                "question": "Do you or have you suffered from skin cracking or peeling?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 5,
                "category": "Dermititis/Skin Conditions",
                "question": "Do you or have you suffered from skin redness or soreness?",
                "type": "bool",
                "order": 2,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 6,
                "category": "Dermititis/Skin Conditions",
                "question": "Do you or have you suffered from itchiness or rashes?",
                "type": "bool",
                "order": 3,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 7,
                "category": "Dermititis/Skin Conditions",
                "question": "Did any of the above last for a week or more?",
                "type": "three-radio",
                "order": 4,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 8,
                "category": "Dermititis/Skin Conditions",
                "question": "Did you require time off work due to severity?",
                "type": "three-radio",
                "order": 5,
                "is_active": 1
            }
        ],
        "Noise Induced Hearing Loss": [
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 9,
                "category": "Noise Induced Hearing Loss",
                "question": "Do you struggle to have conversations over background noise?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 10,
                "category": "Noise Induced Hearing Loss",
                "question": "Do you suffer from ringing in your ears?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 11,
                "category": "Noise Induced Hearing Loss",
                "question": "Do people complain that you have the radio or television too loud?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            }
        ],
        "Muscular Skeletal Disorders": [
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 12,
                "category": "Muscular Skeletal Disorders",
                "question": "Do you suffer from back pain or tenderness in your back?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 13,
                "category": "Muscular Skeletal Disorders",
                "question": "Do you have limited motion in your back or muscular tenderness?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            },
            {
                "createdAt": 1544118728722,
                "updatedAt": 1544118728722,
                "id": 14,
                "category": "Muscular Skeletal Disorders",
                "question": "Do you get pain or numbness down back of your legs or in your feet?",
                "type": "bool",
                "order": 1,
                "is_active": 1
            }
        ]
    },
    "categories_names": [
        "HAVS",
        "Dermititis/Skin Conditions",
        "Noise Induced Hearing Loss",
        "Muscular Skeletal Disorders"
    ]
};

const sleep = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

const updateChecklistItems = (checklist, has_subheadings) => {
    if(has_subheadings) {
        (checklist || []).map(heading => {
            (heading.subheadings || []).map(item => {
                if (item.tagged_company_ref && typeOf(item.tagged_company_ref, "number")) {
                    item.tagged_company_ref = [item.tagged_company_ref];
                } else {
                    sails.log.info('Incorrect type of tagged_company_ref found, Converting to blank array. Value was: ', item.tagged_company_ref);
                    item.tagged_company_ref = [];
                }
                return item;
            });
            return heading;
        });
    } else {
        (checklist || []).map(item => {
            if (item.tagged_company_ref && typeOf(item.tagged_company_ref, "number")) {
                item.tagged_company_ref = [item.tagged_company_ref];
            } else {
                sails.log.info('Incorrect type of tagged_company_ref found, Converting to blank array. Value was: ', item.tagged_company_ref);
                item.tagged_company_ref = [];
            }
            return item;
        });
    }
    return checklist;
};

const getRatingPointInfo = (scoringSystem) => {
    let ratingPointAgainstRating = {};
    if (scoringSystem.has_rating_point && scoringSystem.rating_point  && scoringSystem.rating_point.length) {
        (scoringSystem.values || []).map((value, index) => {
            ratingPointAgainstRating[value.toLowerCase()] = (scoringSystem.rating_point[index] || 0);
            return value;
        });

        return  {hasRatingPoint: true, ratingPointAgainstRating}
    }
    return  {hasRatingPoint: false, ratingPointAgainstRating}
};


const trimObject = (obj) => {
    if (!Array.isArray(obj) && typeof obj != 'object') return obj;
    return Object.keys(obj).reduce(function(acc, key) {
      acc[key.trim()] = typeof obj[key] == 'string'? obj[key].trim() : trimObject(obj[key]);
      return acc;
    }, Array.isArray(obj)? []:{});
  };

const isEnabledForProject = (inspection, projectId) => {
    let projectEntry = (inspection.activate_on_projects || []).find(item => (item.project_id == projectId));
    return (!projectEntry || projectEntry.enabled);
}

const convertArrayOfNumbersToObjects = (arrayOfNumbers, numberKey) => {
    let arrayOfObjects = arrayOfNumbers.reduce((arr, number) => {
        if (+number) {
            let object = {};
            object[numberKey] = number;
            arr.push(object);
        }
        return arr;
    }, []);

    return (arrayOfObjects.length) ? arrayOfObjects : arrayOfNumbers;
};


const goodRatings = ['1', 'compliant', 'good', 'yes'];
const fairRatings = ['2', 'area of concern', 'fair'];
const poorRatings = ['3', 'non compliance record', 'poor', 'no'];

const goodRating = 'Satisfactory';
const fairRating = 'Area for Improvement';
const poorRating = 'Unsatisfactory';

const replaceRating = (rating) => {
    let searchRating = (rating || '').toLowerCase();
    let result = rating;
    if (goodRatings.includes(searchRating)) return goodRating;
    if (fairRatings.includes(searchRating)) return fairRating;
    if (poorRatings.includes(searchRating)) return poorRating;
    return result; // Return the original if no match
}

const computeWeekEndDate = (inputDate, endWeekDay) => {
    const jsTargetDay = endWeekDay === 7 ? 0 : endWeekDay;
    let currentDate = dayjs.utc(inputDate, dbDateFormat_YYYY_MM_DD);
    sails.log.info(currentDate,jsTargetDay, 'currentDate');

    // offset from week start
    const daysUntilWeekend = (jsTargetDay - currentDate.day() + 7) % 7;
    // console.log('daysUntilWeekend', daysUntilWeekend);

    // subtract remaining days of week, to get starting point
    let startDate = currentDate.subtract((7 - daysUntilWeekend), 'day').add(1, 'day');
    // console.log('startOfWeek', startDate);

    let endDate = startDate.clone().add(6, 'day');
    sails.log.info(endDate, 'endDate');
    endDate = dayjs.utc(endDate).format(dbDateFormat_YYYY_MM_DD);

    sails.log.info({inputDate, startDate, endDate});
    return endDate;
};

dateToBeFormated = (date) =>{
    return dayjs.utc(date).local().format(dbDateFormat_YYYY_MM_DD);
}

const  associateStateWithSignOff = async (sign_off, templateSignatures) => {
    let state = 10;
    let closeout_state = 210;
    let lastSignOffIndex = undefined;
    let lastCloseoutIndex = undefined;
    //finding index to set final state
    (templateSignatures || []).map((sign, index) => {
        if (sign.is_closeout) {
            lastCloseoutIndex = index;
        }  else if (!sign.is_closeout_requestor && !sign.is_closeout) {
            lastSignOffIndex = index;
        }
    });
    sails.log.info("lastSignOffIndex: ", lastSignOffIndex, "lastCloseoutIndex: ", lastCloseoutIndex);
    return (templateSignatures || []).map((sign, index) => {
        sign = _.pick((sign || {}), [
            'sign_number',
            'is_requestor',
            'is_closeout_requestor',
            'is_closeout',
            'field_name',
            'field_label',
            'link_fields',
            'link_sections',
            'signatories'
        ]);
        sign.signatories = (sign_off[index] && sign_off[index].signatories) ? sign_off[index].signatories : [];
        //assuming that closeout will last sign off so its state should be 99
        if (templateSignatures[index].is_closeout_requestor) {
            sign.state = 200;
        } else if (templateSignatures[index].is_closeout) {
            sign.state = (lastCloseoutIndex === index) ? 400 : closeout_state;
            closeout_state += 10;
        } else {
            sign.state = (lastSignOffIndex === index) ? 190 : state;
            state += 10;
        }

        return sign;
    });
}


module.exports = {

    seedUserHealthAssessment: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let batchRows = [
            {
                category: 'HAVS',
                question: `Do you suffer from tingling and numbness in fingers which affects everyday tasks?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'HAVS',
                question: `Do you suffer from loss of strength in hands which might affect your ability to do your work safely?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'HAVS',
                question: `Do your fingers go white (blanching) and become red and painful on recovery, reducing ability to work in cold or damp conditions?`,
                type: 'bool',
                is_active: 1
            },

            {
                category: 'Dermititis/Skin Conditions',
                question: `Do you or have you suffered from skin cracking or peeling?`,
                type: 'bool',
                is_active: 1,
                order: 1
            },
            {
                category: 'Dermititis/Skin Conditions',
                question: `Do you or have you suffered from skin redness or soreness?`,
                type: 'bool',
                is_active: 1,
                order: 2
            },
            {
                category: 'Dermititis/Skin Conditions',
                question: `Do you or have you suffered from itchiness or rashes?`,
                type: 'bool',
                is_active: 1,
                order: 3
            },
            {
                category: 'Dermititis/Skin Conditions',
                question: `Did any of the above last for a week or more?`,
                type: 'three-radio',
                is_active: 1,
                order: 4
            },
            {
                category: 'Dermititis/Skin Conditions',
                question: `Did you require time off work due to severity?`,
                type: 'three-radio',
                is_active: 1,
                order: 5
            },

            {
                category: 'Noise Induced Hearing Loss',
                question: `Do you struggle to have conversations over background noise?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'Noise Induced Hearing Loss',
                question: `Do you suffer from ringing in your ears?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'Noise Induced Hearing Loss',
                question: `Do people complain that you have the radio or television too loud?`,
                type: 'bool',
                is_active: 1
            },

            {
                category: 'Muscular Skeletal Disorders',
                question: `Do you suffer from back pain or tenderness in your back?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'Muscular Skeletal Disorders',
                question: `Do you have limited motion in your back or muscular tenderness?`,
                type: 'bool',
                is_active: 1
            },
            {
                category: 'Muscular Skeletal Disorders',
                question: `Do you get pain or numbness down the back of your legs or in your feet?`,
                type: 'bool',
                is_active: 1
            }
        ];

        sails.models.userhealthassessmentquestion.destroy({}).exec(function afterDestroy(err) {
            if (err) {
                sails.log.info('truncate failed', err);
                return ResponseService.errorResponse(res, sails.__('internal server error'), err);
            }
            sails.log.info('truncate success');
            sails.models.userhealthassessmentquestion.createEach(batchRows).exec(function batchInsert(errorInsert, records) {
                if (errorInsert) {
                    sails.log.info('insert failed', errorInsert);
                    return ResponseService.errorResponse(res, sails.__('internal server error'), errorInsert);
                }
                sails.log.info('insert success');
                return ResponseService.successResponse(res, {records});
            });
        });
    },

    seedFormTemplate: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        const batchRows = [{
            identifier: `std-induct-me`,
            label: `Standard Template - innDex`,
            template_path: `pages/induction-form-page`,
            test_data: testDataForStdTemplate,
            visibility: `public`
        }];

        try{
            let deleted = await sails.models.formtemplate.destroy({});
            sails.log.info('truncate success');

            let records = await sails.models.formtemplate.createEach(batchRows);
            sails.log.info('insert success');
            return ResponseService.successResponse(res, {records});
        }catch (err) {
            sails.log.info('seeding failed', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    attachQrImageWithProjects: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        try {
            let projects = (req.body.projects || []).map(id => +id);
            let where = {qrcode_image:null};
            if(projects.length){
                where = {id: projects};
            }
            let projectsListing = await sails.models.project.find(where);
            if (!projectsListing.length) {
                sails.log.info('No record available to update.');
                return ResponseService.successResponse(res, ['No record available to update.']);
            }
            let updates = [];
            let len = projectsListing.length;
            sails.log.info('Total projectsListing', len);
            for (let i = 0; i < len; i++) {
                let projectId = projectsListing[i].id;
                let project = projectsListing[i];
                project.record_id = project.id.toString();
                if(project.project_initial && project.use_prefix){
                    project.record_id = `${project.project_initial.toString().trim().toUpperCase()}${project.record_id}`;
                }
                let imageData = await QRCode.toDataURL(`https://www.inndex.co.uk/app?p=${project.record_id}&t=scan`);
                let uploadedFile = {
                    imageData: imageData,
                    type: 'image/png'
                };
                let s3Url = await SharedService.uploadToS3(uploadedFile, 'project-qr-'+projectId+'.png', 'qr-code-pic');
                let updatedProject = await sails.models.project.updateOne({id: projectId}).set({qrcode_image: s3Url});
                sails.log.info('Attach QR code image with project successful, id', updatedProject.id);
                updates.push({
                    id: updatedProject.id,
                    qrcode_image: s3Url
                })
            }
            //sails.log.info('fetched records', projectsListing);
            return ResponseService.successResponse(res, {updates});
        } catch (err) {
            sails.log.info('seeding failed', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    attachProjectLocationKey: async (req, res) => {
        try {
            let updated_projects = [];
            let projects = (req.body.projects || []);
            let attach_weather_key = (req.body.attach_weather_key || false);
            for (let i = 0; i < projects.length; i++) {
                let projectId = projects[i];
                sails.log.info('Seed location key for project, id:', projectId, 'attach_weather_key', attach_weather_key);
                let project = await sails.models.project.findOne({id: projectId, postcode: {'!=': null}});
                let data = await attachLocationKey(project, attach_weather_key);
                updated_projects.push(data);
                await sleep(500);
            }
            return ResponseService.successResponse(res, {updated_projects});

        }catch (err) {
            sails.log.info('location key seeding failed', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    refreshUserTravelDistance: async (req, res) => {
        let ids = (req.body.ids || []);
        if (!isValidRequest(req) || !ids.length) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        try {
            let all_valid_inductions = await sails.models.inductionrequest.find({
                select: ['additional_data', 'status_code', 'project_ref', 'user_ref', 'travel_time', 'travel_method'],
                where: {
                    id: ids,
                    // optima_badge_number: {'!=': null},
                    // status_code: [1,2, 3, 6], // skip rejected one
                }
            });
            if(all_valid_inductions){
                all_valid_inductions = await populateProjectRefs(all_valid_inductions, 'project_ref', []);
                all_valid_inductions = await populateUserRefs(all_valid_inductions, 'user_ref', []);

                // @todo: google DM
                // group BY project_ref.id
                // user origin
                // let address = _get(record, 'additional_data.contact_detail', {});
                // `${address.city ? address.city : ''} ${address.post_code ? address.post_code : ''}`.replace(/\s+/g, ' ');
                // [project.postcode]
                try {
                    let updatedOnes = {};
                    sails.log.info('add user travel distance Matrix for existing records, length', all_valid_inductions.length);
                    for(let i= 0, len = all_valid_inductions.length; i < len; i++){
                        let ir = all_valid_inductions[i];
                        if(ir.project_ref && ir.project_ref.postcode){
                            let address = _get(ir, 'additional_data.contact_detail', {});
                            let country_code = (ir.project_ref.custom_field && ir.project_ref.custom_field.country_code) || 'UK';
                            const region = deriveDistanceMatrixRegion(country_code);
                            let origins = `${address.city ? address.city : ''} ${address.post_code ? address.post_code : ''} ${region||''}`.replace(/\s+/g, ' ');
                            let destinations = `${ir.project_ref.postcode} ${region}`;
                            let distanceMatrix = await getDistanceMatrix(origins, destinations, region);

                            sails.log.info('Induction Record', ir.id, 'distanceMatrix', distanceMatrix);

                            if(!all_valid_inductions[i].travel_time){
                                all_valid_inductions[i].travel_time = {};
                            }
                            all_valid_inductions[i].travel_time.distance_matrix = _get(distanceMatrix, `[0]elements[0]`, {});
                            await sails.models.inductionrequest.updateOne({id: ir.id}).set({travel_time: all_valid_inductions[i].travel_time});
                            sails.log.info('Updated IR:', ir.id);
                            updatedOnes[ir.id] = all_valid_inductions[i].travel_time.distance_matrix;
                            await sleep(1000);
                            // break;
                        }else{
                            sails.log.info('Skipping seeding for Induction Record', ir.id);
                        }
                    }

                    return ResponseService.successResponse(res, {updatedOnes});
                } catch (e) {
                    sails.log.info('Unable to add user travel distance Matrix.', e);
                }

                return ResponseService.successResponse(res, {all_valid_inductions});
            }
            return ResponseService.errorResponse(res, sails.__('record not found'));
        }catch (err) {
            sails.log.info('refresh user travel distance failed', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    seedHazardCategory: async (req, res) => {
        let settingName = req.param('settingName'); //good_call_category_en_gb, observation_category_en_gb, close_call_category_en_gb
            if (!isValidRequest(req) || !['good_call_category_en_gb', 'observation_category_en_gb', 'close_call_category_en_gb'].includes(settingName)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let goodCallCategory = [
            { name: 'Access & Egress' },
            { name: 'Buried/Above Ground/Overhead services' },
            { name: 'Confined Spaces' },
            { name: 'COSHH' },
            { name: 'Covid-19' },
            { name: 'Ecology (Plants & Animals)' },
            { name: 'Electrical Safety' },
            { name: 'Excavation Safety' },
            { name: 'Fire Safety' },
            { name: 'Hot Works, Welding and Cutting Activities' },
            { name: 'Lifting Operations' },
            { name: 'Manual Handling' },
            { name: 'Moving Plant & Machinery' },
            { name: 'Nuisance (Noise/Lighting)' },
            { name: 'Personal Health' },
            { name: 'PPE' },
            { name: 'Process/Documentation' },
            { name: 'Protection/Possession Arrangements' },
            { name: 'Quality' },
            { name: 'Traffic/Pedestrian Management' },
            { name: 'Railway Operations' },
            { name: 'Rail Vehicles' },
            { name: 'Road Vehicles' },
            { name: 'Safe Systems Of Work (SSOW)' },
            { name: 'Site Welfare & Housekeeping' },
            { name: 'Slips, Trips & Falls' },
            { name: 'Spillage or Pollution (Oils/Dust ect)' },
            { name: 'Tools & Equipment' },
            { name: 'Waste' },
            { name: 'Working at Height' },
            { name: 'Working on/over/near water' },
            { name: 'Unsafe Behaviour' }
        ];

        let observationCategory = [
            { name: 'Access & Egress' },
            { name: 'Buried/Above Ground/Overhead services' },
            { name: 'Confined Spaces' },
            { name: 'COSHH' },
            { name: 'Covid-19' },
            { name: 'Crime/Security' },
            { name: 'Ecology (Plants & Animals)' },
            { name: 'Electrical Safety' },
            { name: 'Excavation Safety' },
            { name: 'Fire Safety' },
            { name: 'Hot Works, Welding and Cutting Activities' },
            { name: 'Lifting Operations' },
            { name: 'Manual Handling' },
            { name: 'Moving Plant & Machinery' },
            { name: 'Nuisance (Noise/Lighting)' },
            { name: 'Personal Health' },
            { name: 'PPE' },
            { name: 'Process/Documentation' },
            { name: 'Protection/Possession Arrangements' },
            { name: 'Traffic/Pedestrian Management' },
            { name: 'Railway Operations' },
            { name: 'Rail Vehicles' },
            { name: 'Road Vehicles' },
            { name: 'Safe Systems Of Work (SSOW)' },
            { name: 'Site Welfare & Housekeeping' },
            { name: 'Slips, Trips & Falls' },
            { name: 'Spillage or Pollution (Oils/Dust ect)' },
            { name: 'Tools & Equipment' },
            { name: 'Waste' },
            { name: 'Working at Height' },
            { name: 'Working on/over/near water' },
            { name: 'Unsafe Behaviour' },
        ];

        let closeCallCategory = [
            { name: 'Access & Egress' },
            { name: 'Buried/Above Ground/Overhead services' },
            { name: 'Confined Spaces' },
            { name: 'COSHH' },
            { name: 'Covid-19' },
            { name: 'Crime/Security' },
            { name: 'Ecology (Plants & Animals)' },
            { name: 'Electrical Safety' },
            { name: 'Excavation Safety' },
            { name: 'Fire Safety' },
            { name: 'Hot Works, Welding and Cutting Activities' },
            { name: 'Lifting Operations' },
            { name: 'Manual Handling' },
            { name: 'Moving Plant & Machinery' },
            { name: 'Nuisance (Noise/Lighting)' },
            { name: 'Personal Health' },
            { name: 'PPE' },
            { name: 'Process/Documentation' },
            { name: 'Protection/Possession Arrangements' },
            { name: 'Traffic/Pedestrian Management' },
            { name: 'Railway Operations' },
            { name: 'Rail Vehicles' },
            { name: 'Road Vehicles' },
            { name: 'Safe Systems Of Work (SSOW)' },
            { name: 'Site Welfare & Housekeeping' },
            { name: 'Slips, Trips & Falls' },
            { name: 'Spillage or Pollution (Oils/Dust ect)' },
            { name: 'Tools & Equipment' },
            { name: 'Waste' },
            { name: 'Working at Height' },
            { name: 'Working on/over/near water' },
            { name: 'Unsafe Behaviour' }
        ];

        await sails.models.inndexsetting.destroy({ name: settingName });
        sails.log.info('truncate success, setting name: ', settingName);

        sails.log.info('Adding category for in inndex setting for ', settingName);

        let settingValue = (settingName == 'good_call_category_en_gb') ? goodCallCategory : (settingName == 'observation_category_en_gb') ? observationCategory : closeCallCategory;
        settingValue = (settingValue).sort((a, b) => a.name.localeCompare(b.name));

        let record = await sails.models.inndexsetting.create({
            name: settingName, value: settingValue
        });

        sails.log.info('insert success');
        return ResponseService.successResponse(res, {record});
    },

    seedLightingConditions: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let lightingConditionsRows = [
            { name: 'Daylight' },
            { name: 'Dark' },
            { name: 'Artificial Light' }
        ];

        sails.models.lightingconditions.destroy({}).exec(function afterDestroy(err) {
            if (err) {
                sails.log.info('truncate failed', err);
                return ResponseService.errorResponse(res, sails.__('internal server error'), err);
            }
            sails.log.info('truncate success');
            sails.models.lightingconditions.createEach(lightingConditionsRows).exec(function batchInsert(errorInsert, records) {
                if (errorInsert) {
                    sails.log.info('insert failed', errorInsert);
                    return ResponseService.errorResponse(res, sails.__('internal server error'), errorInsert);
                }
                sails.log.info('insert success');
                return ResponseService.successResponse(res, {records});
            });
        });
    },

    seedUserMedicalAssessment: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let batchRows = [{
            "question": "Do you have diabetes needing insulin?",
            "is_active": 1,
            "details_req": 0,
            "order": 1,
            "id": 1
        }, {
            "question": "Do you suffer from Epilepsy or fits?",
            "is_active": 1,
            "details_req": 0,
            "order": 2,
            "id": 2
        }, {
            "question": "Have you ever had blackouts, recurrent dizziness or any condition which may cause sudden collapse or incapacity?",
            "is_active": 1,
            "details_req": 0,
            "order": 3,
            "id": 3
        }, {
            "question": "Do you get discomfort or pain in the chest or shortness of breathing on exercise e.g. climbing a single flight of stairs?",
            "is_active": 1,
            "details_req": 0,
            "order": 4,
            "id": 4
        }, {
            "question": "Do you have difficulty in moving rapidly over a short distance, including on slopes, stairs or rough ground?",
            "is_active": 1,
            "details_req": 0,
            "order": 5,
            "id": 5
        }, {
            "question": "Would you have difficulty in looking over your shoulder?",
            "is_active": 1,
            "details_req": 0,
            "order": 6,
            "id": 6
        }, {
            "question": "Do you have any difficulty with your eyesight (simple problems, needing glasses need not be included)?",
            "is_active": 1,
            "details_req": 0,
            "order": 7,
            "id": 7
        }, {
            "question": "Are you taking any medication that is giving you dizziness or drowsiness?",
            "is_active": 1,
            "details_req": 0,
            "order": 9,
            "id": 9
        }, {
            "question": "Have you experienced any Hand/Arm problems from operating vibrating equipment?",
            "is_active": 1,
            "details_req": 0,
            "order": 12,
            "id": 12
        }, {"question": "Do you have any notifiable allergies?", "is_active": 1, "details_req": 1, "order": 13, "id": 13}];

        try {
            var truncated = await sails.models.inndexsetting.destroyOne({name: "medical_assessment_questions"});
            sails.log.info('truncate success');
            try {
                let createRequest = {name: "medical_assessment_questions", value: batchRows};
                sails.log.info('creating medical_assessment_questions in inndex setting with', createRequest);

                let medicalQuestions = await sails.models.inndexsetting.create(createRequest);
                if(medicalQuestions)  {
                    return ResponseService.successResponse(res, medicalQuestions);
                } else {
                    sails.log.info('Failed to create medical_assessment_questions')
                    return ResponseService.errorResponse(res, sails.__('Failed to create medical_assessment_questions'));
                }
            } catch (failure){
                sails.log.info('Failed to create medical_assessment_questions')
                return ResponseService.errorResponse(res, sails.__('Failed to create medical_assessment_questions'));
            }
        } catch (err){
            sails.log.info('truncate failed', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    removeMedicalAssessmentQuestion: async (req, res) => {
        let question = req.param('question', null);
        let force_clean = req.param('force_clean', false);
        if (!question) {
            return ResponseService.errorResponse(res, 'question param missing');
        }
        sails.log.info('Delete user medical assessment answers');
        let deleted_user_med_assessment_ans = await sails.models.usermedicalassessmentans.destroy({question: question});
        question = question.toString().toLowerCase().trim();

        let updated_induction = [];

        let inductions = await sails.models.inductionrequest.find({select: ['id', 'additional_data']});
        for (let i = 0, len = inductions.length; i < len; i++) {

            let answers = _get(inductions[i], 'additional_data.medical_assessments_answers', []);
            sails.log.info('Processing induction', inductions[i].id, ' total existing answers:', answers.length);
            let filtered_answers = answers.filter(a => a.question.toString().toLowerCase().trim() !== question);
            sails.log.info('Filtered answers of', inductions[i].id, ' total count:', filtered_answers.length);

            if (force_clean && filtered_answers.length < answers.length) {
                let updated = await sails.models.inductionrequest.updateOne({id: inductions[i].id}).set({
                    additional_data: {
                        ...(inductions[i].additional_data),
                        medical_assessments_answers: filtered_answers
                    }
                });

                updated_induction.push({id: updated.id});
            }
        }
        sails.log.info('Deleted medical assessment answers', deleted_user_med_assessment_ans.length);
        sails.log.info('Updated induction', updated_induction.length);
        return ResponseService.successResponse(res, {deleted_user_med_assessment_ans, updated_induction});
    },

    exportInductionsToSmartsheet: async (req, res) => {
        let projectId = +(req.param('projectId', 0));
        let filterRequest = _.pick((req.body || {}), [
            'updatedAt',
            'id',
        ]);
        sails.log.info('Export existing approved inductions, project:', projectId, 'filters:', filterRequest);
        let induction_requests = await sails.models.inductionrequest.find({
            project_ref: projectId,
            status_code: 2,
            ...filterRequest
        }).sort([
            {updatedAt: 'ASC'}
        ]);
        induction_requests = await populateUserRefs(induction_requests, 'inductor_ref', []);
        let outcome = await SmartSheetService.exportOnInductionApproval(projectId, induction_requests);
        return ResponseService.successResponse(res, {outcome});
    },

    exportOldTimeLogsToSmartsheet: async (req, res) => {
        let projectId = +(req.param('projectId', 0));
        let filterRequest = _.pick((req.body || {}), [
            'minDate_YYYY_MM_DD',
            'maxDate_YYYY_MM_DD',
        ]);
        let smart_sheet_project = await sails.models.projectsetting.findOne({
            where: {project_ref: projectId, name: SMART_SHEET_APP_INFO, value: {'!=': null}},
        }).populate('project_ref');

        if(smart_sheet_project && smart_sheet_project.value){
            sails.log.info('Export existing time logs to smartsheet, project:', projectId, 'filters:', filterRequest);
            let minDate = dayjs(filterRequest.minDate_YYYY_MM_DD, dbDateFormat_YYYY_MM_DD).startOf('d');
            let maxDate = dayjs(filterRequest.maxDate_YYYY_MM_DD, dbDateFormat_YYYY_MM_DD).startOf('d');
            let outcome = await SmartSheetService.writeTimeRecordsTimesheetOfProject(smart_sheet_project.project_ref, smart_sheet_project.value, minDate, maxDate, false);
            return ResponseService.successResponse(res, {outcome});
        }
        return ResponseService.successResponse(res, {done: false, message: 'Setting not found'});
    },

    reprocessPunchLogs: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let force_clean = req.body.force_clean || false;
        let skip_fatigue = (req.body.skip_fatigue !== undefined) ? req.body.skip_fatigue : true;
        let remove_empty_days = (req.body.remove_empty_days !== undefined) ? req.body.remove_empty_days : false;
        let taget_sources = (req.body.sources) || [VALID_SOURCES.GEO_FENCE, VALID_SOURCES.OPTIMA];
        sails.log.info('Re-progressing punch logs, source:', taget_sources, 'force_clean:', force_clean, 'skip_fatigue:', skip_fatigue, 'remove_empty_days:', remove_empty_days);
        let searchRequest = _.pick((req.body || {}), [
            'from_timestamp',
            'to_timestamp',
            'filter',
        ]);
        if(!taget_sources || !searchRequest.from_timestamp || !searchRequest.to_timestamp){
            sails.log.info('Invalid source or search request:', taget_sources, searchRequest);
            return ResponseService.errorResponse(res, 'Invalid source or search request');
        }
        let result = await TimeLogService.reIndexTimeLogs(searchRequest, taget_sources, force_clean, skip_fatigue, remove_empty_days);
        return ResponseService.successResponse(res, {result});

    },

    seedProjectInductionSlots: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info('create induction slots records, project_id filter?', projectId);
        let rawQuery = `SELECT id, name, custom_field->>'timezone' as tz FROM project WHERE custom_field->>'induction_slot_booking' = 'true' and is_active = 1 ${projectId ? `AND id = ${projectId}`: ''};`;
        let rawResult = await sails.sendNativeQuery(rawQuery);
        let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        sails.log.info('Total projects with induction booking feature enabled:', rows.length);
        let results = [];
        for (let i = 0; i < rows.length; i++) {
            // sails.log.info(`processing project(${rows[i].id}) tz:`, rows[i].tz);
            let records = await DataProcessingService.populateInductionEmptySlots(rows[i].id, dayjs(), rows[i].tz);
            results.push({
                id: rows[i].id,
                count: (records ? records.length : records),
            })
        }
        return ResponseService.successResponse(res, {total_enabled: rows.length, results});
    },

    seedInductionsWithUserDocs: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let requestPayload = _.pick((req.body || {}), [
            'induction_ids',
            'country_code'
        ]);
        if(!requestPayload.induction_ids){
            requestPayload.induction_ids = [];
        }
        if(!requestPayload.country_code){
            requestPayload.country_code = DEFAULT_COUNTY_CODE_GB;
        }
        sails.log.info('seeder, attach user doc with inductions of project_id', projectId, 'induction_ids', requestPayload.induction_ids);
        let inductions = [];
        if(requestPayload.induction_ids && requestPayload.induction_ids.length){
            let startingNoOfEscaped = 0;
            let rawQuery = `SELECT id, user_doc_ids, user_ref, additional_data
                        FROM induction_request
                        WHERE user_doc_ids::text <> '[]'
                          and id IN (${requestPayload.induction_ids.map(() => {
                            startingNoOfEscaped++;
                            return `$${startingNoOfEscaped}`;
                        }).join(',')})`;
            let rawResult = await sails.sendNativeQuery(rawQuery, [...requestPayload.induction_ids]);
            inductions = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        }else{
            let rawQuery = `SELECT id, user_doc_ids, user_ref, additional_data
                        FROM induction_request
                        WHERE user_doc_ids::text <> '[]'
                          and project_ref = $1`;
            let rawResult = await sails.sendNativeQuery(rawQuery, [projectId]);
            inductions = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        }
        let updated_records = [];
        for (let i = 0; i < inductions.length; i++) {
            let user_info = (inductions[i].additional_data.additional_data || {}).user_info || {};
            let updated_one = await attachUserDocument(inductions[i], {
                user_ref: inductions[i].user_ref,
                last_name: user_info.last_name,
                country_code: requestPayload.country_code,
                parent_company: (inductions[i].additional_data && inductions[i].additional_data.project).parent_company,
            }, true, false,true, false);
            updated_records.push({
                id: updated_one.id,
            });
        }
        return ResponseService.successResponse(res, {updated_records, count: updated_records.length});
    },

    exportInductedUsersToProcore: async (req, res) => {
        let requestPayload = _.pick((req.body || {}), [
            'project_ref',
            'id'
        ]);
        if(!requestPayload.project_ref){
            return ResponseService.successResponse(res, {outcome: 'Invalid project details'});
        }
        sails.log.info('seeder, export approved inductions of project_id', requestPayload.project_ref, 'id', requestPayload.id);
        let inductions = await sails.models.inductionrequest_reader.find({
            where: {
                ...requestPayload,
                status_code: 2,
            }
        });
        sails.log.info('seeder, total inductions to process', inductions.length);
        if(inductions.length){
            let outcome = await ProcoreService.exportUsersOnInductionApproval(requestPayload.project_ref, inductions);
            sails.log.info('seeder, induction export completed');
            return ResponseService.successResponse(res, {outcome});
        }
        return ResponseService.successResponse(res, {outcome: 'No records'});
    },

    exportManpowerLogsToProcore: async (req, res) => {
        let projects = (req.body.projects || []);
        let target_day = dayjs(req.body.target_date, dbDateFormat_YYYY_MM_DD);
        sails.log.info("Export Manpower logs of all projects for date: ", target_day.format(dbDateFormat_YYYY_MM_DD));
        let out = await ProcoreService.exportManpowerLogsPerCompany(target_day, (projects.length ? projects : null));
        return ResponseService.successResponse(res, {target_day, out});
    },

    seedInspectionTourChecklist: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        try {
            await sails.models.inndexsetting.destroy({
                name: { in: [ "inspection_tour_common_checklist", "inspection_tour_rail_checklist", "inspection_tour_industrial_checklist" ] }
            });
            sails.log.info('truncate success');

            sails.log.info('creating inspection tour checklist in inndex setting.');

            let inspectionTourChecklist = await sails.models.inndexsetting.createEach([
                {name: "inspection_tour_common_checklist", value: DataToSeedService.metaInspectionTourCommonChecklist},
                {name: "inspection_tour_rail_checklist", value: DataToSeedService.metaInspectionTourRailChecklist},
                {name: "inspection_tour_industrial_checklist", value: DataToSeedService.metaInspectionTourIndustrialChecklist}
            ]);

            if(inspectionTourChecklist)  {
                sails.log.info('Created inspection tour checklist successfully.');
                return ResponseService.successResponse(res, inspectionTourChecklist);
            }

            sails.log.info('Failed to create inspection tour checklist');
            return ResponseService.errorResponse(res, sails.__('Failed to create inspection tour checklist'));
        } catch (failure){
            sails.log.info('Failed to create inspection tour checklist', failure)
            return ResponseService.errorResponse(res, sails.__('Failed to create inspection tour checklist'), failure);
        }
    },

    seedVehicleAssetInspectionChecklist: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        //Note: Add checklist alphabatically.
        let vehicleAssetInspectionChecklist = {
            "general_checklist": {},
            "360_excavator_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and working ROPS structure operators seat and restraining system are complete and secure"
                },
                "track_condition":{
                    "title":"Track condition",
                    "info":"correct tension & check condition of grousers"
                },
                "guards":{
                    "title":"Guards",
                    "info":"In position & suitable condition"
                },
                "rated_capacity_indicator":{
                    "title":"Rated Capacity Indicator (RCI)",
                    "info":"Fully operational & Lifting Duty Chart in place"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "quick_hitch_mechanisms":{
                    "title":"Quick Hitch Mechanisms",
                    "info":"Checked and in full working order"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "backhoe_excavator_checklist":{
                "front_horn":{
                    "title":"Front horn in working condition",
                    "info":""
                },
                "reverse_horn":{
                    "title":"Reverse horn in working condition",
                    "info":""
                },
                "head_and_tail_lamps":{
                    "title":"Head & tail lamps (for working at night) in working condition",
                    "info":""
                },
                "hydraulic_oil_leakage":{
                    "title":"Any Leakage of oil in hydraulic systems observed?",
                    "info":""
                },
                "loose_bolts_or_pins":{
                    "title":"Are any bolts / connecting pins in the loose condition in JCB?",
                    "info":""
                },
                "rearview_mirror":{
                    "title":"Is the rearview mirror without damage?",
                    "info":""
                },
                "hydraulic_oil_level":{
                    "title":"Is sufficient Hydraulic oil level available?",
                    "info":""
                },
                "structure_beam":{
                    "title":"Structure beam condition without damage / cut / crack",
                    "info":""
                },
                "oil_or_grease_spillage":{
                    "title":"Diesel, oil, and grease spillage were observed",
                    "info":""
                },
                "outriggers_working":{
                    "title":"Outriggers in working condition",
                    "info":""
                },
                "registration_cert":{
                    "title":"Registration Certificate available",
                    "info":""
                },
                "vehicle_insurance":{
                    "title":"Vehicle Insurance available",
                    "info":""
                },
                "driver_license":{
                    "title":"Driver License available",
                    "info":""
                },
                "puc_cert":{
                    "title":"PUC available",
                    "info":""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "cherry_picker_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "all_fittings":{
                    "title":"All Fittings",
                    "info":"including attachments & safety devices - intact and free from defects"
                },
                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "body_harness":{
                    "title":"Body Harness",
                    "info":"Check anchor points for signs of damage or wear"
                },
                "battery_condition":{
                    "title":"Battery Condition",
                    "info":"if electric machine"
                },
                "outrigger_operation":{
                    "title":"Outrigger operation",
                    "info":"if applicable"
                },
                "entrapment_protection_device":{
                    "title":"Entrapment Protection Device",
                    "info":"a suitable device has been fitted"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "concrete_pump_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "concrete_mixer_truck_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure"
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },

                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"In place and properly adjusted and working"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "check_service":{
                    "title":"Check service",
                    "info":"In date"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "crane_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "dozer_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure."
                },
                "all_fittings":{
                    "title":"All Fittings / Attachments / Safety Devices",
                    "info":"Intact and free from defects"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"Clearly marked and function correctly. Lift duty chart available "
                },
                "blade_attachments":{
                    "title":"Blade / Winch / Ripper Attachments",
                    "info":"Good working order"
                },
                "cab_console":{
                    "title":"Cab Console",
                    "info":"Check for any warning light display and gauges to be in the correct operating range"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "dumper_truck_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and working ROPS structure operators seat and restraining system are complete and secure"
                },
                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"clearly marked and function correctly. No excessive play in steering"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "forklift_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure."
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },
                "cab_glass":{
                    "title":"Cab Glass",
                    "info":"No extensive damage and full visibility maintained"
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel Security",
                    "info":"Visually check all weheel nut indicators are in-place and correctly aligned"
                },
                "all_guards":{
                    "title":"All guards",
                    "info":"In position and secure"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"Clearly marked and function correctly. Lift duty chart available"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "forward_tipping_dumper_checklist": {
                "front_horn":{
                    "title":"Front horn in working condition",
                    "info":""
                },
                "reverse_horn":{
                    "title":"Reverse horn in working condition",
                    "info":""
                },
                "head_and_tail_lamps":{
                    "title":"Head & tail lamps (for working at night) in working condition",
                    "info":""
                },
                "hydraulic_oil_leakage":{
                    "title":"Any Leakage of oil in hydraulic systems observed?",
                    "info":""
                },
                "loose_bolts_or_pins":{
                    "title":"Are any bolts / connecting pins in the loose condition in JCB?",
                    "info":""
                },
                "rearview_mirror":{
                    "title":"Is the rearview mirror without damage?",
                    "info":""
                },
                "hydraulic_oil_level":{
                    "title":"Is sufficient Hydraulic oil level available?",
                    "info":""
                },
                "structure_beam":{
                    "title":"Structure beam condition without damage / cut / crack",
                    "info":""
                },
                "oil_or_grease_spillage":{
                    "title":"Diesel, oil, and grease spillage were observed",
                    "info":""
                },
                "outriggers_working":{
                    "title":"Outriggers in working condition",
                    "info":""
                },
                "registration_cert":{
                    "title":"Registration Certificate available",
                    "info":""
                },
                "vehicle_insurance":{
                    "title":"Vehicle Insurance available",
                    "info":""
                },
                "driver_license":{
                    "title":"Driver License available",
                    "info":""
                },
                "puc_cert":{
                    "title":"PUC available",
                    "info":""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "grab_lorry":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "hiab_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure"
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },
                "cab_glass":{
                    "title":"Cab Glass",
                    "info":"No extensive damage and full visibility maintained"
                },
                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"In place and properly adjusted and working"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"clearly marked and function correctly."
                },
                "crane_operating_controls":{
                    "title":"Crane Operating Controls",
                    "info":"Functioning correctly"
                },
                "check_service":{
                    "title":"Check service",
                    "info":"In date"
                },
                "technograph":{
                    "title":"Technograph",
                    "info":"Functioning correctly - speed limiting device working correctly"
                },
                "rated_capacity_indicator":{
                    "title":"Rated Capacity Indicator (RCI)",
                    "info":"Fully operational & Lifting Duty Charts in place"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "hoist_checklist":{
                "operation":{
                    "title":"Operation of landing flaps, gates and interlocks",
                    "info":""
                },
                "base_enclosure":{
                    "title":"Base enclosure gates and interlocks",
                    "info":""
                },
                "hoistway_clear":{
                    "title":"Hoistway clear of obstructions",
                    "info":""
                },
                "mast_ties":{
                    "title":"Mast ties are secure",
                    "info":""
                },
                "operating_controls":{
                    "title":"Check operating controls",
                    "info":""
                },
                "emergency_controls":{
                    "title":"Emergency controls",
                    "info":""
                },
                "mains_isolator":{
                    "title":"Mains isolator switch operation and condition",
                    "info":""
                },
                "operation_lc":{
                    "title":"Operation of upper and lower limit switches",
                    "info":""
                },
                "operation_of_trailing":{
                    "title":"Operation of trailing cable storage system",
                    "info":""
                },
                "check_cable_guide":{
                    "title":"Check cable guide springs are intact",
                    "info":""
                },
                "check_brake":{
                    "title":"Check brake operates normally",
                    "info":""
                },
                "no_unusual_noises":{
                    "title":"No unusual noises from motor, gearbox etc",
                    "info":""
                },
                "notices":{
                    "title":"Notices - instruction, operating and warning are in place",
                    "info":""
                },
                "operation_of_audible":{
                    "title":"Operation of audible or visual warning alarms",
                    "info":""
                },
                "base_enclosure_is":{
                    "title":"Base enclosure is clear of debris",
                    "info":""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "laser_screed_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "loading_shovel_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure."
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"Clearly marked and function correctly. Lift duty chart available "
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel Security",
                    "info":"Visually check all weheel nut indicators are in-place and correctly aligned"
                },
                "differential_lock":{
                    "title":"Differential Lock",
                    "info":"Ensure differential lock is out unless the machine is being used in soft gorund"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"In place and properly adjusted and working"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensors or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "mobile_column_lift_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"Free of bent, dented or broken parts"
                },
                "rollers":{
                    "title":"Rollers",
                    "info":"No chunks missing, loose wheel nuts, rollers are smooth and free of plastic/debris"
                },
                "forks":{
                    "title":"Forks",
                    "info":"Not bent, damaged or worn"
                },
                "mast":{
                    "title":"Mast (inc. chains)",
                    "info":"Free of damage, signs of bending, no loose chains"
                },
                "batteries":{
                    "title":"Batteries",
                    "info":"Sufficient charge available (Green)"
                },
                "welds_integrity":{
                    "title":"Welds Integrity",
                    "info":"No visible cracks between metals"
                },
                "hydraulic_oil":{
                    "title":"Hydraulic Oil",
                    "info":"Free from leaks"
                },
                "floor":{
                    "title":"Floor",
                    "info":"Surface is sound, suitable for lifting"
                },
                "mode_change":{
                    "title":"Mode Change",
                    "info":"Wheels realign smoothly, no sticking"
                },
                "travel":{
                    "title":"Travel",
                    "info":"No unusual noise, smooth changes"
                },
                "steering":{
                    "title":"Steering",
                    "info":"No excessive play or restriction in steering"
                },
                "control_panel":{
                    "title":"Control Panel",
                    "info":"Operate all intended paired lifters and mechanical locks engage"
                },
                "hydraulic_control":{
                    "title":"Hydraulic Control",
                    "info":"Forks-Lift/Lower, mast – up/down"
                },
                "wind_speed":{
                    "title":"Wind Speed",
                    "info":"Wind speed is less than 13 mph/6m/s"
                }
            },
            "nano_lift_checklist": {
                "examination_certificate": {
                    "title": "Current through examination certificate (within last six months)",
                    "info": ""
                },
                "operator_manual": {
                    "title": "Manufacturer's operator manual",
                    "info": ""
                },
                "rescue_plan": {
                    "title": "Rescue plan",
                    "info": ""
                },
                "wheel_security": {
                    "title": "Wheel security (nuts, retainers: loose, damaged, missing)",
                    "info": ""
                },
                "tyre_pressure": {
                    "title": "Tyre pressure (pneumatic, foam filled or solid)",
                    "info": ""
                },
                "rims_security": {
                    "title": "Cuts, splits, exposed braiding damaged rims",
                    "info": ""
                },
                "fluid_level": {
                    "title": "Fluid levels (engine oil, coolant, fuel)",
                    "info": ""
                },
                "fluid_leakage": {
                    "title": "Fluid leakage on ground & ground engine",
                    "info": ""
                },
                "battery_check": {
                    "title": "Battery (electrolyte, security & charging plug condition)",
                    "info": ""
                },
                "hydraulic_fluid": {
                    "title": "Hydraulic fluid level",
                    "info": ""
                },
                "hydraulic_leaks": {
                    "title": "Leaks (hoses, pipe connections, rams, cylinders)",
                    "info": ""
                },
                "hoses_security_check": {
                    "title": "Hoses and Cables - Security & condition (cuts, chaffing, bulges)",
                    "info": ""
                },
                "cable_secutity_check": {
                    "title": "Power track cable trays (free from damage & debris)",
                    "info": ""
                },
                "stabilisers_check": {
                    "title": "Outriggers / Stabilisers - General condition, pins/retainers, footplate",
                    "info": ""
                },
                "spreader_check": {
                    "title": "Spreader plates (present, condition, secure for travel)",
                    "info": ""
                },
                "interlocks_check": {
                    "title": "Interlocks (functioning, engaged)",
                    "info": ""
                },
                "chassis_condition": {
                    "title": "Chassis/Boom and Scissor Pack - General condition (damage, misalignment, corrosion)",
                    "info": ""
                },
                "weld_cracks": {
                    "title": "Cracks in weld",
                    "info": ""
                },
                "pins_check": {
                    "title": "Pins, retainers & chains (security, signs of wear)",
                    "info": ""
                },
                "canopies_check": {
                    "title": "Canopies, guards, engine covers (security & condition)",
                    "info": ""
                },
                "cage_check": {
                    "title": "Platform or Cage - Steps for access/egress (secure, undamaged, clear)",
                    "info": ""
                },
                "gate_check": {
                    "title": "Entrance gate, guard rails & retaining pins",
                    "info": ""
                },
                "harness_check": {
                    "title": "Harness anchor points",
                    "info": ""
                },
                "debris_check": {
                    "title": "Clear of rubbish, debris & obstructions",
                    "info": ""
                },
                "decals_check": {
                    "title": "Decals and Signage - ID plate, safety, warning and information decals (legible)",
                    "info": ""
                },
                "control_check": {
                    "title": "Controls (identification decals, directional arrows)",
                    "info": ""
                },
                "platoform_check": {
                    "title": "Platform loads (SWL, max. wind speed, max. number of persons)",
                    "info": ""
                },
                "security_check": {
                    "title": "Security device (power isolator, keypad, smart card)",
                    "info": ""
                },
                "ignition_check": {
                    "title": "Function enable (ignition key, foot switch, hold to run device)",
                    "info": ""
                },
                "emergency_check": {
                    "title": "Emergency stops and emergency lowering system",
                    "info": ""
                },
                "switches_check": {
                    "title": "All switches, function controls (move freely, do not stick)",
                    "info": ""
                },
                "lifting_check": {
                    "title": "Lifting functions (raise, lower, slew, tele-out, tele-in)",
                    "info": ""
                },
                "travel_check": {
                    "title": "Travel functions (forward, reverse, steer, brakes)",
                    "info": ""
                },
                "drive_check": {
                    "title": "Elevated drive speed (reduced or prevented)",
                    "info": ""
                },
                "warnings_check": {
                    "title": "Lights, beacons, warning devices",
                    "info": ""
                },
                "alarm_check": {
                    "title": "Alarms (tilt, descent and travel)",
                    "info": ""
                },
                "limit_check": {
                    "title": "Limit switches (e.g. descent, load, outreach, rotation)",
                    "info": ""
                },
                "pothole_check": {
                    "title": "Pothole protection device (fully deploys and retracts)",
                    "info": ""
                },
                "axle_check": {
                    "title": "Oscillating axle locks, extending axles",
                    "info": ""
                },
                "accessory_check": {
                    "title": "Accessories, power to platform, extending decks",
                    "info": ""
                },
                "jacks_check": {
                    "title": "Jacks-legs, stabilisers, outriggers, levelling devices",
                    "info": ""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "paver_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "all_fittings":{
                    "title":"All Fittings / Attachments / Safety Devices",
                    "info":"Intact and free from defects"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"Clearly marked and function correctly. Lift duty chart available"
                },
                "sufficiently_lubricated":{
                    "title":"Sufficiently Lubricated",
                    "info":"All applicable bearings & brushes etc"
                },
                "fuel_filtet":{
                    "title":"Fuel Filter Water Seperator Drain",
                    "info":"Fully operational"
                },
                "fire_extinguishers":{
                    "title":"Fire Extinguishers",
                    "info":"Installed and in date"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "peco_lift_checklist": {
                "visual_check": {
                    "title": "Visually inspect the lift for any signs of damage to handrails, platform tray, chassis and mast lifting structure including mast fixing bolts",
                    "info": ""
                },
                "castor_check": {
                    "title": "Check castor and wheels rotate freely and are undamaged",
                    "info": ""
                },
                "wheel_check": {
                    "title": "Check castor and wheel fixings are secure",
                    "info": ""
                },
                "chassis_check": {
                    "title": "Check that the front rubber chassis feet (pads) are undamaged and fixings are secure",
                    "info": ""
                },
                "level_check": {
                    "title": "Check spirit level is intact and bubble is centred to ensure machine is level",
                    "info": ""
                },
                "gates_check": {
                    "title": "Check gates, gate hinges, hinge springs and hinge fixings are undamaged and that gates open and close correctly",
                    "info": ""
                },
                "feet_check": {
                    "title": "Step into basket; check machine sinks down to rest on front rubber pads (feet)",
                    "info": ""
                },
                "flywheel_check": {
                    "title": "When standing in the basket: check 'fly-wheel' operating handle works correctly. Hold handle firmly and pull operating knob towards you, release, knob should spring back to lock wheel. Repeat but turn handle once clockwise with knob held pulled towards you. Wheel should turn freely. Turn once anti-clockwise to come down.",
                    "info": ""
                },
                "loweringtool_check": {
                    "title": "Check emergency lowering tool is attached on the chassis",
                    "info": ""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "piling_rig_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "air_drill_valve":{
                    "title":"Air Drill Valve",
                    "info":"opens, closes and blows out regularly"
                },
                "rope_swivel":{
                    "title":"Rope swivel",
                    "info":"rotating freely and greased"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"fully operational"
                },
                "rigger_computer":{
                    "title":"Rigger computer",
                    "info":"working correctly"
                },
                "date_logger":{
                    "title":"Date Logger",
                    "info":"working correctly"
                },
                "instrumentation":{
                    "title":"Instrumentation",
                    "info":"Pump/Stroke, Auger Diameter, Calibration"
                },
                "emergency_stop_button":{
                    "title":"Emergency stop button",
                    "info":"fully operational. Test all day"
                },
                "limit_switched":{
                    "title":"Limit switched",
                    "info":"working correctly"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "trench_roller_checklist":{
                "alarms":{
                    "title":"Alarms",
                    "info":""
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":""
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":""
                },
                "coolant_level":{
                    "title":"Coolant Level",
                    "info":""
                },
                "isolator":{
                    "title":"Isolator",
                    "info":""
                },
                "condition":{
                    "title":"Condition",
                    "info":""
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":""
                },
                "beacon":{
                    "title":"Beacon",
                    "info":""
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":""
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":""
                },
                "service_level_indicator":{
                    "title":"Service Level Indicator",
                    "info":""
                },
                "operation_of":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":""
                },
                "operatives":{
                    "title":"Operatives",
                    "info":""
                },
                "structure":{
                    "title":"Structure",
                    "info":""
                },
                "test_certificates":{
                    "title":"Test Certificates",
                    "info":""
                },
                "transmission_safety_device":{
                    "title":"Transmission Safety Device",
                    "info":""
                },
                "remote_control":{
                    "title":"Remote Control in good working order and all present & correct",
                    "info":""
                }
            },
            "ride_on_roller_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working. ROPS structure operators seat and restraining system are complete and secure."
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "transmission_safety_device":{
                    "title":"Transmission Safety Device",
                    "info":"If fitted - Fully operational"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"In place and properly adjusted and working"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensors or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "scissor_lift_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "all_fittings":{
                    "title":"All Fittings",
                    "info":"including attachments & safety devices - intact and free from defects"
                },
                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "battery_condition":{
                    "title":"Battery Condition",
                    "info":"if electric machine"
                },
                "outrigger_operation":{
                    "title":"Outrigger operation",
                    "info":"if applicable"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "soil_stabilizing_machine_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "suction_excavator_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "telehandler_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and working ROPS structure operators seat and restraining system are complete and secure"
                },
                "cab_glass":{
                    "title":"Cab Glass",
                    "info":"No extensive damage and full visibility maintained"
                },
                "tyre_condition":{
                    "title":"Tyre condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel security",
                    "info":"Visually Check all wheel nut indicators are in-place and correctly aligned"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "operating_controls":{
                    "title":"Operating Controls",
                    "info":"clearly marked and function correctly. Safety lever bar fitted and working"
                },
                "outrigger_legs_jacks":{
                    "title":"Outrigger Legs & Jacks",
                    "info":"Fully operational"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "telescopic_boom_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "all_fittings":{
                    "title":"All Fittings / Attachments / Safety Devices",
                    "info":"Intact and free from defects"
                },
                "all_guards":{
                    "title":"All Guards",
                    "info":"In place and secure"
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel Security",
                    "info":"Visually check all weheel nut indicators are in-place and correctly aligned"
                },
                "all_gauges":{
                    "title":"All Gauges & Warning Devices",
                    "info":"Working sufficiently"
                },
                "body_harness":{
                    "title":"Body Harness Anchor Points",
                    "info":"Check for signs of damage or wear"
                },
                "batter_condition":{
                    "title":"Batter Condition",
                    "info":"If electric machines"
                },
                "outrigger_operation":{
                    "title":"Outrigger Operation",
                    "info":"If applicable"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"In place and properly adjusted and working"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensors or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "tower_crane_checklist":{
                "base_clear":{
                    "title":"Base clear of obstruction/debris",
                    "info":""
                },
                "isolator_box":{
                    "title":"Isolator box clear and unflooded",
                    "info":""
                },
                "tower_mast":{
                    "title":"Tower and mast pins checked",
                    "info":""
                },
                "rail_track":{
                    "title":"Rail track, Stops, cable, fencing",
                    "info":""
                },
                "access_ladders":{
                    "title":"Access ladders/platforms checked",
                    "info":""
                },
                "hoist_break":{
                    "title":"Hoist break (load test)",
                    "info":""
                },
                "visual_hoist":{
                    "title":"Visual hoist rope condition checked",
                    "info":""
                },
                "visual_trolley":{
                    "title":"Visual trolley rope checked",
                    "info":""
                },
                "hook_block_height":{
                    "title":"Hook block height limit checked",
                    "info":""
                },
                "hook_block_safety":{
                    "title":"Hook block safety clip and swivel checked",
                    "info":""
                },
                "operation_of_trolley":{
                    "title":"Operation of trolley brake checked",
                    "info":""
                },
                "trolley_in_out":{
                    "title":"Trolley in and out limits checked",
                    "info":""
                },
                "operation_slewing":{
                    "title":"Operation of slewing brakes checked",
                    "info":""
                },
                "anti_climb":{
                    "title":"Anti-climb and hatch checked?",
                    "info":""
                },
                "slew_ring":{
                    "title":"Slew ring teeth checked",
                    "info":""
                },
                "security_fan":{
                    "title":"Security fan/base enclosure checked",
                    "info":""
                },
                "base_counter":{
                    "title":"Base and counter weight ballast checked",
                    "info":""
                },
                "luffing_in_out":{
                    "title":"Luffing in and out limits checked",
                    "info":""
                },
                "luffing_brake":{
                    "title":"Luffing brake (load test)",
                    "info":""
                },
                "visual_luffing":{
                    "title":"Visual luffing rope condition checked",
                    "info":""
                },
                "swl_indicators":{
                    "title":"SWL indicators checked",
                    "info":""
                },
                "controls_respond":{
                    "title":"Controls respond correctly",
                    "info":""
                },
                "deadman_controls":{
                    "title":"Deadman controls checked",
                    "info":""
                },
                "windscreen_wipers":{
                    "title":"Windscreen wipers checked",
                    "info":""
                },
                "cab_gauges":{
                    "title":"Cab gauges and warning lights",
                    "info":""
                },
                "windspeed_indicators":{
                    "title":"Windspeed indicators checked",
                    "info":""
                },
                "tidiness_cleanliness":{
                    "title":"Tidiness and cleanliness checked",
                    "info":""
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "tracked_screen_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "tractor_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"The machine is free from accumulation of debris and is able to operate freely"
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":"Vehicle appropriate for the activities being undertaken / planned"
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":"Available on-site for reference"
                },
                "test_certificates":{
                    "title":"Thorough Examination / Test Certificates",
                    "info":"In date and available as applicable"
                },
                "warning_information":{
                    "title":"Warning Information & Decals",
                    "info":"Safety information clearly marked and clearly understandable"
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":"engine / hydraulic / coolant and fuel levels are ok and all systems are free from leaks"
                },
                "structure":{
                    "title":"Structure",
                    "info":"Chassis / stabalizers / scissors / booms in good condition with no visible damage and pins all fitted"
                },
                "driver_access":{
                    "title":"Driver Access & Egress",
                    "info":"A means of driver access / aggress to and from the vehicle – i.e. steps / handrails"
                },
                "brakes_steering":{
                    "title":"Brakes / Steering / Drive Shafts",
                    "info":"In good condition and working correctly including parking brake"
                },
                "lights_indicators":{
                    "title":"Lights & Indicators",
                    "info":"Working correctly and fully functional"
                },
                "alarms_horn":{
                    "title":"Alarms / Horn / Audible Reversing",
                    "info":"working correctly and fully functional"
                },
                "operatives":{
                    "title":"Operatives",
                    "info":"Drivers or users trained and qualified and able to operate the vehicle safely"
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":"Any additional checks as required by manufacturer undertaken satisfactorily"
                },
                "seatbelts":{
                    "title":"Seatbelts",
                    "info":"Fitted and Working"
                },
                "number_plate":{
                    "title":"Number Plate (if applicable)",
                    "info":"Present and Visible"
                },
                "all_fittings":{
                    "title":"All Fittings / Attachments / Safety Devices",
                    "info":"Intact and free from defects"
                },
                "all_guards":{
                    "title":"All guards",
                    "info":"In position and secure"
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel Security",
                    "info":"Visually check all weheel nut indicators are in-place and correctly aligned"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "mirrors":{
                    "title":"Mirrors",
                    "info":"in place and properly adjusted and working"
                },
                "collision_avoidance_system":{
                    "title":"Operation of Forward Collision Avoidance System",
                    "info":"Fully operational - proximity sensor or similar approved"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
                "plant_hours":{
                    "title":"Plant Hours",
                    "info":"",
                    "type":"text"
                },
                "fall_protection": {
                    "title": "Fall Protection",
                    "info":""
                }
            },
            "tractor_bowser_checklist":{
                "inspect_all_hydraulic":{
                    "title":"Inspect all hydraulic hoses & fittings for rubbing, chafing, weather checking and/or leaking",
                    "info":""
                },
                "inspect_water":{
                    "title":"Inspect water tank for leaks or abnormal surface defects",
                    "info":""
                },
                "level_gauge":{
                    "title":"Level Gauge",
                    "info":""
                },
                "tyres":{
                    "title":"Tyres",
                    "info":""
                },
                "all_oil_levels":{
                    "title":"All Oil Levels",
                    "info":""
                },
                "couplings_hitch":{
                    "title":"Couplings & Hitch",
                    "info":""
                },
                "condition":{
                    "title":"Condition",
                    "info":""
                },
                "fit_for_purpose":{
                    "title":"Fit for purpose",
                    "info":""
                },
                "beacon":{
                    "title":"Beacon",
                    "info":""
                },
                "manufacturer_instructions":{
                    "title":"Manufacturer Instructions",
                    "info":""
                },
                "manufacturer_requirements":{
                    "title":"Manufacturer Requirements",
                    "info":""
                },
                "number_plate":{
                    "title":"Number Plate",
                    "info":"if applicable"
                },
                "reversing_camera":{
                    "title":"Reversing Camera",
                    "info":""
                },
                "operatives":{
                    "title":"Operatives",
                    "info":""
                },
                "brake_lights":{
                    "title":"Brake Lights / Indicators",
                    "info":""
                },
                "test_certificates":{
                    "title":"Test Certificates",
                    "info":""
                }
            },
            "utility_vehicle_checklist":{
                "lights":{
                    "title":"Lights",
                    "info":"Working correctly and fully functioning"
                },
                "horn":{
                    "title":"Horn",
                    "info":"Working correctly and fully functioning"
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "bodywork_glass_mirrors":{
                    "title":"Bodywork, Glass & Mirrors",
                    "info":"All free of debris"
                },
                "breaks_break_lights":{
                    "title":"Brakes and Brake Lights",
                    "info":"In good condition and working correctly"
                },
                "seat_belt":{
                    "title":"Seat Belt",
                    "info":"Fitted and working"
                }
            },
            "vacuum_lifter_checklist":{
                "condition":{
                    "title":"Condition",
                    "info":"Free of bent, dented or broken parts"
                },
                "bolt_screw":{
                    "title":"Bolt & Screw",
                    "info":"All in place and tightened"
                },
                "drain_water_trap ":{
                    "title":"Drain water trap",
                    "info":"In place and working"
                },
                "suction_cups_seals":{
                    "title":"Suction Cups & Seals",
                    "info":"In place and working"
                },
                "hoses_and_connections":{
                    "title":"Hoses and Connections",
                    "info":"In place and operational"
                },
                "indicator_lights":{
                    "title":"Indicator Lights",
                    "info":"Working correctly and fully functional"
                },
                "warning_sounder":{
                    "title":"Warning Sounder",
                    "info":"Working correctly & fully functional"
                },
                "vacuum_control_valve":{
                    "title":"Vacuum Control Valve",
                    "info":"Check operation and integrity"
                },
                "locking_pins":{
                    "title":"Locking Pins",
                    "info":"All in correct locations with retaining clips in place and secure"
                },
                "secondary_fall_safety_straps":{
                    "title":"Secondary Fall Safety Straps",
                    "info":"All in good condition and undamaged"
                }
            },
            "van_checklist":{
                "engine_oil_level":{
                    "title":"Engine Oil Level",
                    "info":"Oil level is ok and system is free from leaks"
                },
                "brake_fluid_level":{
                    "title":"Brake Fluid Level",
                    "info":"Fluid level is ok and system is free from leaks"
                },
                "steering_fluid_level":{
                    "title":"Steering Fluid Level",
                    "info":"Fluid level is ok and system is free from leaks"
                },
                "coolant_level":{
                    "title":"Coolant Level",
                    "info":"Coolant level is ok and system is free from leaks"
                },
                "washer_wipers":{
                    "title":"Washer & Wipers",
                    "info":"Fluid level is ok and wipers in good working order"
                },
                "lights":{
                    "title":"Lights",
                    "info":"Working correctly and fully functioning"
                },
                "horn":{
                    "title":"Horn",
                    "info":"Working correctly and fully functioning"
                },
                "tyre_condition":{
                    "title":"Tyre Condition",
                    "info":"Sufficient tread depth and correct pressure"
                },
                "wheel_security":{
                    "title":"Wheel Security",
                    "info":"Visiually check all wheel nut indicators are in-place and correctly aligned"
                },
                "bodywork_glass_mirrors":{
                    "title":"Bodywork, Glass & Mirrors",
                    "info":"All free of debris"
                },
                "breaks_break_lights":{
                    "title":"Brakes and Brake Lights",
                    "info":"In good condition and working correctly"
                },
                "seat_belt":{
                    "title":"Seat Belt",
                    "info":"Fitted and working"
                },
                "fire_extinguisher":{
                    "title":"Fire Extinguisher",
                    "info":"In place and in date"
                },
                "first_aid_kit":{
                    "title":"First Aid Kit",
                    "info":"Well stocked and all items in date"
                },
                "cameras":{
                    "title":"Cameras",
                    "info":"Fully operational"
                },
                "warning_devices_alarms":{
                    "title":"Warning Devices & Alarms",
                    "info":"Working correctly and fully functioning"
                },
                "mileage":{
                    "title":"Mileage",
                    "info":"",
                    "type":"text"
                },
                "fuel_purchased_amount":{
                    "title":"Fuel Purchased (£)",
                    "info":"",
                    "type":"text"
                },
                "fuel_purchased_volume":{
                    "title":"Fuel Purchased (Litres)",
                    "info":"",
                    "type":"text"
                },
                "ddblue": {
                    "title": "Adblue",
                    "info":""
                },
            }
        };

        try {
            let truncated = await sails.models.inndexsetting.destroy({
                name: 'vehicle_asset_inspection_checklist'
            });
            sails.log.info('truncate success');

            let createRequestChecklist = {name: "vehicle_asset_inspection_checklist", value: vehicleAssetInspectionChecklist};
            sails.log.info('creating vehicle asset inspection checklist in inndex setting.');

            let inspectionChecklist = await sails.models.inndexsetting.create(createRequestChecklist);

            if(inspectionChecklist)  {
                sails.log.info('Created checklist successfully.');
                return ResponseService.successResponse(res, inspectionChecklist);
            }

            sails.log.info('Failed to create checklist');
            return ResponseService.errorResponse(res, sails.__('Failed to create checklist'));
        } catch (failure){
            sails.log.info('Failed to create checklist', failure);
            return ResponseService.errorResponse(res, sails.__('Failed to create checklist'), failure);
        }
    },

    seedMetaDataByKey: async (req, res) => {
        let meta_key = (req.body.meta_key || '-');
        let meta_data = DataToSeedService[meta_key];

        sails.log.info('Request for seeding meta-key', meta_key);
        if(!meta_data){
            return ResponseService.errorResponse(res, 'Provided meta key does not exists', {meta_key, available: Object.keys(DataToSeedService)});
        }

        await sails.models.inndexsetting.destroy({name: meta_key});
        sails.log.info('record removed, success');

        sails.log.info('creating meta data in inndex setting.');

        let record = await sails.models.inndexsetting.create({name: meta_key, value: meta_data});

        sails.log.info('meta record created successfully', record.id);
        return ResponseService.successResponse(res, {record});

    },

    // Sample Payload for seedTypeOfAssets('post /api/seed/type-of-assets');
    // We can use multiple assets for each local(asset_list)
    // {
    //     "asset_category": "type_of_asset_vehicles | type_of_asset_equipments | type_of_temporary_works",
    //     "asset_list": {
    //         "en_gb": [{"key":"360_excavator","value":"360 Excavator","has_specific_checklist":true}],
    //         "en_us": [{"key":"360_excavator","value":"360 Excavator ","has_specific_checklist":true}],
    //         "de_de": [{"key":"360_excavator","value":"360 Excavator","has_specific_checklist":true}]
    //     },
    //     "locales": [
    //         "en_gb",
    //         "en_us",
    //         "de_de"
    //     ],
    //     "force_update": true/false
    // }
    seedTypeOfAssets: async (req, res) => {
        let payload = trimObject(req.body);
        const { asset_category, asset_list, locales, force_update } = payload;
        let settingName;
        let updatedSettings = [];
        let items_per_local = {};
        for (let i = 0; i < locales.length; i++) {
            let assetValues = asset_list[locales[i]];
            settingName = asset_category.concat("_", locales[i]);
            sails.log.info(`Fetching list of assets for ${settingName} from inndex setting table`);
            let availableAssets = await sails.models.inndexsetting.findOne({
                where: {
                    name: settingName,
                }
            });

            // Creating a new row as it is not available in the table
            if (!availableAssets) {
                sails.log.info(`Creating a new row for ${settingName} as it is not available in the inndex setting table`);
                assetValues.sort((a, b) => a.key.localeCompare(b.key));
                const createRequestChecklist = { name: settingName, value: assetValues };
                createdSetting = await sails.models.inndexsetting.create(createRequestChecklist);
                if (!createdSetting) {
                    return ResponseService.errorResponse(res, 'Failed to added ', settingName);
                }
                items_per_local[settingName] =  createdSetting.value.length;
                updatedSettings.push(createdSetting);
                sails.log.info(`Created ${settingName} successfully.`);
                continue;
            }

            // Proceeding update as the row is available in the table
            // Checking & updating the asset items as per availability
            availableAssets.value.filter((availableValue, i) => {
                assetValues.some((newValue, j) => {
                    if(newValue.key === availableValue.key){
                        if(force_update){
                            availableAssets.value[i] = assetValues[j];
                        }
                        assetValues.splice(j, 1);
                    }
                });
            });

            // Pushing asset items in the list as it is not available
            if(assetValues.length){
                sails.log.info(`Pushing new asset values in the ${settingName} row`);
                availableAssets.value = [...availableAssets.value, ...assetValues];
            }

            // Updating asset list for the specific setting.
            sails.log.info(`Updating & Sorting ${settingName} asset values`);
            availableAssets.value.sort((a, b) => a.key.localeCompare(b.key));
            let updatedAssets = await sails.models.inndexsetting
                .updateOne({
                    where: {
                        name: settingName
                    },
                })
                .set({
                    value: availableAssets.value
                });
            sails.log.info(`Updated ${settingName} successfully`);
            items_per_local[settingName] =  updatedAssets.value.length;
            updatedSettings.push(updatedAssets);
        }
        return ResponseService.successResponse(res, {items_per_local, updatedSettings});
    },
   

    correctVehicleAssetRecords: async (req, res) => {
        let assetVehicles =  await sails.models.projectassetvehicles.find({
            select: ["examination_cert_ref", "service_certificate_ref", "mot_certificate_ref"]
        });

        sails.log.info(`Number of vehicle ${assetVehicles.length}`);
        if (assetVehicles.length) {
            for (let key in assetVehicles) {
                let assetVehicle = assetVehicles[key];
                await sails.models.projectassetvehicles.updateOne({id: assetVehicle.id}).set({
                    examination_certificates: (assetVehicle.examination_cert_ref && typeof assetVehicle.examination_cert_ref === 'number') ? [assetVehicle.examination_cert_ref ] : [],
                    service_certificates: (assetVehicle.service_certificate_ref && typeof assetVehicle.service_certificate_ref === 'number') ? [assetVehicle.service_certificate_ref ] : [],
                    mot_certificates: (assetVehicle.mot_certificate_ref && typeof assetVehicle.mot_certificate_ref === 'number') ? [assetVehicle.mot_certificate_ref ] : []
                });
            }
        }

        sails.log.info('All records has been updated.');
        return ResponseService.successResponse(res, {message:'All records has been updated.'});
    },

    seedAssetEquipmentItems: async (req, res) => {
        let itemsTruncated = await sails.models.inndexsetting.destroy({
            name: 'asset_equipment_items'
        });
        sails.log.info('truncated equipment items.');

        let assetEquipmentItems = {
            "access_equipment_items": ['Ladder', 'Mobile Scaffold Tower', 'Podium Stepladder'],
            "fall_arrest_systems_items": ['Full Body Harness', 'Lanyard']
        };
        let createItemsRequest = {name: "asset_equipment_items", value: assetEquipmentItems};
        sails.log.info('adding asset_equipment_items in inndex setting.');

        assetEquipmentItems = await sails.models.inndexsetting.create(createItemsRequest);

        if(assetEquipmentItems)  {
            sails.log.info('Added asset_equipment_items successfully.');
            return ResponseService.successResponse(res, assetEquipmentItems);
        }

        sails.log.info('Failed to added asset_equipment_items');
        return ResponseService.errorResponse(res, 'Failed to added asset_equipment_items');
    },

    seedEquipmentAssetInspectionChecklist: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed.');
        }

        //Note: Add checklist alphabetically.
        let equipmentAssetInspectionChecklist = {
            "ae_mobile_scaffold_tower_checklist": DataToSeedService.metaAccessEquipmentMobileScaffoldTowerChecklist,
            "ae_podium_stepladder_checklist": DataToSeedService.metaAccessEquipmentPodiumStepladderChecklist,
            "ae_ladder_checklist": DataToSeedService.metaAccessEquipmentLadderChecklist,
        };

        try {
            let truncated = await sails.models.inndexsetting.destroy({
                name: 'equipment_asset_inspection_checklist'
            });
            sails.log.info('truncate success');

            let createRequestChecklist = {name: "equipment_asset_inspection_checklist", value: equipmentAssetInspectionChecklist};
            sails.log.info('creating equipment asset inspection checklist in inndex setting.');

            let inspectionChecklist = await sails.models.inndexsetting.create(createRequestChecklist);

            if(inspectionChecklist)  {
                sails.log.info('Created checklist successfully.');
                return ResponseService.successResponse(res, inspectionChecklist);
            }

            sails.log.info('Failed to create checklist');
            return ResponseService.errorResponse(res, sails.__('Failed to create checklist'));
        } catch (failure){
            sails.log.info('Failed to create checklist', failure);
            return ResponseService.errorResponse(res, sails.__('Failed to create checklist'), failure);
        }
    },

    seedTemporaryWorksAssetInspectionChecklist: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        //Note: Add new equipment alphabatically.
        let typeOfTemporaryWorksInspectionChecklist = {
            'mast_climber_checklist': [
                {
                    "category": "Inspection",
                    "question": "Are you “User Competent Person” and deemed competent to conduct a check on the mast climber?",
                    "question_id": 1
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked that no part of the platform, and it’s associated equipment has been tampered with since the Mast climber was last in operation?",
                    "question_id": 2
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked the cleanliness and that there are no general indications of damage?",
                    "question_id": 3
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked that all the mast and ties are secure, paying special attention following serve weather conditions?",
                    "question_id": 4
                },
                {
                    "category": "Inspection",
                    "question": "Have you check that there are no obstacles either within the operating pathway of the platform or which are likely to cause hindrance during operation?",
                    "question_id": 5
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked that all handrails ,guard gates and toe boards are in position and are secure. Ensure any adjustment/replacement is undertaken before operating the platform?",
                    "question_id": 6
                },
                {
                    "category": "Inspection",
                    "question": "Has the area been checked below the platform and free from unwanted debris and materials. Ensuring the platform is suitably fenced off?",
                    "question_id": 7
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked to ensure that all instruction notices, including the maximum safe working load, sign are in position and legible?",
                    "question_id": 8
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked to ensure that trailing power cables is properly un-coiled and routed to the platform safely?",
                    "question_id": 9
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked to ensure the main switch is turned on, check that up and down direction of travel is drive correct for emergency lowering and/or raising the work platform?",
                    "question_id": 10
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked the condition and operation of all electrical limit switches and their functions?",
                    "question_id": 11
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked the functioning of controls and safety devices (emergency stops, anemometers, two-way communication systems, overload/moment detecting and indicating devices, if fitted)?",
                    "question_id": 12
                },
                {
                    "category": "Inspection",
                    "question": "Have you checked the guide rollers between work platform and mast?",
                    "question_id": 13
                },


            ]
        };

        let truncated = await sails.models.inndexsetting.destroy({
            name: 'temporary_work_asset_inspection_checklist'
        });
        sails.log.info('truncate success');

        let createRequestChecklist = {name: "temporary_work_asset_inspection_checklist", value: typeOfTemporaryWorksInspectionChecklist};
        sails.log.info('creating vehicle asset temporary_work_asset_inspection_checklist in inndex setting.');

        checkList = await sails.models.inndexsetting.create(createRequestChecklist);

        if(checkList)  {
            sails.log.info('Added temporary_work_asset_inspection_checklist successfully.');
            return ResponseService.successResponse(res, checkList);
        }

        sails.log.info('Failed to added temporary_work_asset_inspection_checklist');
        return ResponseService.errorResponse(res, sails.__('Failed to added temporary_work_asset_inspection_checklist'));

    },

    fillPhraseFields: async (req, res) => {
        let phraseKey = req.param('phrase_key');
        let phraseVal = req.param('phrase_val');
        let deployCCPhrase = +req.param('deploy_close_call_phrase', false);
        if (!phraseKey || !(phraseVal || deployCCPhrase)) {
            return ResponseService.errorResponse(res, '`phrase_key` & `phrase_val` are required parameters.');
        }

        sails.log.info('Filling phrase data fields for ', phraseKey, ' with value ', phraseVal, (deployCCPhrase ? " closecall phrase" : ''));

        let selectKeys = (deployCCPhrase) ? ['close_call_phrase', 'custom_field'] : ['custom_field'];

        let projects = await sails.models.project.find({select: selectKeys});
        for (let index in projects) {
            sails.log.info('Processing Project, ID:custom_field', projects[index].id, projects[index].custom_field);
            phraseVal = (deployCCPhrase) ? projects[index].close_call_phrase : phraseVal;
            let custom_field = {
                ...projects[index].custom_field,
            };
            custom_field[phraseKey] = phraseVal;

            sails.log.info('project updating with id:data', projects[index].id, custom_field);
            let project = await sails.models.project.updateOne({id: projects[index].id}).set({custom_field: custom_field});
        }
        sails.log.info('Records have been updated.');
        return ResponseService.successResponse(res, {message:'All projects have been updated.'});
    },

    /**
     * Update inspection tour records and inspection builder records for field
     * tagged_company_ref in checklists that left untouched during company merge
     */
    fixCompanyMergeRecords: async (req, res) => {
        //api/seed/fix-company-merge-records?fromCompanyId=10&toCompanyId=1&inspectionType=ib&update=true&t=xxxxxxx
        //api/seed/fix-company-merge-records?fromCompanyId=10&toCompanyId=1&inspectionType=it&update=true&t=xxxxxxx

        let fromCompanyId = +req.param('fromCompanyId');
        let toCompanyId = +req.param('toCompanyId');
        let update = req.param('update');
        let inspectionType = req.param('inspectionType', 'it'); //it: inspection tour and ib: inspection builder

        let meta_data = [];
        let inspectionTourIds = [];
        let updateRequired = false;

        sails.log.info("Inspection Type: ", inspectionType);
        if(inspectionType == 'it') {
            let projectInspectionTours = await sails.models.projectinspectiontour.find({
                select: ['id', 'common_checklist', 'rail_checklist', 'industrial_checklist', 'additional_checklist']
            });
            for(let index in projectInspectionTours) {
                let inspectionTour = projectInspectionTours[index];
                (inspectionTour.common_checklist || []).forEach(function (item, j) {
                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspectionTour.id,
                            checklist: 'common_checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspectionTour.id);

                        updateRequired = true;
                    }
                });

                (inspectionTour.rail_checklist || []).forEach(function (item, j) {
                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspectionTour.id,
                            checklist: 'rail_checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspectionTour.id);

                        updateRequired = true;
                    }
                });

                (inspectionTour.industrial_checklist || []).forEach(function (item, j) {
                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspectionTour.id,
                            checklist: 'industrial_checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspectionTour.id);

                        updateRequired = true;
                    }
                });

                (inspectionTour.additional_checklist || []).forEach(function (item, j) {
                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspectionTour.id,
                            checklist: 'additional_checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspectionTour.id);

                        updateRequired = true;
                    }
                });

                if (updateRequired && update) {
                    sails.log.info(`Updating inspection tour: ${inspectionTour.id}`);
                    let updateRequest = {
                        common_checklist: inspectionTour.common_checklist,
                        rail_checklist: inspectionTour.rail_checklist,
                        industrial_checklist: inspectionTour.industrial_checklist,
                        additional_checklist: inspectionTour.additional_checklist
                    };
                    await sails.models.projectinspectiontour.updateOne({id: inspectionTour.id}).set(updateRequest);
                }
                updateRequired = false;
            }
        } else {
            let projectInspectionBuilder = await sails.models.inspectionbuilderreport.find({
                select: ['id', 'checklist', 'additional_checklist']
            });

            for(let index in projectInspectionBuilder) {
                let inspection = projectInspectionBuilder[index];
                (inspection.checklist || []).forEach(function (item, j) {
                    if (item.heading && item.subheadings) {
                        (item.subheadings || []).forEach(function (subItem, j) {
                            if (subItem.tagged_company_ref && subItem.tagged_company_ref.length && subItem.tagged_company_ref.includes(fromCompanyId)) {
                                let fromCompanyIndex = subItem.tagged_company_ref.indexOf(fromCompanyId);
                                subItem.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                meta_data.push({
                                    id: inspection.id,
                                    checklist: 'checklist',
                                    checklist_item_index: j,
                                    updated_checklist_item: subItem,
                                });
                                inspectionTourIds.push(inspection.id);

                                updateRequired = true;
                            }
                        });

                        return false
                    }

                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspection.id,
                            checklist: 'checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspection.id);

                        updateRequired = true;
                    }
                });

                (inspection.additional_checklist || []).forEach(function (item, j) {
                    if (item.heading && item.subheadings) {
                        (item.subheadings || []).forEach(function (subItem, j) {
                            if (subItem.tagged_company_ref && subItem.tagged_company_ref.length && subItem.tagged_company_ref.includes(fromCompanyId)) {
                                let fromCompanyIndex = subItem.tagged_company_ref.indexOf(fromCompanyId);
                                subItem.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                                meta_data.push({
                                    id: inspection.id,
                                    checklist: 'additional_checklist',
                                    checklist_item_index: j,
                                    updated_checklist_item: subItem,
                                });
                                inspectionTourIds.push(inspection.id);

                                updateRequired = true;
                            }
                        });

                        return false
                    }

                    if (item.tagged_company_ref && item.tagged_company_ref.length && item.tagged_company_ref.includes(fromCompanyId)) {
                        let fromCompanyIndex = item.tagged_company_ref.indexOf(fromCompanyId);
                        item.tagged_company_ref[fromCompanyIndex] = toCompanyId;
                        meta_data.push({
                            id: inspection.id,
                            checklist: 'additional_checklist',
                            checklist_item_index: j,
                            updated_checklist_item: item,
                        });
                        inspectionTourIds.push(inspection.id);

                        updateRequired = true;
                    }
                });

                if (updateRequired && update) {
                    sails.log.info(`Updating inspection: ${inspection.id}`);
                    let updateRequest = {
                        checklist: inspection.checklist,
                        additional_checklist: inspection.additional_checklist
                    };
                    await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(updateRequest);
                }
                updateRequired = false;
            }
        }

        sails.log.info("Records to update: ", meta_data);
        sails.log.info("Following inspection will update: ", inspectionTourIds);

        return ResponseService.successResponse(res, {inspectionTourIds, meta_data});
    },

    /*
     * Converting field equipment_photo_ref: number to equipment_photos: array
     * in project_asset_equipment table OR
     * Converting field vehicle_photo_ref: number to vehicle_photos: array
     * in project_asset_vehicle table
     * */
    seedingAssetTables: async (req, res) => {

        if(req.param('type') == 'equipment') {
            sails.log.info('Starting conversion equipment_photo_ref: number => equipment_photos: array');

            let rawResult = await sails.sendNativeQuery(`select id, equipment_photo_ref, equipment_photos from project_asset_equipment WHERE equipment_photo_ref IS NOT NULL`);
            let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
            for (let i = 0; i < rows.length; i++) {
                sails.log.info('Processing asset equipments record, ID', rows[i].id);
                let assetEquipment = rows[i];
                if (assetEquipment.equipment_photo_ref && !assetEquipment.equipment_photos.includes(assetEquipment.equipment_photo_ref)) {
                    assetEquipment.equipment_photos.push(assetEquipment.equipment_photo_ref);
                    let data = {
                        equipment_photos: assetEquipment.equipment_photos
                    };
                    sails.log.info('Updating asset equipment record with ID: ', assetEquipment.id);
                    await sails.models.projectassetequipment.updateOne({id: assetEquipment.id}).set(data);
                }
            }
            sails.log.info('`equipment_photo_ref` for all records have been converted to array type.');
            return ResponseService.successResponse(res, {message:'`equipment_photo_ref` for all records have been converted.'});
        }

        sails.log.info('Starting conversion vehicle_photo_ref: number => vehicle_photos: array');

        let rawResult = await sails.sendNativeQuery(`select id, vehicle_photo_ref, vehicle_photos from project_asset_vehicles WHERE vehicle_photo_ref IS NOT NULL`);
        let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        for (let i = 0; i < rows.length; i++) {
            sails.log.info('Processing asset vehicles record, ID', rows[i].id);
            let assetVehicle = rows[i];
            if (assetVehicle.vehicle_photo_ref && !assetVehicle.vehicle_photos.includes(assetVehicle.vehicle_photo_ref)) {
                assetVehicle.vehicle_photos.push(assetVehicle.vehicle_photo_ref);
                let data = {
                    vehicle_photos: assetVehicle.vehicle_photos
                };
                sails.log.info('Updating asset vehicle record with ID: ', assetVehicle.id);
                await sails.models.projectassetvehicles.updateOne({id: assetVehicle.id}).set(data);
            }
        }

        sails.log.info('`vehicle_photo_ref` for all records have been converted to array type.');
        return ResponseService.successResponse(res, {message:'`vehicle_photo_ref` for all records have been converted.'});

    },

    incidentReportActionCategories: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        //Note: Add new category alphabatically.
        let irActionCategories = [
            {
                key: 'A',
                value: 'A'
            },
            {
                key: 'B',
                value: 'B'
            },
            {
                key: 'C',
                value: 'C'
            },
            {
                key: 'D',
                value: 'D'
            },
            {
                key: 'E',
                value: 'E'
            }
        ];

        let truncated = await sails.models.inndexsetting.destroy({
            name: 'ir_action_categories'
        });
        sails.log.info('truncate success');

        let createRequest = {name: "ir_action_categories", value: irActionCategories};
        sails.log.info('adding ir_action_categories in inndex setting.');

        irActionCategories = await sails.models.inndexsetting.create(createRequest);

        if(irActionCategories)  {
            sails.log.info('Added ir_action_categories successfully.');
            return ResponseService.successResponse(res, irActionCategories);
        }

        sails.log.info('Failed to added ir_action_categories.');
        return ResponseService.errorResponse(res, 'Failed to added ir_action_categories.');
    },

    seedItpCustomFields: async (req, res) => {
        let itps = await sails.models.qualitychecklist_reader.find({ select: ['id', 'custom_fields'] });
        for(const itp of itps) {
            let custom_field = (itp.custom_fields || []).map(field => {
                field.field_type = 'textbox';
                field.options = [];
                return field;
            });
            sails.log.info('Updating ITP with id:custom_fields', itp.id, custom_field);
            await sails.models.qualitychecklist.updateOne({id: itp.id}).set({custom_fields: custom_field});
        }

        sails.log.info('All the existing ITP custom_fields have been updated with `field_type:text` and `option:[]`.');
        return ResponseService.successResponse(res, {message:'All the existing ITP custom_fields have been updated with `field_type:text` and `option:[]`.'});
    },

    seedingIBScoreType: async (req, res) => {
        let scoringSystem = [{
            "type": 1,
            "title": "Traffic Light System",
            "values": [
                "Good",
                "Fair",
                "Poor"
            ]
        },
        {
            "type": 2,
            "title": "Rating: 3/2/1",
            "values": [
                "3",
                "2",
                "1"
            ]
        },
        {
            "type": 3,
            "title": "Satisfactory: Yes/No",
            "values": [
                "Yes",
                "No"
            ]
        }];

        let inspectionBuilders = await sails.models.inspectionbuilder_reader.find({
            select: ['id', 'score_type', 'scoring_system']
        });

        for (let index in inspectionBuilders) {
            let inspection = inspectionBuilders[index];
            sails.log.info(`inspection score_type: `, inspection.score_type);

            let req = {
                scoring_system: scoringSystem[inspection.score_type - 1]
            };

            sails.log.info(`scoring_system for inspection: .....${inspection.id} will be `, req);

            let ibChecklist = await sails.models.inspectionbuilder.updateOne({id: inspection.id}).set(req);
        }

        sails.log.info('Updated inspections count: ', inspectionBuilders.length);
        return ResponseService.successResponse(res, {message:`Updated inspections count:  ${inspectionBuilders.length}`});
    },

    importExportItems: async (req, res) => {
        if(req.param('type') == 'import') {
            let file_path = path.join(process.cwd(), 'file-to-import.xlsx');

            let workbook = new ExcelJS.Workbook();
            let validRows = [];
            await workbook.xlsx.readFile(file_path).then(function(){
                let workSheet =  workbook.getWorksheet("inspection-tour-items");
                workSheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
                    if (rowNumber != 1) {
                        let data = (row.values || []);
                        //POSITION: Inspection Id,	Project Id,	Item Que Id, Item Question,	Item Summary, Category
                        data.shift()
                        let item = {
                            "category": data[5],
                            "question": (data[3]) ? data[3] : '',
                            "question_id": data[2],
                            "inspectionId": data[0]
                        };
                        sails.log.info(`Record to update inspection tour ${item.inspectionId} with category: ${item.category}, question: ${item.question}, question_id: ${item.question_id}`);
                        validRows.push(item);
                    }
                });
            });

            sails.log.info(validRows);
            for (let index in validRows) {
                let item = validRows[index];
                let inspectionTour =  await sails.models.projectinspectiontour_reader.findOne({
                    where: {id: item.inspectionId},
                    select: [`id`, 'user_ref', 'project_ref', 'additional_checklist']
                });
                //sails.log.info(inspectionTour);

                if(inspectionTour) {
                    (inspectionTour.additional_checklist || []).map(rItem => {
                        if(rItem.question_id == item.question_id) {
                            rItem.category = (rItem.category) ? rItem.category : item.category;
                            rItem.question = (rItem.question) ? rItem.question : item.question;
                        }
                        return rItem;
                    });

                    await sails.models.projectinspectiontour.updateOne({id: item.inspectionId}).set({additional_checklist: inspectionTour.additional_checklist});
                    sails.log.info(`Inspection tour with id ${item.inspectionId} has been updated.`);
                } else {
                    sails.log.error(`No Inspection found with Id: ${item.inspectionId}.`);
                }
            }

            return ResponseService.successResponse(res, {message:'All record has been imported.'});
        }

        let projectInspectionTour =  await sails.models.projectinspectiontour.find({
            select: ['id', 'project_ref', 'additional_checklist']
        });
        let rowsToExport = [];
        for(const inspectionTour of projectInspectionTour) {
            for (let item of inspectionTour.additional_checklist) {
                if(!item.category) {
                    let iData = {
                        "Inspection Id": inspectionTour.id,
                        "Project Id": inspectionTour.project_ref,
                        "Item Que Id": (item.question_id || ''),
                        "Item Question": (item.question || ''),
                        "Item Summary": (item.summary || ''),
                        "Category": '',
                    }
                    rowsToExport.push(iData);
                }
            }
        }

        if(rowsToExport.length) {
            let workbook = new ExcelJS.Workbook();
            let worksheet = workbook.addWorksheet('inspection-tour-items', {
                views: [{zoomScale: 80}],
                pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
            });
            let styleFilter = {alignment: {  vertical: 'middle',horizontal: 'center',  wrapText: true}};

            let columns = [
                { header: "Inspection Id", key: "Inspection Id", width:20, ...styleFilter},
                { header: "Project Id", key: "Project Id", width:15, ...styleFilter},
                { header: "Item Que Id", key: "Item Que Id", width:20, ...styleFilter},
                { header: "Item Question", key: "Item Question", width:15, ...styleFilter},
                { header: "Item Summary", key: "Item Summary", width:25, ...styleFilter},
                { header: "Category", key: "Category", width:15, ...styleFilter},
            ];
            worksheet.columns = columns;
            worksheet.addRows(rowsToExport);
            return streamExcelDownload(res, workbook);
            return ResponseService.successResponse(res, {message:'All record has been exported.'});
        }
        return ResponseService.successResponse(res, {message:'No record found to export.'});
    },

    replaceIbWording: async (req, res) => {
        let ib_id = +(req.param('id'));
        let inspection_builder = await sails.models.inspectionbuilder.findOne({
            where: {
                id: ib_id
            },
            select: ['scoring_system']
        });

        inspection_builder.scoring_system.values = ['Compliant', 'Observation/Concern', 'Immediate'];
        await sails.models.inspectionbuilder.updateOne({ id: inspection_builder.id }).set({scoring_system: inspection_builder.scoring_system});
        sails.log.info(`Inspection builder with id: ${inspection_builder.id} has been updated.`);

        let ibClReports =  await sails.models.inspectionbuilderreport.find({
            where: {ib_ref: inspection_builder.id},
            select: ['has_subheadings', 'checklist']
        });

        for (let i in ibClReports) {
            let report = ibClReports[i];
            if (report.has_subheadings) {
                sails.log.info(`Before report................${report.id}......................`, JSON.stringify(report.checklist));
                for (let j in report.checklist) {
                    let checklistItem = report.checklist[j];
                    for (let k in checklistItem['subheadings']) {
                        let finalItem = checklistItem['subheadings'][k];
                        if ((finalItem.answer || '').toLowerCase() ==  'good') {
                            finalItem.answer = 'Compliant';
                        }

                        if ((finalItem.answer || '').toLowerCase() ==  'fair') {
                            finalItem.answer = 'Observation/Concern';
                        }

                        if ((finalItem.answer || '').toLowerCase() ==  'non-compliant') {
                            finalItem.answer = 'Immediate';
                        }

                        report.checklist[j]['subheadings'][k] = finalItem;
                    }
                }
                sails.log.info(`After report.............${report.id}.........................`, JSON.stringify(report.checklist));

                await sails.models.inspectionbuilderreport.updateOne({id: report.id}).set({checklist: report.checklist});
                sails.log.info(`Inspection builder report with id: ${report.id} has been updated.`);
            }
        }

        return ResponseService.successResponse(res, {message:'All Records updated.'});
    },

    seedIncidentCauseTypAndOptions: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }

        let defaultCauseTypOptions = {
            "immediate_causes" : [
                {
                    "title": "Unsafe acts",
                    "options": [
                        "Individual behaviour/attitude",
                        "Poor Judgement",
                        "Negative attitude towards HSE / lack of belief",
                        "Improper lifting",
                        "Inattention or distraction (i.e. footing, surroundings, external sources, etc.)",
                        "Horseplay",
                        "Improper reaction to change",
                        "Victim of another person's actions",
                        "Personal goals vs. safety goals (i.e. Perceived inappropriate reward system)",
                        "Taking unsafe position (line of fire)"
                    ]
                },
                {
                    "title": "Tools or Equipment Use",
                    "options": [
                        "Operating equipment without authority or required training",
                        "Improper placement of tool or equipment",
                        "Unsafe mixing of chemicals",
                        "Disabling guards or warning systems",
                        "Using known defective tool or equipment",
                        "Loss of control of tool or equipment",
                        "Using tool or equipment improperly",
                        "Improper use or failure to use proper Personal Protective Equipment",
                        "Using improper or modified tool or equipment"
                    ]
                },
                {
                    "title": "Procedures implementation",
                    "options": [
                        "Risk Assessment not followed",
                        "Safety Standards/ Procedures/ Guidelines not followed",
                        "Conscious risk taking (by group or individual)",
                        "Improper loading of materials or equipment",
                        "Working too quickly or in a hurry"
                    ]
                },
                {
                    "title": "Unsafe conditions",
                    "options": [
                        "Workplace Hazards",
                        "Exposure to elevation change / heights",
                        "Inadequate illumination",
                        "Inadequate housekeeping (including obstacles, access)",
                        "Slippery surfaces",
                        "Lack of signage or poor marking (emergency exits, warning signs, etc.)",
                        "Abnormal biological presence",
                        "Inadequate ventilation",
                        "Inadequate/defective work surfaces"
                    ]
                },
                {
                    "title": "Process Hazards",
                    "options": [
                        "Fire and explosion hazards",
                        "Deficient hazardous substance labelling",
                        "Process system/equipment failure",
                        "Inadequate isolation of process or equipment",
                        "Unsegregated hazardous substances",
                        "Excess or uncontrolled - radiation, noise, chemicals, etc."
                    ]
                },
                {
                    "title": "Tools & Equipment Condition",
                    "options": [
                        "Defective tool or equipment (defect unknown to user)",
                        "Insufficient or lack of anchor points",
                        "Improperly prepared tool or equipment",
                        "Inadequate tool or equipment for the task",
                        "Maintenance of tool or equipment inadequate"
                    ]
                },
                {
                    "title": "Protective Defences",
                    "options": [
                        "Inadequate guards/protective devices",
                        "Defective guards/protective devices",
                        "Inadequate warning systems",
                        "Defective warning systems",
                        "Defective Personal Protective Equipment"
                    ]
                },
                {
                    "title": "Weather conditions",
                    "options": [
                        "Extreme temperatures",
                        "Abnormal wind conditions",
                        "Precipitation, fog, etc"
                    ]
                }
            ],
            "underlying_causes" : [
                {
                    "title": "People Factors",
                    "options": [
                        "Physical Capabilities",
                        "Fatigue",
                        "Substance sensitivities or allergies",
                        "Inability to use/wear PPE",
                        "Overexertion",
                        "Self medication, prescription drug use",
                        "Alcohol or drug abuse"
                    ]
                },
                {
                    "title": "Mental Capabilities",
                    "options": [
                        "Fears and phobias",
                        "Emotional disturbance / stress",
                        "Poor judgement"
                    ]
                },
                {
                    "title": "Physiological",
                    "options": [
                        "Mental fatigue due to lack of rest",
                        "Atmospheric pressure variation",
                        "Incapacitated (Blood sugar, blood pressure, vision, other personal health conditions)",
                        "Emotional overload",
                        "Extreme concentration/perception demands/mental task load or speed",
                        "Routine, monotony, demand for uneventful vigilance",
                        "Conflicting demands, frustration"
                    ]
                },
                {
                    "title": "Execution Factors",
                    "options": [
                        "Engineering / Design",
                        "Unsafe design",
                        "Engineering or design related failure",
                        "Improper materials used, specified",
                        "Poor ergonomic design"
                    ]
                },
                {
                    "title": "Execution",
                    "options": [
                        "Inadequate performance measurement, evaluation and feedback",
                        "Inadequate job placement (wrong worker assigned to the job)",
                        "Unclear or conflicting reporting relationships",
                        "Improper delegation",
                        "Inadequate accident reporting or investigation",
                        "Deficient review of Risk Assessment by supervisor",
                        "Lack of coaching or monitoring",
                        "Lack of supervisory task/job knowledge"
                    ]
                },
                {
                    "title": "Communication",
                    "options": [
                        "Unclear or incomplete instructions, to injured",
                        "Unclear or incomplete instructions between others (supervisors, contractors, clients, etc.)",
                        "Non-standard terms, phrases, etc.",
                        "Deficient handover process (between shifts, workers, etc.)",
                        "Language barriers"
                    ]
                },
                {
                    "title": "Skill & Knowledge",
                    "options": [
                        "Lack of experience by worker(s)",
                        "Infrequently performed task",
                        "Inadequate training, knowledge or skill for task"
                    ]
                },
                {
                    "title": "Tools & Equipment Provision",
                    "options": [
                        "Inadequate assessment of tools or equipment needs",
                        "Inadequate availability of tools or equipment",
                        "Inadequate maintenance of tools or equipment"
                    ]
                }
            ],
            "root_causes": [
                {
                    "title": "Management Aspects",
                    "options": [
                        "Resource Management",
                        "Inadequate evaluation of change in work scope / plans",
                        "Insufficient communication of safety expectations",
                        "Shortage of resources to perform the task safely",
                        "Lack of evaluation of hazards or mitigation options",
                        "Lack of clear roles and responsibilities",
                        "Inadequate job placement- supervisory level (wrong supervisor for the job)",
                        "Lack of reinforcement of proper behaviours or lack of disciplinary actions",
                        "Cultural issues not recognised, understood, respected, etc."
                    ]
                },
                {
                    "title": "Leadership",
                    "options": [
                        "Improper performance rewards (actions other than safety being rewarded)",
                        "Lack of or ineffective safety incentives",
                        "Avoided confronting client or others about safety",
                        "Inappropriate peer pressure",
                        "Lack of lessons learned sharing and implementing",
                        "Inadequate performance feedback",
                        "Failure to follow recommendations of HSE personnel",
                        "Failure to implement corrective actions, identified earlier",
                        "Inadequate attempt to save time, money, labour, etc. - by supervision or management"
                    ]
                },
                {
                    "title": "Contractors & Subcontractor Mgt.",
                    "options": [
                        "Inadequate safety specification / contract clauses",
                        "Inadequate receiving, inspection, acceptance of work, equip., etc."
                    ]
                },
                {
                    "title": "Accountability",
                    "options":[
                        "Inconsistent enforcement of safety rule(s)",
                        "Lack of enforcement of existing policies & procedures",
                        "Safe Work Practices training not reinforced on the job",
                        "Safety responsibilities not clearly stated",
                        "No recognized accountability system for safety responsibilities",
                        "Consequences insufficient to sustain behaviour"
                    ]
                },
                {
                    "title": "Program/System Aspects",
                    "options": [
                        "Work Standards / Procedures",
                        "Inadequate/absent regulations or procedures",
                        "Inadequate requirements for PPE",
                        "Inadequate reference documents, directives, or guidance manuals"
                    ]
                },
                {
                    "title": "Risk Evaluation",
                    "options": [
                        "Inadequate job safety/hazard analysis",
                        "Failure in Management Of Change",
                        "Risk evaluation not performed"
                    ]
                },
                {
                    "title": "Task Planning",
                    "options": [
                        "Risk Assessment not developed",
                        "Risk Assessment written but not reviewed by all involved",
                        "Risk Assessment did not involve all concerned workers",
                        "Risk Assessment did not address all task steps/activities",
                        "Risk Assessment did not address all hazards associated with the task"
                    ]
                },
                {
                    "title": "Training",
                    "options": [
                        "Lack of initial orientation",
                        "Inadequate orientation provided",
                        "Lack of required training",
                        "Inadequate training provided",
                        "Inadequate refresher / update training"
                    ]
                },
                {
                    "title": "Inspection and Audit program",
                    "options": [
                        "Failure to perform required inspections / audits",
                        "Hazard not identified on audit (but could have been)",
                        "Missing program element not identified on inspection / audit"
                    ]
                }
            ]
          };

        let causeTypOptions = (req.body.incident_report_cause_type_en_gb || defaultCauseTypOptions);

        try {
            var truncated = await sails.models.inndexsetting.destroy({
                name: { in: [ "incident_report_cause_type_en_gb"] }
            });
            sails.log.info('truncate success');

            let createRequest = {name: "incident_report_cause_type_en_gb", value: causeTypOptions};
            sails.log.info('creating incident report cause type options in inndex setting.');

            let causeTypOptionsData = await sails.models.inndexsetting.create(createRequest);

            if(causeTypOptionsData)  {
                sails.log.info('Created incident report cause type options successfully.');
                return ResponseService.successResponse(res, causeTypOptionsData);
            }

            sails.log.info('Failed to create incident report cause type options');
            return ResponseService.errorResponse(res, sails.__('Failed to create incident report cause type options'));
        } catch (failure){
            sails.log.info('Failed to create incident report cause type options', failure)
            return ResponseService.errorResponse(res, sails.__('Failed to create incident report cause type options'), failure);
        }
    },

    fixGCRecordRefs: async (req, res) => {
        let data_type = (req.param('feature') == 'observation') ? 'observation' : 'good-call';
        let records = await sails.models.goodcall_reader.find({
            select: ['project_ref', 'record_id', 'data_type'],
            where: {
                data_type: data_type, project_ref: {'!=': null}, record_id: {'!=': null },
            }
        }).sort([{id: 'ASC'}]);

        let allProjectRecords = _groupBy(records || [], (l) => l.project_ref);
        for (let index in allProjectRecords) {
            let projectRecords = allProjectRecords[index];
            sails.log.info(`Processing project ${data_type} records, group`, projectRecords);
            for (let i in projectRecords) {
                sails.log.info('Current record_id ', projectRecords[i].record_id, " new record_id ", (+i+1));
                await sails.models.goodcall.updateOne({id: projectRecords[i].id}).set({record_id: (+i+1)});
            }
        }
        sails.log.info('Records have been updated.');
        return ResponseService.successResponse(res, {message: `${records.length} records have been updated.`});
    },

    fixingIbInspectionRating: async (req, res) => {
        let ibId = +req.param('ibId');
        let goodRating = [];
        let poorRating = [];
        let goodRatingToReplace = 'Compliant';
        let poorRatingToReplace = 'Non-conformance';
       if (ibId == 83) {
            goodRating = ["yes"];
            poorRating = ["no"];
       } else {
           return ResponseService.successResponse(res, {message:`Id not matched.`});
       }

        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: {id: ibId},
            select: ['ib_title', 'has_subheadings', 'scoring_system']
        });

        let ibChecklists = await sails.models.inspectionbuilderreport_reader.find({
            where: {ib_ref: ibId},
            select: ['checklist', 'additional_checklist']
        });

        for (let i in ibChecklists) {
            let inspection = ibChecklists[i];
            if (ibInfo.has_subheadings) {
                for (let j in inspection.checklist) {
                    let checklistItem = inspection.checklist[j];
                    for (let k in checklistItem['subheadings']) {
                        let finalItem = checklistItem['subheadings'][k];
                        if (finalItem.answer && goodRating.includes(finalItem.answer)) {
                            finalItem.answer = goodRatingToReplace;
                        }

                        if (finalItem.answer && poorRating.includes(finalItem.answer)) {
                            finalItem.answer = poorRatingToReplace;
                        }

                        inspection.checklist[j]['subheadings'][k] = finalItem;
                    }
                }

                for (let j in inspection.additional_checklist) {
                    let checklistItem = inspection.additional_checklist[j];
                    for (let k in checklistItem['subheadings']) {
                        let finalItem = checklistItem['subheadings'][k];
                        if (finalItem.answer && goodRating.includes(finalItem.answer)) {
                            finalItem.answer = goodRatingToReplace;
                        }

                        if (finalItem.answer && poorRating.includes(finalItem.answer)) {
                            finalItem.answer = poorRatingToReplace;
                        }

                        inspection.additional_checklist[j]['subheadings'][k] = finalItem;
                    }
                }

                let req = {checklist: inspection.checklist, additional_checklist: inspection.additional_checklist}
                await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(req);
                sails.log.info(`Inspection builder report with id: ${inspection.id} has been updated.`);
            }
        }

        sails.log.info('Updated inspections count: ', ibChecklists.length);
        return ResponseService.successResponse(res, {message:`Updated inspections count:  ${ibChecklists.length}`});
    },

    updateIbClQuestion: async (req, res) => {
        let inspectionId = +req.param('inspectionId');

        if (inspectionId == 915) {
            //"Have any softs spots been found? client to be invited to site - record location and dimensions - photo "
            let ibChecklists = await sails.models.inspectionbuilderreport_reader.find({
                where: {id: inspectionId},
                select: ['checklist']
            });

            for (let i in ibChecklists) {
                let inspection = ibChecklists[i];
                for (let j in inspection.checklist) {
                    let checklistItem = inspection.checklist[j];

                    if (checklistItem.question == "Have any softs spots been found? client to be invited to site - record location and dimensions - photo ") {
                        checklistItem.question = 'Is the area free from any soft spots? client to be invited to site - record location and dimensions - photo';
                        checklistItem.answer = 'yes';
                    }

                    inspection.checklist[j] = checklistItem;
                }
                let req = {checklist: inspection.checklist}
                await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(req);
                sails.log.info(`Inspection builder report with id: ${inspection.id} has been updated.`);
            }
        }

        if ([916, 902, 833, 619, 317].includes(inspectionId)) {
            let ibChecklists = await sails.models.inspectionbuilderreport_reader.find({
                where: {id: inspectionId},
                select: ['checklist']
            });

            for (let i in ibChecklists) {
                let inspection = ibChecklists[i];
                for (let j in inspection.checklist) {
                    let checklistItem = inspection.checklist[j];

                    if (checklistItem.question == "31. Is there any visible movement of the ground surrounding the excavation?") {
                        checklistItem.question = '31. Is the ground surrounding the excavation free from any visible signs of movement?';
                        checklistItem.answer = (checklistItem.answer == 'no') ? 'yes' : checklistItem.answer;
                    }

                    inspection.checklist[j] = checklistItem;
                }
                let req = {checklist: inspection.checklist}
                await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(req);
                sails.log.info(`Inspection builder report with id: ${inspection.id} has been updated.`);
            }
        }

        sails.log.info('Updated inspections count: ');
        return ResponseService.successResponse(res, {});
    },

    assetFaultCorrection: async (req, res) => {
        let assetType = +req.param('assetType');

        let assetInspections = [];
        if(assetType == 'vehicle') {
            assetInspections =  await sails.models.assetvehicleinspection_reader.find({
                select: ['id', 'fault_details', 'createdAt']
            });
        } else {
            assetInspections =  await sails.models.assetequipmentinspection_reader.find({
                select: ['id', 'fault_details', 'createdAt']
            });
        }

        let recordCount = 0;
        for (let i in assetInspections) {
            let inspection = assetInspections[i];
            if (inspection.fault_details && inspection.fault_details.length) {
                inspection.fault_details = (inspection.fault_details).reduce((arr, fault, index) => {
                    fault.fault_id = index + 1;
                    fault.status = (!fault.status || !fault.closedout_at) ? 'open' : fault.status;
                    fault.date_reported = (fault.date_reported) ? fault.date_reported : inspection.createdAt;
                    fault.reported_by = (fault.reported_to) ? fault.reported_to : (fault.reported_by) ? fault.reported_by : "N/A";
                    delete fault.reported_to;

                    arr.push(fault);

                    return arr;
                }, []);

                let req = {
                    "fault_details": inspection.fault_details
                }
                if(assetType == 'vehicle') {
                    await sails.models.assetvehicleinspection.updateOne({id: inspection.id}).set(req);
                } else {
                    await sails.models.assetequipmentinspection.updateOne({id: inspection.id}).set(req);
                }
                recordCount += 1;
            }
        }

        sails.log.info(`Total ${recordCount} inspection's faults have been updated.`);
        return ResponseService.successResponse(res, {});
    },

    associateFaultIdToAssetInspectionFaults: async (req, res) => {
        let assetType = +req.param('assetType');

        let assets = [];
        if (assetType == 'vehicle') {
            assets = await sails.models.projectassetvehicles.find({
                select: ['id']
            }).sort([
                { id: 'ASC' }
            ]);
        } else {
            assets = await sails.models.projectassetequipment.find({
                select: ['id']
            }).sort([
                { id: 'ASC' }
            ]);
        }

        let updatedRecordsCount = 0;
        for (let i in assets) {
            let asset = assets[i];

            let rawResult = [];
            if (assetType == 'vehicle') {
                rawResult = await sails.sendNativeQuery(`SELECT id,
                                                                    fault_details,
                                                                    "createdAt"
                                                             FROM asset_vehicle_inspection
                                                             WHERE vehicle_ref = $1
                                                             ORDER BY id DESC`, [asset.id]);
            } else {
                rawResult = await sails.sendNativeQuery(`SELECT id,
                                                                    fault_details,
                                                                    "createdAt"
                                                             FROM asset_equipment_inspection
                                                             WHERE equipment_ref = $1
                                                             ORDER BY id DESC`, [asset.id]);
            }
            let previousInspections = [];
            if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
                previousInspections = rawResult.rows;
            }

            let dates = [];
            //pick only last inspection of the date(Filter Duplicate)
            previousInspections = previousInspections.reduce((inspections, vi) => {
                if (!dates.includes(moment(+vi.createdAt).format('DD-MM-YYYY'))) {
                    dates.push(moment(+vi.createdAt).format('DD-MM-YYYY'));
                    inspections.push(vi);
                }
                return inspections;
            }, []);

            previousInspections = (previousInspections || []).sort((a,b) => (a.id > b.id) ? 1 : ((b.id > a.id) ? -1 : 0));
            if (previousInspections.length) {
                let lastFaultId = 0;
                for (let j in previousInspections) {
                    let inspection = previousInspections[j];
                    inspection.fault_details = (inspection.fault_details || []).map(fault => {
                        lastFaultId += 1;
                        fault.fault_id = lastFaultId;
                        return fault;
                    });

                    if (assetType == 'vehicle') {
                        await sails.models.assetvehicleinspection.updateOne({id: inspection.id}).set({
                            'fault_details': inspection.fault_details
                        });
                    } else {
                        await sails.models.assetequipmentinspection.updateOne({id: inspection.id}).set({
                            'fault_details': inspection.fault_details
                        });
                    }
                    updatedRecordsCount += 1;
                }
            }
        }

        sails.log.info(`Total ${updatedRecordsCount} inspection's faults have been updated.`);
        return ResponseService.successResponse(res, {});
    },

    ibReportRatingPercentage: async (req, res) => {
        let ibReports = await sails.models.inspectionbuilderreport_reader.find({
            select: ['checklist', 'additional_checklist', 'ib_ref']
        });

        sails.log.info(`Found ${ibReports.length} inspection reports to update.`);

        let ibRefs = ibReports.reduce((arr, item) => {
            arr.push(item.ib_ref);
            return arr;
        }, []);

        let ibChecklists = await sails.models.inspectionbuilder_reader.find({
            where: {
                id: _uniq(ibRefs)
            },
            select: ['scoring_system', 'has_subheadings']
        });

        for(let ibReport of ibReports) {
            sails.log.info(`Preparing rating for report ${ibReport.id} of ib ${ibReport.ib_ref}`);

            let ib = (ibChecklists || []).find(ib => ib.id == ibReport.ib_ref);
            let ratings = ib.scoring_system.values;
            let goodRatingLabel = '';
            let poorRatingLabel = '';
            let fairRatingLabel = '';
            if ([1,2].includes(ib.scoring_system.type)) {
                goodRatingLabel = ratings[0];
                fairRatingLabel = ratings[1];
                poorRatingLabel = ratings[2];
            } else if (ib.scoring_system.type == 3) {
                goodRatingLabel = ratings[0];
                poorRatingLabel = ratings[1];
            }
            let { hasRatingPoint, ratingPointAgainstRating } = getRatingPointInfo(ib.scoring_system);

            let inspectionChecklist = [...ibReport.checklist, ...ibReport.additional_checklist];

            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            if (ib.has_subheadings) {
                inspectionChecklist.forEach(function (heading, i) {
                    if (heading.subheadings) {
                        heading.subheadings.forEach(function (item, j) {
                            let answer = (item.answer || '').toLowerCase();
                            goodRatingItemsCount += (answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                            poorRatingItemsCount += (answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                            fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
                        });
                    }
                });
            } else {
                inspectionChecklist.forEach(function (item, i) {
                    let answer = (item.answer || '').toLowerCase();
                    goodRatingItemsCount += (answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
                });
            }
            let ratingPointsFraction = 0;
            if (hasRatingPoint) {
                let maximumNumberOfPoints = ratingPointAgainstRating[goodRatingLabel.toLowerCase()] * (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
                let totalNumberOfPoints = 0;
                totalNumberOfPoints += (ratingPointAgainstRating[goodRatingLabel.toLowerCase()] * goodRatingItemsCount) + (ratingPointAgainstRating[poorRatingLabel.toLowerCase()] * poorRatingItemsCount);
                totalNumberOfPoints += (fairRatingLabel && ratingPointAgainstRating[fairRatingLabel.toLowerCase()]) ? (ratingPointAgainstRating[fairRatingLabel.toLowerCase()] * fairRatingItemsCount) : 0;
                ratingPointsFraction = (100 * totalNumberOfPoints)/maximumNumberOfPoints;
                ratingPointsFraction = (isNaN(ratingPointsFraction) || Math.sign(ratingPointsFraction) == -1) ? 0 : ratingPointsFraction;
            }

            let ratingPercent = parseInt(100/(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount)*(goodRatingItemsCount));
            ratingPercent = (isNaN(ratingPercent) || Math.sign(ratingPercent) == -1) ? 0 : ratingPercent;
            await sails.models.inspectionbuilderreport.updateOne({id: ibReport.id}).set({'rating_percentage': (hasRatingPoint) ? Math.round(ratingPointsFraction) : ratingPercent});
        }

        sails.log.info(`All inspection report have been updated.`);
        return ResponseService.successResponse(res, {});
    },

    inspectionTourRatingPercentage: async (req, res) => {
        let projectInspectionTour =  await sails.models.projectinspectiontour_reader.find({
            select: ['common_checklist', 'rail_checklist', 'additional_checklist', 'industrial_checklist', 'project_ref']
        });

        sails.log.info(`Found ${projectInspectionTour.length} inspection tours to update.`);
        let projectIds = projectInspectionTour.reduce((arr, item) => {
            arr.push(item.project_ref);
            return arr;
        }, []);
        projectIds = projectIds.filter(id => id);
        sails.log.info(`Fetching projects info.`, projectIds);

        let projects = await sails.models.project_reader.find({
            where: {id: _uniq(projectIds)},
            select: ['project_type']
        });

        for(let inspectionTour of projectInspectionTour) {
            if(inspectionTour.project_ref) {
                sails.log.info(`Preparing rating for inspection tour ${inspectionTour.id}`);
                let projectInfo = (projects || []).find(project => project.id == inspectionTour.project_ref);
                let isIndustrialProject = (projectInfo.project_type === 'industrial');
                //rating labels
                let goodRatingLabel = `yes`;
                let fairRatingLabel = ``;
                let poorRatingLabel = `no`;
                let inspectionTourChecklist = [];
                if (isIndustrialProject) {
                    goodRatingLabel = `good`;
                    fairRatingLabel = `fair`;
                    poorRatingLabel = `poor`;
                    inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                } else {
                    inspectionTourChecklist = [...inspectionTour.common_checklist];
                    if (projectInfo.project_type === 'rail') {
                        inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                    }
                }
                let goodRatingItemsCount = 0;
                let poorRatingItemsCount = 0;
                let fairRatingItemsCount = 0;
                inspectionTourChecklist.forEach(function(item, j) {
                    let answer = (item.answer || '').toLowerCase();
                    goodRatingItemsCount += (answer === goodRatingLabel) ? 1 : 0;
                    poorRatingItemsCount += (answer === poorRatingLabel) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel) ? 1 : 0;
                });

                let ratingPercent = parseInt(100/(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount)*(goodRatingItemsCount));
                ratingPercent = (isNaN(ratingPercent) || Math.sign(ratingPercent) == -1) ? 0 : ratingPercent;
                await sails.models.projectinspectiontour.updateOne({id: inspectionTour.id}).set({'rating_percentage':ratingPercent});
            }
        }

        sails.log.info(`All inspection tour have been updated.`);
        return ResponseService.successResponse(res, {});
    },

    /*
     Converting field tagged_company_ref: number to tagged_company_ref: array
     for all the Inspection Tour records.
     */
    seedMultiTaggedCompanies: async (req, res) => {
        sails.log.info('Starting conversion tagged_company_ref: number => tagged_company_ref: array');

        let inspectionReports = await sails.models.inspectionbuilderreport.find({
            select: ['checklist', 'additional_checklist', 'has_subheadings']
        });

        for (let inspection of inspectionReports) {
            sails.log.info('Processing Inspection report, ID', inspection.id);

            let data = {
                checklist: updateChecklistItems(inspection.checklist, inspection.has_subheadings),
                additional_checklist: updateChecklistItems(inspection.additional_checklist, inspection.has_subheadings),
            };

            await sails.models.inspectionbuilderreport.updateOne({id: inspection.id}).set(data);
        }
        sails.log.info('`tagged_company_ref` for all records have been converted to array type.');
        return ResponseService.successResponse(res, {message:'`tagged_company_ref` for all records have been converted.'});
    },

    clerkOfWorksCategory: async (req, res) => {
        if (!isValidRequest(req)) {
            return ResponseService.errorResponse(res, 'Validation failed');
        }
        //Note: Add new category alphabatically.
        let cowCategories = [
            {
                key: 'Benchmark',
                value: 'Benchmark'
            },
            {
                key: 'Design',
                value: 'Design'
            },
            {
                key: 'Document',
                value: 'Document'
            },
            {
                key: 'Environmental',
                value: 'Environmental'
            },
            {
                key: 'Health, Safety and Welfare',
                value: 'Health, Safety and Welfare'
            },
            {
                key: 'Material',
                value: 'Material'
            },
            {
                key: 'Other',
                value: 'Other'
            },
            {
                key: 'Process',
                value: 'Process'
            },
            {
                key: 'Quality',
                value: 'Quality'
            },
            {
                key: 'Temporary Work',
                value: 'Temporary Work'
            },
            {
                key: 'Workmanship',
                value: 'Workmanship'
            }
        ];

        let truncated = await sails.models.inndexsetting.destroy({
            name: 'cow_categories'
        });
        sails.log.info('truncate success');

        let createRequest = {name: "cow_categories", value: cowCategories};
        sails.log.info('adding cow_categories in inndex setting.');

        cowCategories = await sails.models.inndexsetting.create(createRequest);

        if(cowCategories)  {
            sails.log.info('Added cow_categories successfully.');
            return ResponseService.successResponse(res, cowCategories);
        }

        sails.log.info('Failed to added cow_categories.');
        return ResponseService.errorResponse(res, 'Failed to added cow_categories.');
    },

    /*
     Converting project supply_chain_companies data from name:string to ID:number.
     */
    convertSupplyChainCompanyNamesToIds: async (req, res) => {
        sails.log.info('Starting conversion supply_chain_companies: names => supply_chain_companies: IDs...');
        let rawQuery = `SELECT id, name, custom_field->>'supply_chain_companies' as scc, custom_field->>'country_code' as country_code FROM project WHERE custom_field->>'supply_chain_companies' IS NOT NULL;`;
        let rawResult = await sails.sendNativeQuery(rawQuery);
        let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        sails.log.info('Total projects with induction booking feature enabled:', rows.length);

        for (let i = 0; i < rows.length; i++) {
            let projectId = rows[i].id;
            let project = await sails.models.project_reader.findOne({
                where: {id: projectId},
                select: [`id`, 'custom_field']
            });
            sails.log.info('Processing Project, ID:custom_field', projectId, rows[i].scc);


            let companyRecords = await sails.models.createemployer_reader.find({
                where: {name: { in: JSON.parse(rows[i].scc) }, country_code: rows[i].country_code },
                select: [`id`]
            });
            if(companyRecords.length) {
                let companyIds = (companyRecords || []).map(c => +c.id);
                let custom_field = {
                    ...project.custom_field,
                };
                custom_field['supply_chain_companies'] = companyIds;

                sails.log.info('project updating with id:data', projectId, custom_field);
                await sails.models.project.updateOne({id: projectId}).set({custom_field: custom_field});
            }
        }
        sails.log.info('Records have been updated.');
        return ResponseService.successResponse(res, {message:`${rows.length} projects have been updated.`});
    },

    inductionQueIdsCorrection: async (req, res) => {
        let allQuestions = await sails.models.inductionquestions_reader.find({where: {data_type: 'quiz'}, select:['id', 'induction_questions', 'induction_answers', 'project_ref']});
        let update = [];
        for (let i in allQuestions) {
            let question = allQuestions[i];
            if (question.induction_questions && question.induction_questions.length) {
                let record = {project_ref: question.project_ref, updates: [], failure: []};

                (question.induction_questions || []).map((q, i) => {
                    let answer = question.induction_answers[i];

                    let id = getUniqueId();
                    if(answer && q.id == answer.question_id) {
                        record.updates.push({index: i, question: {...q}, answer: {...answer}, id});
                        q.id = id;
                        question.induction_answers[i].question_id = id;
                    } else if (!answer) {
                        record.updates.push({index: i, question: {...q}, id});
                        q.id = id;
                    } else {
                        record.failure.push({index: i, question: {...q}, answer: {...answer}});
                    }
                    return q;
                });

                let req = {
                    "induction_questions": question.induction_questions,
                    "induction_answers": question.induction_answers
                }
                await sails.models.inductionquestions.updateOne({id: question.id}).set(req);
                update.push(record);
            }
        }
        sails.log.info(`Updated IDs of all induction question's for all projects.`);
        return ResponseService.successResponse(res, {update});
    },

    transferObservationToCloseCall: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');

        if (userId != req.user.id) {
            sails.log.info("you are not authorize to perform the action.");
        }

        let observations = await sails.models.goodcall_reader.find({
            where: {
                data_type: 'observation',
                project_ref: projectId
            }
        });

        let transferredObservations = [];
        for(let i= 0, len = observations.length; i < len; i++) {
            let observation = observations[i];
            let closeCallPayload = {
                createdAt: observation.createdAt,
                updatedAt: observation.updatedAt,
                //cc_number: observation.record_id,
                cc_detail: observation.details,
                location: observation.location,
                corrective_detail: (observation.closeout_detail.description) ? observation.closeout_detail.description : '',
                cc_images: observation.images,
                corrective_images: (observation.closeout_detail.images) ? observation.closeout_detail.images : [],
                hazard_category: observation.hazard_category,
                status: observation.status,
                closed_out_by: (observation.closeout_detail.closeout_by) ? observation.closeout_detail.closeout_by : null,
                closed_out_date: (observation.closeout_detail.closeout_at) ? observation.closeout_detail.closeout_at : null,
                is_anonymous: observation.is_anonymous,
                additional_detail: observation.further_actions,
                project_ref: observation.project_ref,
                user_ref: observation.user_ref,
                user_revision_ref: observation.user_revision_ref,
                company_ref: observation.company_ref,
                tagged_owner: observation.tagged_owner,
                assigned_to: observation.assigned_to
            };

            await sails.models.closecall.create(closeCallPayload);
            transferredObservations.push(observation.id);
        }

        sails.log.info(`Moved all observations ${transferredObservations} to close call for project ${projectId} by user ${userId}.`);
        return ResponseService.successResponse(res, {});
    },

    assetInspectionRecordCorrection: async (req, res) => {
        let asset = req.body.asset;
        let asset_ids = req.body.asset_ids;
        let asset_type = req.body.asset_type;
        let update_confirm = req.body.update_confirm || false;
        let fromDay = 1683244838000;
        let toDay = 1684574282000;

        if (asset === 'vehicle') {
            let metaInspectionChecklist = await sails.models.inndexsetting_reader.findOne({ name: "vehicle_asset_inspection_checklist" });
            let metaVehicleSpecificChecklist = metaInspectionChecklist.value[asset_type+'_checklist'];
            let vehicleInspections =  await sails.models.assetvehicleinspection_reader.find({
                where: {
                    createdAt: {'>=': fromDay, '<=': toDay},
                    vehicle_ref: asset_ids
                },
                select: ['id', 'vehicle_ref', 'general_checklist', 'vehicle_specific_checklist']
            });
            sails.log.info(`Found ${vehicleInspections.length} inspections for vehicle(${asset_type}) id ${asset_ids}`);

            if (!update_confirm) {
                return ResponseService.successResponse(res, {vehicleInspections, 'message': `Found ${vehicleInspections.length} inspections for vehicle(${asset_type}) id ${asset_ids}`});
            }

            for(let i= 0, len = vehicleInspections.length; i < len; i++) {
                let inspection = vehicleInspections[i];
                let inspection_checklist = {
                    ...inspection.general_checklist,
                    ...inspection.vehicle_specific_checklist
                };

                let general_checklist = {};
                if (!['van','hoist'].includes(asset_type)) {
                    let metaGeneralChecklist = metaInspectionChecklist.value.general_checklist;
                    for (let key in metaGeneralChecklist) {
                        general_checklist[key] = (inspection_checklist[key]) ? inspection_checklist[key] : true;
                    }
                }

                let vehicle_specific_checklist = {};
                for (let key in metaVehicleSpecificChecklist) {
                    vehicle_specific_checklist[key] = (inspection_checklist[key]) ? inspection_checklist[key] : true;
                }

                let req = {
                    "general_checklist": general_checklist,
                    "vehicle_specific_checklist": vehicle_specific_checklist,
                }

                await sails.models.assetvehicleinspection.updateOne({id: inspection.id}).set(req);
                sails.log.info(`Updated inspection ${inspection.id} for vehicle(${asset_type}) id ${asset_ids} successfully`);
            }
            sails.log.info(`Correct details of all ${asset} inspections.`);
            return ResponseService.successResponse(res, {'message': `Updated ${vehicleInspections.length} inspections for vehicle(${asset_type}) id ${asset_ids}`});
        }

        if (asset === 'equipment') {
            let metaInspectionChecklist = await sails.models.inndexsetting_reader.findOne({ name: "equipment_asset_inspection_checklist" });
            let metaEquipmentSpecificChecklist = metaInspectionChecklist.value[asset_type+'_checklist'];

            let equipmentInspections = await sails.models.assetequipmentinspection_reader.find({
                where: {
                    createdAt: {'>=': fromDay, '<=': toDay},
                    equipment_ref: asset_ids
                },
                select: ['id', 'equipment_ref', 'general_checklist', 'equipment_specific_checklist']
            });

            sails.log.info(`Found ${equipmentInspections.length} inspections for equipment(${asset_type}) id ${asset_ids}`);
            if (!update_confirm) {
                return ResponseService.successResponse(res, {equipmentInspections, 'message': `Found ${equipmentInspections.length} inspections for equipment(${asset_type}) id ${asset_ids}`});
            }

            for(let i= 0, len = equipmentInspections.length; i < len; i++) {
                let inspection = equipmentInspections[i];
                let inspection_checklist = {
                    ...inspection.general_checklist,
                    ...inspection.equipment_specific_checklist
                };
                let general_checklist = {};
                let metaGeneralChecklist = metaInspectionChecklist.value.general_checklist;
                for (let key in metaGeneralChecklist) {
                    general_checklist[key] = (inspection_checklist[key]) ? inspection_checklist[key] : true;
                }

                let equipment_specific_checklist = {};
                for (let key in metaEquipmentSpecificChecklist) {
                    equipment_specific_checklist[key] = (inspection_checklist[key]) ? inspection_checklist[key] : true;
                }

                let req = {
                    "general_checklist": general_checklist,
                    "equipment_specific_checklist": equipment_specific_checklist,
                }

                await sails.models.assetequipmentinspection.updateOne({id: inspection.id}).set(req);
                sails.log.info(`Updated inspection ${inspection.id} for equipment(${asset_type}) id ${asset_ids} successfully`);
            }

            sails.log.info(`Correct details of all ${asset} inspections.`);
            return ResponseService.successResponse(res, {'message': `Updated ${equipmentInspections.length} inspections for equipment(${asset_type}) id ${asset_ids}`});
        }

        sails.log.info(`Correct details of all ${asset} inspections.`);
        return ResponseService.successResponse(res, {});
    },

    saveOldBriefingsToNewTables: async (req, res) => {
        return ResponseService.successResponse(res, {
            message: 'The `register` column has been removed from all the briefing tool tables.'
        });

        // let toolKey = req.param('toolKey', null);
        // let filterRequest = _.pick((req.body || {}), [
        //     'IDs',
        //     'countOnly'
        // ]);
        // let fetchIDs = _uniq(filterRequest.IDs);
        // let countOnly = filterRequest.countOnly;

        // if(!Object.keys(briefingToolTables || {}).includes(toolKey)) {
        //     sails.log.info(`Invalid toolKey ${toolKey} supplied.`);
        //     return ResponseService.errorResponse(res, `Invalid toolKey ${toolKey} supplied.`);
        // }

        // if(!fetchIDs.length || !typeOf(fetchIDs, 'array')) {
        //     sails.log.info(`Invalid IDs supplied, IDs can not be blank.`);
        //     return ResponseService.errorResponse(res, `Invalid IDs supplied, IDs can not be blank.`);
        // }

        // let rawQuery = `SELECT id, project_ref, register FROM ${briefingToolTables[toolKey]}
        //     WHERE id IN(${fetchIDs.join(',')}) and "register"::text != '[]';`;
        // let rawResult = await sails.sendNativeQuery(rawQuery);
        // let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];

        // let allrecords = [];
        // let rowslen = rows.length;
        // let briefingCount = 0;
        // for (let i = 0; i < rowslen; i++) {
        //     let row = rows[i];
        //     if(countOnly) {
        //         briefingCount += row.register.length;
        //         continue;
        //     }
        //     row.register.forEach(reg => {
        //         let breifing, signatures = [], registerReq = [], guestRegReq = [];
        //         let projRef = (reg.project_ref) ? reg.project_ref : (row.project_ref ? row.project_ref : 0);
        //         breifing = {
        //             "tool_key": toolKey,
        //             "briefed_at": reg.briefed_at,
        //             "briefed_by": reg.briefed_by_user_ref || null,
        //             "briefed_by_name": reg.briefed_by,
        //             "project_ref": projRef || null,
        //             "tool_record_ref": row.id,
        //         };
        //         (reg.register || []).forEach(r => {
        //             if(typeof r === 'number') {
        //                 registerReq.push({"user_ref": r});
        //                 let s = (reg.signature || []).find(s => s.userId == r);
        //                 if(s && s.sign) {
        //                     signatures.push({ "sign": s.sign, "briefing_ref": null, "user_ref": r });
        //                 }
        //             }
        //         });
        //         (reg.guest_register || []).forEach(gr => {
        //             if(gr.name && gr.sign) {
        //                 let id = Math.floor(1000000000000 + Math.random() * 9000000000000);
        //                 guestRegReq.push({
        //                     "id": id,
        //                     "name": gr.name,
        //                     "employer": gr.employer,
        //                     "job_role": gr.jobRole
        //                 });
        //                 signatures.push({ "sign": gr.sign, "briefing_ref": null, "guest_reg_id": id });
        //             }
        //         });
        //         breifing.register = registerReq;
        //         breifing.guest_register = guestRegReq;
        //         allrecords.push({
        //             breifing,
        //             signatures
        //         });
        //     });
        // }

        // if(!countOnly) {
        //     for (let j = 0, len = allrecords.length; j < len; j++) {
        //         let rec = allrecords[j];
        //         let briefing = await sails.models.toolbriefings.create(rec.breifing);

        //         let signatures = (rec.signatures || []).map(sign => {
        //             sign.briefing_ref = briefing.id;
        //             return sign;
        //         });

        //         await sails.models.usersignature.createEach(signatures);
        //     }

        // }

        // return ResponseService.successResponse(res, {
        //     allrecords,
        //     filterRequest,
        //     recordsCount: rowslen,
        //     briefingCount,
        //     message: 'Success'
        // });
    },

    saveOldTake5sBriefingsToNewTables: async (req, res) => {
        let toolKey = req.param('toolKey', null);
        let filterRequest = _.pick((req.body || {}), [
            'IDs',
            'countOnly'
        ]);
        let fetchIDs = _uniq(filterRequest.IDs);
        let countOnly = filterRequest.countOnly;

        if(!Object.keys(briefingToolTables || {}).includes(toolKey)) {
            sails.log.info(`Invalid toolKey ${toolKey} supplied.`);
            return ResponseService.errorResponse(res, `Invalid toolKey ${toolKey} supplied.`);
        }

        if(!fetchIDs.length || !typeOf(fetchIDs, 'array')) {
            sails.log.info(`Invalid IDs supplied, IDs can not be blank.`);
            return ResponseService.errorResponse(res, `Invalid IDs supplied, IDs can not be blank.`);
        }

        let rawQuery = `SELECT id, project_ref, register, guest_register FROM ${briefingToolTables[toolKey]}
            WHERE id IN(${fetchIDs.join(',')}) and ("register"::text != '[]' OR "guest_register"::text != '[]');`;
        let rawResult = await sails.sendNativeQuery(rawQuery);
        sails.log.info(`Length of the data`, rawResult.rows.length);
        let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];

        let allRecords = [];
        let rowsLen = rows.length;
        let briefingCount = 0;
        for (let i = 0; i < rowsLen; i++) {
            let row = rows[i];
            if(countOnly) {
                briefingCount += row.register.length;
                continue;
            }
            let signatures = [];
            let briefing;
            let projRef = row.project_ref ? row.project_ref : null;
            briefing = {
                "tool_key": toolKey,
                "briefed_at": null,
                "briefed_by": null,
                "briefed_by_name": null,
                "project_ref": projRef,
                "tool_record_ref": row.id,
            };

            let registerReq = [];
            (row.register || []).forEach(reg => {
                registerReq.push({"user_ref": reg});
            });

            let guestRegReq = [];
            (row.guest_register || []).forEach(gr => {
                if(gr.name && gr.sign) {
                    let id = Math.floor(1000000000000 + Math.random() * 9000000000000);
                    guestRegReq.push({
                        "id": id,
                        "name": gr.name,
                        "employer": gr.emp,
                        "job_role": gr.job_role || gr.jobRole
                    });
                    signatures.push({ "sign": gr.sign, "briefing_ref": null, "guest_reg_id": id });
                }
            });

            briefing.register = registerReq;
            briefing.guest_register = guestRegReq;
            allRecords.push({
                briefing,
                signatures
            });
        }

        if(!countOnly) {
            for (let j = 0, len = allRecords.length; j < len; j++) {
                let rec = allRecords[j];
                let briefing = await sails.models.toolbriefings.create(rec.briefing);

                let signatures = (rec.signatures || []).map(sign => {
                    sign.briefing_ref = briefing.id;
                    return sign;
                });

                let signData = await sails.models.usersignature.createEach(signatures);
                sails.log.info(signData);
            }
        }

        return ResponseService.successResponse(res, {
            allRecords,
            filterRequest,
            recordsCount: rowsLen,
            briefingCount,
            message: 'Success'
        });
    },

    seedWeatherLogsForDailyActivities: async (req, res) => {
        let allActivities = await sails.models.projectdailyactivities_reader.find({project_ref:  {'!=': null} });
        allActivities = await populateProjectRefs(allActivities, 'project_ref', ['id', 'weather_location_key', 'postcode']);
        for(let i=0; i<allActivities.length; i++) {
            let dailyactivity = allActivities[i];
            let weatherLogs = await getHourlyWeatherLogs(
                dailyactivity.project_ref.weather_location_key,
                dailyactivity.project_ref.postcode,
                dailyactivity.shift_date,
                dailyactivity.shift_time
            );
            let weather_log_ids = weatherLogs.map(w=> w.id);
            await sails.models.projectdailyactivities.update({id: dailyactivity.id}).set({weather_logs: weather_log_ids})
        }
        sails.log.info(`Updated details of weather log for all ${allActivities.length} activities.`);
        return ResponseService.successResponse(res, {message:`Updated details of weather log for all ${allActivities.length} activities.`});
    },
    saveInductionRequestCoordinate: async (req, res) => {
        let failedInductions = [];
        const projectId = req.param("projectId");
        try {
            sails.log.info(`starting seeding for project - ${projectId} `)

            const sleepTimeout = req.param("sleep") ? +req.param("sleep") : 300;
            if (!projectId) {
                return ResponseService.errorResponse(res, "ProjectId should be assed in params failed");
            }
            const project = await sails.models.project_reader.findOne({ where: { id: projectId }, select: ["custom_field", "postcode"] });
            if (!project) {
                return ResponseService.errorResponse(res, "no project found");
            }
            let location;
            location = project.custom_field.location && project.custom_field.location;
            if (!location && !project.postcode) {
                return ResponseService.errorResponse(res, "NO postcode available for this project");
            }
            if (!location) {
                let project_coordinate = await getCoordinates(project.postcode);
                location = project_coordinate && { long: project_coordinate[0], lat: project_coordinate[1] };
            }
            if (!location) {
                return ResponseService.errorResponse(res, "Invalid postcode of the project - " + projectId);
            }

            const induction_requests = await sails.models.inductionrequest_reader.find({
                where: {
                    project_ref: projectId,
                    status_code: { in: [2, 6] },
                },
                select: ["additional_data", "travel_time"],
            });
            let cachedData = {};
            let updated = 0;
            for (let i = 0; i < induction_requests.length; i++) {
                let { post_code } = induction_requests[i].additional_data.contact_detail;
                let coordinate;
                const country_code = induction_requests[i].additional_data.user_info.country_code || DEFAULT_COUNTY_CODE_GB;
                coordinate = cachedData[post_code]
                    ? cachedData[post_code].coordinate
                    : await getCoordinates(post_code, country_code && country_code.trim());

                    let {travel_time,failedInductions:updatedFailedInductions,distance} = await updateTravelTime(induction_requests[i],coordinate,failedInductions,location,cachedData,post_code);
                    if(coordinate && distance){
                        if (!cachedData[post_code]) {
                            cachedData[post_code] = { coordinate, distance };
                        }
                    }else{
                        failedInductions=[...updatedFailedInductions]
                    }

                    try {
                        await sails.models.inductionrequest.updateOne({ id: induction_requests[i].id }).set({
                            travel_time: travel_time,
                        });
                        updated++;
                    } catch (e) {
                        sails.log.error(e);
                    }

                await sleep(sleepTimeout);
            }
            sails.log.info(`seeding for project - ${projectId} completed successfully.`);
            return ResponseService.successResponse(res, { updated, total: induction_requests.length, failedInductions });
        } catch (e) {
            sails.log.error(`seeding failed for project Id - ${projectId} due to ${e.message}`);
            return ResponseService.errorResponse(res, e);
        }
    },
    seedCountriesAndNationalityMetaData: async (req, res) => {
        await sails.models.inndexsetting.destroy({
            name: 'countries_meta'
        });
        await sails.models.inndexsetting.destroy({
            name: 'nationalities_meta'
        });
        await sails.models.inndexsetting.createEach([
            { name: 'countries_meta', value: countries },
            { name: 'nationalities_meta', value: nationalities }
        ]);
        return ResponseService.successResponse(res, { message: `Countries & Nationalities data has been added` });
    },
    importDistricts: async (req, res) => {
        let file_path = path.join(process.cwd(), 'Borough-Area-Codes.xlsx');
        sails.log.info(file_path);
        let workbook = new ExcelJS.Workbook();
        let validRows = [];
        await workbook.xlsx.readFile(file_path).then(function(){
            let workSheet =  workbook.getWorksheet("Sheet1");
            workSheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
                if (rowNumber != 1) {
                    let data = (row.values || []);
                    //POSITION: District, Area Codes
                    data.shift();
                    if (data[0]) {
                        let item = {
                            "district_name": data[0],
                            "area_codes": (data[1]).split(",").map(function(item) {
                                return item.trim();
                            })
                        };
                        validRows.push(item)
                    }
                }
            });
        });

        await sails.models.metadistrict.destroy({});
        sails.log.info('truncate meta_district table.');

        sails.log.info('Adding data in meta_district table.');
        let districts = await sails.models.metadistrict.createEach(validRows);

        sails.log.info(`Importing districts successfully.`);
        return ResponseService.successResponse(res, { districts });
    },

    seedDistrictIntoInductions: async (req, res) => {
        let limit = +req.param('limit');
        let meta_districts = await sails.models.metadistrict_reader.find({
            select: ['district_name', 'area_codes']
        });
        let rawResult = await sails.sendNativeQuery(`select id, additional_data->'contact_detail'->>'post_code' post_code
                                                     from induction_request
                                                     where additional_data->'contact_detail'->>'country' = 'United Kingdom'
                                                       AND district IS NULL LIMIT ${limit}`);
        let rows = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        sails.log.info('Total projects with induction booking feature enabled:', rows.length);
        let count = 0;
        for (let i = 0; i < rows.length; i++) {
            let ir = rows[i];
            if (ir.post_code) {
                let areaCode = (ir.post_code || '').substring(0, ir.post_code.length - 3).trim();
                let matchedDistrict = meta_districts.find(item => item.area_codes.includes(areaCode));
                let district_name = (matchedDistrict && matchedDistrict.district_name) || null;
                if (district_name) {
                    sails.log.info(`ir.id: ${ir.id}, district_name: ${district_name}`);
                    await sails.sendNativeQuery(`UPDATE induction_request set district = $1 WHERE id = $2`,
                        [district_name, ir.id]
                    );

                    count++;
                }
            }
        }

        sails.log.info(`Successfully updated.`);
        return ResponseService.successResponse(res, { message: `Successfully updated ${count} records.` });
    },

    seedPermitTypes: async (req, res) => {
        let permit_type_en_gb = ['Access Permit', 'Asbestos Management', 'Break Ground', 'Confined Space', 'Demolition', 'Electrical Work', 'Hot Works', 'Ladder Use', 'Lifting', 'Permit to Load', 'Permit to Pump', 'Permit to Strike', 'Permit to Work', 'Pipework Pressure Systems', 'Work on Wet Systems', 'Working at Height'];
        let permit_type_en_us = [];
        let permit_type_de_de = [];

        await sails.models.inndexsetting.destroy({
            name: { in: [ "permit_type_en_gb", "permit_type_en_us", "permit_type_de_de" ] }
        });

        sails.log.info('truncate success, setting name: permit_type');

        sails.log.info('Seeding permit types');

        let records = await sails.models.inndexsetting.createEach([
            { name: 'permit_type_en_gb', value: permit_type_en_gb },
            { name: 'permit_type_en_us', value: permit_type_en_us },
            { name: 'permit_type_de_de', value: permit_type_de_de },
        ]);

        sails.log.info('insert success');
        return ResponseService.successResponse(res, {records});
    },
    seedTravelTimeData: async(req,res) => {
        let limit = req.param('chunksize',50);
        const sleepTimeout = req.param("sleep") ? +req.param("sleep") : 300;
        let failedInductions = req.body.failed;
        let sql = `select travel_time, user_ref, id, travel_time->'coordinate' user_lat_long, additional_data->'project'->'custom_field'->'location' project_lat_long from induction_request where travel_time->>'to_work' is null and travel_time->>'to_home' is null and travel_time->>'coordinate' is not null and additional_data->'project'->'custom_field'->>'location' is not null ${failedInductions && failedInductions.length ?  ` and id not in (${failedInductions.map(id => +id)})` : ''} and status_code in (2) and additional_data->'user_info'->>'email' not like 'info+shadow%' order by id limit ${+limit}`;
        let records = await runReadQuery(sql);
        if(!records.length){
            ResponseService.successResponse(res, {stop: true});
            return
        }
        let updatedInductions = [];
        for(let record of records){
            let oldTravelTimeData = record.travel_time;
            let project_coordinate = record.project_lat_long, user_coordinate = record.user_lat_long;
            if(!project_coordinate.lat || !project_coordinate.long || !user_coordinate.lat || !user_coordinate.long ){
                updatedInductions.push({id:record.id, status:'failed'});
                sails.log.info(`coordinate data not found for induction id - ${record.id}`);
                continue;
            }
            sails.log.info(`updating travel_time data for induction id - ${record.id}`);
            let response =  await calculateDistance(project_coordinate, user_coordinate, true);
            if(!response){
                updatedInductions.push({id:record.id, status:'failed'});
                sails.log.info(`updation failed for induction id - ${record.id}, status - failed`);
                continue;
            }
            let travelTime = response.routes &&
                             response.routes[0] &&
                             response.routes[0].duration;
            let duration = `PT${Math.round(travelTime / 60)}M`;
            let newTravelTimeData = {...oldTravelTimeData, to_home: duration, to_work: duration};
            let updated = await sails.models.inductionrequest.updateOne({id:record.id}).set({travel_time:newTravelTimeData});
            if(!updated){
                updatedInductions.push({id:record.id, duration, status:'failed'});
                sails.log.info(`updation failed for induction id - ${record.id}, status - failed, duration - ${duration} `);
                continue;
            }
            updatedInductions.push({id:record.id, duration, status:'success'});
            sails.log.info(`successfully updated travel time data of induction id - ${record.id}, status - success, duration - ${duration} `);
            await sleep(sleepTimeout);
        }
        sails.log.info("Updated Inductions:", updatedInductions);
        return ResponseService.successResponse(res,{updatedInductions, stop: false})
    },

    seedMobileNumberWithCodeInInductionRequest: async (req, res) => {
        let inductionIds = req.body.inductionIds || [];

        if (!inductionIds || inductionIds.length === 0) {
            return ResponseService.errorResponse(res, {
                message: "No induction IDs provided",
            });
        }

        const record = await sails.models.inndexsetting_reader.findOne({
            where: { name: 'countries_meta' },
            select: ['value'],
        });

        let countries = record.value.map(item => [item.code, item.phone_code]);
        let countriesMap = new Map(countries);

        let variables = [], variableIndex = 0;
        variables.push(...inductionIds);
        let query = `SELECT id, additional_data
            FROM induction_request
            WHERE
            id in (${inductionIds.map(() => `$${++variableIndex}`)})`;

        let induction_requests = await runReadQuery(query, variables);

        if (!induction_requests.length) {
            sails.log.info('[seeder] - No induction requests found to update.');
            return ResponseService.successResponse(res, { message: "No records exist to update", stop: true });
        }

        let failedInductionIds = [];

        induction_requests.forEach(async (ir) => {
            try {
                let additional_data =  ir.additional_data || {};

                let user_info = additional_data.user_info || {};
                let contact_detail = additional_data.contact_detail || {};

                let code = countriesMap.get(user_info.country_code);

                if (code) {
                    sails.log.info(`[seeder] [seedMobileNumberWithCodeInInductionRequest] - Updating induction request ID: ${ir.id} with country code: ${user_info.country_code} and phone code: ${code}`);

                    const updatedContactDetail = { ...contact_detail };

                    if (!updatedContactDetail.mobile_number && contact_detail.mobile_no) {
                        updatedContactDetail.mobile_number = {
                            number: contact_detail.mobile_no,
                            code: code
                        };
                    }

                    if (!updatedContactDetail.emergency_contact_number && contact_detail.emergency_contact_no) {
                        updatedContactDetail.emergency_contact_number = {
                            number: contact_detail.emergency_contact_no,
                            code: code
                        };
                    }

                    if (!updatedContactDetail.home_number && contact_detail.home_no) {
                        updatedContactDetail.home_number = {
                            number: contact_detail.home_no,
                            code: code
                        };
                    }

                    additional_data = {
                        ...additional_data,
                        contact_detail: updatedContactDetail
                    };

                    await sails.models.inductionrequest.update({ id: ir.id }).set({ additional_data });
                } else {
                    sails.log.info(`[seeder] [seedMobileNumberWithCodeInInductionRequest] - Country code not found for induction request ID: ${ir.id}. Skipping.`);
                    failedInductionIds.push(ir.id);
                }
            } catch (error) {
                sails.log.error(`[seeder] [seedMobileNumberWithCodeInInductionRequest] - Failed to update induction request ID: ${ir.id}`, error);
                failedInductionIds.push(ir.id);
            }
        });


        sails.log.info('[seeder] [seedMobileNumberWithCodeInInductionRequest] - Update process completed.');
        sails.log.info(`[seeder] [seedMobileNumberWithCodeInInductionRequest] - failedInductionIds - ${failedInductionIds}`);

        return ResponseService.successResponse(res, {
            message: "Update process completed",
            failedInductionIds,
            stop: false,
        });
    },

    updateInvalidPostcodeProjects: async (req, res) => {
        const newValidPostcodes = req.body.newValidPostcodes;
        const failedUpdates = [];

        for (let postcodeData of newValidPostcodes) {
            const { validPostcode, countryCode, projectId } = postcodeData;
            const coordinates = await postcodeToLatLong(validPostcode, countryCode);

            if (coordinates) {
                const project = await sails.models.project_reader.findOne({ id: projectId })
                const updatedCustomField = { ...project.custom_field, location: coordinates };
                try{
                    const updatedProject = await sails.models.project.updateOne({ id: projectId }).set({ custom_field: updatedCustomField, postcode: validPostcode });
                    sails.log.info(`Successfully updated project [projectId: ${projectId}], custom_field: ${JSON.stringify(updatedCustomField)}`);
                }catch(e){
                    sails.log.info(`Failed to update project [projectId: ${projectId}]`);
                    failedUpdates.push({ projectId, validPostcode, countryCode });
                }
            } else {
                sails.log.info(`Coordinate lookup failed for [projectId: ${projectId}, postcode: ${validPostcode}, country: ${countryCode}]`);
                failedUpdates.push({ projectId, validPostcode, countryCode });
            }
        }
        ResponseService.successResponse(res,{ message: "Update process completed", failedUpdates });
    },


    seedingParentCompanyToProject: async (req, res) => {
        let rawResult = await sails.sendNativeQuery(`SELECT id, contractor, parent_company,
            custom_field->>'country_code' as country_code,
            custom_field->>'timezone' as timezone
            FROM project`
        );

        let projectIds = [];
        if (typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            for (let i=0; i < rawResult.rows.length; i++) {
                let projectInfo = rawResult.rows[i];
                if (projectInfo.parent_company) {
                    continue;
                }
                let country_code = projectInfo.country_code || 'GB';
                let parentCompany = await sails.models.createemployer_reader.findOne({where: {name: projectInfo.contractor, country_code}, select: 'name'});
                if (parentCompany && parentCompany.id) {
                    projectIds.push(projectInfo.id);
                    await sails.models.project.updateOne({id: projectInfo.id}).set({parent_company: parentCompany.id});
                }
            }
        }

        sails.log.info("Updated Projects:", projectIds);
        return ResponseService.successResponse(res,{updated_projects: projectIds})
    },

    /**
     * This seed is used to migrate Company Inductions, Company Inspection Reports and TBTs
     * when we need to change (OR after changing) parant_company of the project.
     *
     * Some of the projects were having parent_company as GB companies, but the actual project was not from GB.
     */
     updateProjectDataForCompanyChange: async (req, res) => {
        const {project_id, from_company, to_company} = req.body;

        let project = await sails.models.project_reader.findOne({
            where: {
                id: project_id,
            },
            select: ['id', 'parent_company'],
        });
        // Update project's parent_company if not updated.
        if(project.parent_company != to_company){
            // await sails.models.project.updateOne({id: project_id}).set({parent_company: to_company});
        }

        //Process Company Inspection Builders
        // select id, enabled, record_id,ib_type from ib_checklist where company_ref = 1155 and project_ref is null;
        let companyIBChecklists = await sails.models.inspectionbuilder_reader.find({
            where: {
                company_ref: from_company,
                ib_type: 'company'
            },
            select: ['id']
        });

        let companyIBChecklistIDs = companyIBChecklists.map(r => r.id);
        sails.log.info(`[updateProjectDataForCompanyChange] Found ${companyIBChecklistIDs.length} company inspection builder checklists. IDs ${companyIBChecklistIDs}`);

        //select id, record_id, project_ref, company_ref, finalised from ib_inspection_report where ib_ref IN () and project_ref IN();
        let projectIBReports = await sails.models.inspectionbuilderreport_reader.find({
            where: {
                ib_ref: {in: companyIBChecklistIDs},
                project_ref: project_id
            },
            select: ['id', 'ib_ref']
        });
        sails.log.info(`[updateProjectDataForCompanyChange] Found ${projectIBReports.length} company IB reports. IDs ${projectIBReports.map(r => r.id)}`);

        let proccessedIbRef = [], ibRefMapping = {}, changedIbReports = [];
        //Process if there are any reports from old company.
        for (let i=0; i < projectIBReports.length; i++) {
            let ibRep = projectIBReports[i];
            if(!proccessedIbRef.includes(ibRep.ib_ref)) { //If not already processed.
                let loadedCmpnyIBChecklist = await sails.models.inspectionbuilder_reader.findOne({id:ibRep.ib_ref});
                delete loadedCmpnyIBChecklist.createdAt;
                delete loadedCmpnyIBChecklist.updatedAt;
                delete loadedCmpnyIBChecklist.id;

                loadedCmpnyIBChecklist.ib_title = `${loadedCmpnyIBChecklist.ib_title} (Migrated)`;
                loadedCmpnyIBChecklist.company_ref = to_company;

                proccessedIbRef.push(ibRep.ib_ref); //Keep track of processed ib_ref.

                sails.log.info(`[updateProjectDataForCompanyChange] Copying company inspection builder checklist from ib_ref ${ibRep.ib_ref}`);
                let newIbChecklist = await sails.models.inspectionbuilder.create(loadedCmpnyIBChecklist);
                sails.log.info(`[updateProjectDataForCompanyChange] Successfully copied IB checklist from company ${from_company} ib_ref ${ibRep.ib_ref} to company ${to_company} new_ib_ref ${newIbChecklist.id}`);
                // Map new ib_ref with old one, ex: {"{{old_ib_ref}}": "{{new_ib_ref}}", "231": "424"}
                ibRefMapping[ibRep.ib_ref] = newIbChecklist.id;
            }
        }

        for (let i=0; i < projectIBReports.length; i++) {
            let ibRep = projectIBReports[i];
            changedIbReports.push(ibRep.id);
            sails.log.info(`[updateProjectDataForCompanyChange] Updating project IB report ID, ${ibRep.id} from ib_ref, ${ibRep.ib_ref} to ${ibRefMapping[ibRep.ib_ref]}`);
            await sails.models.inspectionbuilderreport.updateOne({id: ibRep.id}).set({ib_ref: ibRefMapping[ibRep.ib_ref]});
        }

        //Process Company Inductions

        let company_induction_sql = `SELECT ci.*
            FROM company_induction ci
            WHERE ci.company_ref = ${from_company}
            AND ci.user_ref NOT IN (
                SELECT user_ref
                FROM company_induction
                WHERE company_ref = ${to_company}
            )
            AND ci.user_ref IN (
                SELECT user_ref
                FROM induction_request
                WHERE project_ref = ${project_id}
            );`;
        let inductionsToCopy = await runReadQuery(company_induction_sql);
        sails.log.info(`[updateProjectDataForCompanyChange] Got ${inductionsToCopy.length} company induction records to copy, for company ${from_company}, project ${project_id}.`);

        let companyInductionMapping = [];
        for (let j = 0; j < inductionsToCopy.length; j++) {
            let inductionPayload = inductionsToCopy[j];
            let mapData = {old_induction_id: inductionPayload.id, from_company, to_company};

            delete inductionPayload.id;
            delete inductionPayload.record_id;
            delete inductionPayload.createdAt;
            delete inductionPayload.updatedAt;
            inductionPayload.company_ref = to_company;
            let company_induction = await sails.models.companyinduction.create(inductionPayload);
            mapData['new_induction_id'] = company_induction.id;
            sails.log.info(`[updateProjectDataForCompanyChange] Company induction copied successfully, old_induction_id: ${mapData.old_induction_id}, id: ${company_induction.id}, expire on: ${company_induction.expire_on}, status: ${company_induction.status_code}`);
            companyInductionMapping.push(mapData);
        }

        ResponseService.successResponse(res,{
            message: "Migartion process completed!",
            migrated_ib_refs: ibRefMapping,
            changed_ib_reports: changedIbReports,
            migrated_inductions: companyInductionMapping
        });
    },

    seedIbChecklistIntoProject: async (req, res) => {
        let companiesList = await sails.models.createemployer.find({
            where: {
                has_company_portal: true
            },
            select: ['id']
        });
        let companyIds = (companiesList || []).map(c => +c.id);
        let projectIds = [];

        sails.log.info('Fetched companies to get projects :-', companyIds);
        let projectLists = await sails.models.project_reader.find({
            where: {
                parent_company: companyIds,
            },
            select: ['id', 'parent_company'],
        }).sort([
            {parent_company: 'ASC'}
        ]);

        sails.log.info('Fetched ib_checklists for companies :-', companyIds);
        let companyIBChecklists = await sails.models.inspectionbuilder.find({
            where: {
                company_ref: companyIds,
                ib_type: 'company',
                enabled: true
            },
            select: ['id', 'company_ref', 'ib_type', 'project_ref', 'activate_on_projects', 'ib_title', 'enabled']
        });

        for (let i=0; i < projectLists.length; i++) {
            sails.log.info('Fetch project IB checklists filter.', projectLists[i].id);
            let projectIBChecklists = await sails.models.inspectionbuilder.find({
                where: {
                    project_ref: projectLists[i].id,
                    ib_type: 'project',
                    enabled: true
                },
                select: ['id', 'company_ref', 'ib_type', 'project_ref', 'ib_title', 'enabled']
            });

            if(projectIBChecklists){
                projectIds.push(projectLists[i].id);
                sails.log.info('Loop continued for this id:-', projectLists[i].id);
                continue;
            }

            sails.log.info('Fetch company IB checklists filter.', projectLists[i].parent_company);
            let companyIBChecklistforProject = companyIBChecklists.filter(ele => ele.company_ref === projectLists[i].parent_company);
            let ibChecklists = (companyIBChecklistforProject || []).filter(inspection => isEnabledForProject(inspection, projectLists[i].id));
            if (ibChecklists.length > 0) {
                projectIds.push(projectLists[i].id);
            }
        }

        sails.log.info('Projects with ib_checklist ->', projectIds, 'total ->', projectIds.length);
        return ResponseService.successResponse(res,{projectIds: projectIds});
    },

    // Example Payload for seedFaceCollectionForProjects('/api/seed/oto/create-face-collection-for-projects');
    // Payload: {
    //     "exclude_projects":[
    //         21,22,23,24
    //     ]
    // }
    seedFaceCollectionForProjects: async (req, res) => {
        const {exclude_projects} = req.body;
        let projectIds = await sails.models.project.find({
            where: {
                is_active: 1
            },
            select: ['id']
        });
        projectIds = (projectIds || []).map(c => +c.id);
        let filteredProjectIds = projectIds;
        if(exclude_projects){
            sails.log.info(`Excluding these projects ${exclude_projects}`);
            filteredProjectIds = projectIds.filter(val => !exclude_projects.includes(val));
        }

        for(let i=0; i < filteredProjectIds.length; i++){
            let createCollection = await triggerCreateCollection(filteredProjectIds[i]);
            sails.log.info(`Created collection for: ${createCollection.collectionId}`);
        }
        sails.log.info(`Successfully created collection for all projects: ${filteredProjectIds}`);
        return ResponseService.successResponse(res,{projectIds: filteredProjectIds})
    },

    convertDocToPdf: async (req, res) => {
        let tool = req.param('tool','toolbox-talks'); //tt || tb || wpp || rams
        let action = req.param('action','view'); //view || update

        let records = [];
        let rawQuery = '';
        let file_field = 'talk_file_ref';
        let tableWriter = 'toolboxtalks';
        if (tool === 'toolbox-talks') {
            rawQuery = `SELECT tt.id tool_record_id, tt.talk_file_ref tool_file_id, uf.id uf_id, uf.name uf_name, uf.file_url uf_file_url, uf.user_id uf_user_id FROM toolbox_talks tt LEFT JOIN user_file uf ON uf.id = tt.talk_file_ref WHERE tt.talk_file_ref IS NOT NULL AND (uf.file_mime = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' OR uf.file_mime = 'application/msword');`;
            file_field = 'talk_file_ref';
            tableWriter = 'toolboxtalks';
        } else if (tool === 'task-briefings') {
            rawQuery = `SELECT tb.id tool_record_id, tb.briefing_file_ref tool_file_id, uf.id uf_id, uf.name uf_name, uf.file_url uf_file_url, uf.user_id uf_user_id FROM project_task_briefings tb LEFT JOIN user_file uf ON uf.id = tb.briefing_file_ref WHERE tb.briefing_file_ref IS NOT NULL AND (uf.file_mime = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' OR uf.file_mime = 'application/msword');`;
            file_field = 'briefing_file_ref';
            tableWriter = 'projecttaskbriefings';
        } else if (tool === 'wpp') {
            rawQuery = `SELECT wpp.id tool_record_id, wpp.briefing_file_ref tool_file_id, uf.id uf_id, uf.name uf_name, uf.file_url uf_file_url, uf.user_id uf_user_id FROM project_work_package_plans wpp LEFT JOIN user_file uf ON uf.id = wpp.briefing_file_ref WHERE wpp.briefing_file_ref IS NOT NULL AND (uf.file_mime = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' OR uf.file_mime = 'application/msword');`;
            file_field = 'briefing_file_ref';
            tableWriter = 'projectworkpackageplans';
        } else if (tool === 'rams') {
            rawQuery = `SELECT rams.id tool_record_id, rams.briefing_file_ref tool_file_id, uf.id uf_id, uf.name uf_name, uf.file_url uf_file_url, uf.user_id uf_user_id FROM project_rams rams LEFT JOIN user_file uf ON uf.id = rams.briefing_file_ref WHERE rams.briefing_file_ref IS NOT NULL AND (uf.file_mime = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' OR uf.file_mime = 'application/msword');`;
            file_field = 'briefing_file_ref';
            tableWriter = 'projectrams';
        } else {
            return ResponseService.errorResponse(res, 'Invalid tool selected.');
        }

        let rawResult = await sails.sendNativeQuery(rawQuery);
        records = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];

        if (!records.length) {
            return ResponseService.successResponse(res,{"message": "No records found."})
        }
        sails.log.info(`tool records with word documents,`, records, records.length);

        if(action === 'view') {
            return ResponseService.successResponse(res,{records});
        }

        let failedFiles = [];
        for (let i = 0; i < records.length; i++) {
            let toolRecord = records[i];
            let docFileUrl = toolRecord.uf_file_url;
            let docFileName = toolRecord.uf_name;
            let outputFileName = docFileName.split(".");
            let urlParts = docFileUrl.split(".");

            let {
                success,
                statusCode,
                data
            } = await triggerLambdaFn(sails.config.custom.LAMBDA_DOC_TO_PDF_FN, {
                    doc_url: docFileUrl,
                    fromExt: urlParts[urlParts.length-1],
                    toExt: 'pdf',
                    feature_name: tool,
                }
            );

            sails.log.info(`Response received from lambda, doc to pdf.`, success, statusCode,  data);

            if(success && statusCode === 200 && (data.Location || data.location)) {
                const public_url = data.public_url || data.Location || data.location;
                sails.log.info(`Received converted PDF from lambda fn, path: ${public_url}`);
                let record = {
                    file_url : public_url,
                    file_mime : 'application/pdf',
                    name: outputFileName[0]+'.pdf',
                    user_id: toolRecord.uf_user_id,
                    category: tool
                };
                let user_file = await sails.models.userfile.create(record);
                sails.log.info('User File record', user_file.id);

                let payload = {};
                payload[file_field] = user_file.id;
                await sails.models[tableWriter].updateOne({id: toolRecord.tool_record_id}).set(payload);
                sails.log.info(`Tool record ${toolRecord.tool_record_id} has been updated, old file id: ${toolRecord.tool_file_id}, new file id ${user_file.id}`);
            } else {
                failedFiles.push(toolRecord.tool_file_id);
                sails.log.info(`Failed to convert the doc file to pdf. Please try again after sometime.`);
            }
        }
        return ResponseService.successResponse(res,{failedFiles});
    },

    /*Removed*/
    backFillRamsForRevision: async (req, res) => {
        let project_rams_sql = `SELECT project_ref
                             FROM project_rams
                             GROUP BY project_ref`;
        let projectIds = await runReadQuery(project_rams_sql);

        for (let i = 0; i < projectIds.length; i++) {
            let project = projectIds[i];
            sails.log.info(`update rams record for project ${project.project_ref}`);
            let rams_sql = `SELECT id
                             FROM project_rams
                             where project_ref = ${project.project_ref} order by id asc`;
            let ramsRecords = await runReadQuery(rams_sql);
            sails.log.info(`Got ${ramsRecords.length} rams record to update.`);

            for (let j = 0; j < ramsRecords.length; j++) {
                let rams = ramsRecords[j];
                let payload = {
                    revision_number: `Revision #${j+1}`,
                    revision_status: true,
                    reference_number: j+1,
                    group_id: j+1
                }
                sails.log.info(`---------${j}---------`, payload)
                await sails.models.projectrams.updateOne({id: rams.id}).set(payload);
            }
        }

        return ResponseService.successResponse(res,{projectIds});
    },

    incidentReportInsertUserRefInPersonAffected: async (req, res) => {
        sails.log.info('Starting user_ref seeding in person_affected column for incident reports');

        // Query to fetch records with person type `Employee`
        const recordsSql = `
            SELECT
                id,
                person_affected,
                project_ref
            FROM
                project_incident_report
            WHERE EXISTS (
                    SELECT 1
                    FROM JSON_ARRAY_ELEMENTS(person_affected) AS person
                    WHERE person->>'person_type' = 'Employee'
                );
        `;
        const records = await runReadQuery(recordsSql);
        sails.log.info(`Fetched ${records.length} records to populate person_affected with user_ref`);

        if (!records.length) {
            return ResponseService.successResponse(res, { message: 'No records to process' });
        }
        let tempRecords = [...records];
        const personQueries = [];
        const recordMap = new Map();

        // Build the person queries and map records
        records.forEach(record => {
            if (record.person_affected && record.person_affected.length) {
                record.person_affected.forEach(person => {
                    if (person.person_type === 'Employee') {
                        const key = `${record.project_ref}-${person.f_name.toLowerCase()}-${person.l_name.toLowerCase()}`;
                        personQueries.push({ project_ref: record.project_ref, f_name: person.f_name, l_name: person.l_name });
                        recordMap.set(key, { record, person });
                    }
                });
            }
        });

        sails.log.info(`Generated ${personQueries.length} person queries for user_ref mapping`);

        if (!personQueries.length) {
            return ResponseService.successResponse(res, { message: 'No person_affected records to update' });
        }

        // Query to match users
        const userQuerySql = `
            WITH matched_users AS (
                SELECT
                    project_ref,
                    additional_data->'user_info'->>'first_name' AS first_name,
                    additional_data->'user_info'->>'last_name' AS last_name,
                    user_ref,
                    COUNT(*) OVER (
                        PARTITION BY
                            project_ref,
                            additional_data->'user_info'->>'first_name',
                            additional_data->'user_info'->>'last_name'
                        ) AS match_count
                FROM induction_request
                WHERE (project_ref, LOWER(additional_data->'user_info'->>'first_name'), LOWER(additional_data->'user_info'->>'last_name'))
                IN (${personQueries.map((_, i) => `($${i * 3 + 1}, LOWER($${i * 3 + 2}), LOWER($${i * 3 + 3}))`).join(',')})
            ),
            validated_users AS (
                SELECT *
                FROM matched_users
                WHERE match_count = 1
                OR project_ref IN (
                    SELECT project_ref
                    FROM matched_users
                    GROUP BY project_ref, user_ref
                    HAVING COUNT(DISTINCT user_ref) = 1
                )
            )
            SELECT *
            FROM validated_users;
        `;
        const userParams = personQueries.flatMap(q => [q.project_ref, q.f_name, q.l_name]);
        const users = await runReadQuery(userQuerySql, userParams);

        sails.log.info(`Matched ${users.length} users for the provided person queries`);

        const userMap = new Map();
        users.forEach(user => {
            const key = `${user.project_ref}-${user.first_name.toLowerCase()}-${user.last_name.toLowerCase()}`;
            userMap.set(key, user.user_ref);
        });

        const recordsToUpdate = [];
        const failedRecords = [];

        // Map user_ref to person_affected and classify records
        records.forEach(record => {
            let hasFailed = true;
            if (record.person_affected && record.person_affected.length) {
                record.person_affected.forEach(person => {
                    const key = `${record.project_ref}-${person.f_name.toLowerCase()}-${person.l_name.toLowerCase()}`;
                    const userRef = userMap.get(key);
                    if (userRef) {
                        person.user_ref = userRef;
                        hasFailed = false;
                    }
                });
            }
            if (hasFailed) {
                failedRecords.push(record);
            } else {
                recordsToUpdate.push(record);
            }
        });

        sails.log.info(`Records to update: ${recordsToUpdate.length}, Failed records: ${failedRecords.length}`);

        // Update the records in the database with updates user_ref
        const updatePromises = recordsToUpdate.map(record =>{
            sails.log.info(`udating record - ${JSON.stringify(record)}`);
             return sails.models.projectincidentreport.updateOne(
                { id: record.id },
                { person_affected: record.person_affected }
            )
        });

        const updatedRecords = await Promise.allSettled(updatePromises);

        // Log and respond with results
        const successfulUpdates = updatedRecords.filter(r => r.status === 'fulfilled').map(r => r.value);
        sails.log.info(`Successfully updated ${successfulUpdates.length} records`);
        sails.log.info('Failed to update records:', failedRecords);

        ResponseService.successResponse(res, {
            all_records: tempRecords,
            updated_records: successfulUpdates,
            failed_records: failedRecords
        });

    },


    rollCallUpdateUserColumns: async (req, res) => {
        let columns = ['id', 'present_users', 'un_accounted_users', 'present_visitors', 'un_accounted_visitors'];
        let pageSize = 500;
        let pageNumber = +req.param('pageNumber', 0);
        if (isNaN(pageNumber) || pageNumber < 0) {
            return ResponseService.errorResponse(res, 'Invalid Request.');
        }

        let rollCalls = await sails.models.rollcall_reader.find({
            select: columns,
            skip : pageNumber * pageSize,
            limit: pageSize
        }).sort([
            {id: 'ASC'}
        ]);

        for (let i = 0; i < rollCalls.length; i++) {
            let rollCall = rollCalls[i];
            let payload = {};
            payload.present_users = (rollCall.present_users.length) ? convertArrayOfNumbersToObjects(rollCall.present_users, 'user_ref') : [];
            payload.un_accounted_users = (rollCall.un_accounted_users.length) ? convertArrayOfNumbersToObjects(rollCall.un_accounted_users, 'user_ref') : [];
            payload.present_visitors = (rollCall.present_visitors.length) ? convertArrayOfNumbersToObjects(rollCall.present_visitors, 'visitor_ref') : [];
            payload.un_accounted_visitors = (rollCall.un_accounted_visitors.length) ? convertArrayOfNumbersToObjects(rollCall.un_accounted_visitors, 'visitor_ref') : [];

            await sails.models.rollcall.updateOne({id: rollCall.id}).set(payload);
        }

        return ResponseService.successResponse(res,{'message': 'records updated.'});
    },

    correctingIbReports: async (req, res) => {
        let ibIds = req.body.ibIds.split(',');
        sails.log.info(`Updating ib reports of ibs ${req.param('ibIds', '')}`);
        //(858, 874, 1045, 509, 919, 888, 733, 914, 644, 506, 913, 841, 466, 822, 920, 241, 512, 906, 1078, 501, 810, 811, 814, 850, 645, 276, 513, 1044, 507, 897, 921, 862, 300, 608, 463, 367, 374, 1019, 586, 572, 825, 885, 270, 911, 462, 605, 838, 505, 461, 240, 843, 1043, 468, 643, 373, 609, 372, 243, 808, 377, 585, 370, 511, 514, 504, 1042, 798, 376, 610, 510, 907, 917, 516, 882, 460, 371, 835, 928, 742, 912)
        let reportUpdateBefore = *************;
        let variables = [...ibIds];
        let variableIndex = 0;
        let rawQuery = `SELECT id, has_subheadings FROM ib_checklist WHERE id IN (${ibIds.map(()=>`$${++variableIndex}`)})`;
        let rows = await runReadQuery(rawQuery, variables);
        for (let i = 0; i < rows.length; i++) {
            let ibChecklist = rows[i];
            let rawIBReportQuery = `SELECT id, checklist, additional_checklist FROM ib_inspection_report WHERE "createdAt" <= ${reportUpdateBefore} AND ib_ref= ${ibChecklist.id}`;
            let ibReports = await runReadQuery(rawIBReportQuery);
            sails.log.info(`got ${ibReports.length} reports for ib ${ibChecklist.id}`);
            for (let j = 0; j < ibReports.length; j++) {
                let ibReport = ibReports[j];
                let checklist = ibReport.checklist;
                let additional_checklist = ibReport.additional_checklist;
                if(ibChecklist.has_subheadings) {
                    checklist = ibReport.checklist.map((item) => {
                        if (item.subheadings && item.subheadings.length) {
                            item.subheadings = item.subheadings.map((subItem) => ({
                                ...subItem,
                                answer: replaceRating(subItem.answer),
                            }))
                        }

                        return item;
                    });

                    additional_checklist = ibReport.additional_checklist.map((item) => {
                        if (item.subheadings && item.subheadings.length) {
                            item.subheadings = item.subheadings.map((subItem) => ({
                                ...subItem,
                                answer: replaceRating(subItem.answer),
                            }))
                        }
                        return item;
                    });
                } else {
                    checklist = ibReport.checklist.map((item) => ({
                        ...item,
                        answer: replaceRating(item.answer),
                    }));

                    additional_checklist = ibReport.additional_checklist.map((item) => ({
                        ...item,
                        answer: replaceRating(item.answer),
                    }));
                }

                await sails.models.inspectionbuilderreport.updateOne({id: ibReport.id}).set({checklist, additional_checklist});
            }
        }

        return ResponseService.successResponse(res,{'message': 'records updated.'});
    },

    updateProjectPermitConfig: async (req, res) => {
        let pageSize = 500;
        let pageNumber = +req.param('pageNumber', 0);
        if (isNaN(pageNumber) || pageNumber < 0) {
            return ResponseService.errorResponse(res, 'Invalid Request.');
        }

        let permitProjectConfigs = await sails.models.projectpermitconfig_reader.find({
            select: ['id', 'sign_off', 'permit_ref'],
            skip : pageNumber * pageSize,
            limit: pageSize
        }).sort([
            {id: 'DESC'}
        ]);

        if(!permitProjectConfigs.length) {
            return ResponseService.successResponse(res,{"message": "No more records to update."})
        }

        let permitRefs = permitProjectConfigs.map(ppc => ppc.permit_ref);
        permitRefs = _.uniq(permitRefs).filter(id => id);

        sails.log.info("permitRefs: ", permitRefs.length);

        let permitTemplates = await sails.models.permittemplate_reader.find({
            where: {
                id: permitRefs
            },
            select: ['signatures']
        });

        for (let i = 0; i < permitProjectConfigs.length; i++) {
            let permitConfig = permitProjectConfigs[i];
            let permit_template = permitTemplates.find(pt => pt.id === permitConfig.permit_ref);
            if(!permit_template) {
                sails.log.info(`permit template not found for config ${permitConfig.id}.`);
                continue;
            }

            let sign_off = await associateStateWithSignOff(permitConfig.sign_off, permit_template.signatures);
            await sails.models.projectpermitconfig.updateOne({id: permitConfig.id}).set({
                sign_off
            });
        }

        return ResponseService.successResponse(res,{'message': 'records updated.'});
    },

    seedCloseCallDefaults: async (req, res) => {
        let { project_ids } = await runReadQuery(`SELECT ARRAY_AGG(p.id) as project_ids
                           FROM project p
                           WHERE NOT EXISTS (
                               SELECT 1
                               FROM project_setting ps
                               WHERE ps.project_ref = p.id
                               AND ps.name = 'close_call_custom_fields'
                           )`, [], true);
        if(!project_ids || !project_ids.length) {
            return ResponseService.errorResponse(res,{message: 'All projects include custom fields for close call data'});
        }
        sails.log.info('feeding default close call data to close_call_custom_fields for projects - ', project_ids);

        customCloseCallData = project_ids.map(p => ({ project_ref: p, value: DataToSeedService.defaultDataForCloseCallCustomField, name: 'close_call_custom_fields' }));
        await sails.models.projectsetting.createEach(customCloseCallData);
        return ResponseService.successResponse(res, { 'message': 'records updated.' });
    },

    updatePermitRequests: async (req, res) => {
        let pageSize = 500;
        let pageNumber = +req.param('pageNumber', 0);
        if (isNaN(pageNumber) || pageNumber < 0) {
            return ResponseService.errorResponse(res, 'Invalid Request.');
        }

        let permitRequests = await sails.models.permitrequest_reader.find({
            select: ['state'],
            where: {
                state: {'in': [91, 95, 100]},
            },
            skip : pageNumber * pageSize,
            limit: pageSize
        }).sort([
            {id: 'DESC'}
        ]);

        if(!permitRequests.length) {
            return ResponseService.successResponse(res,{"message": "No more records to update."})
        }

        for (let i = 0; i < permitRequests.length; i++) {
            let permitRequest = permitRequests[i];
            if(permitRequest.state === 91) {
                permitRequest.state = 200;
            } else if(permitRequest.state === 95) {
                permitRequest.state = 400;
            } else {
                permitRequest.state = 500;
            }
            await sails.models.permitrequest.updateOne({id: permitRequest.id}).set({
                state: permitRequest.state
            });
        }

        return ResponseService.successResponse(res,{'message': 'records updated.'});
    },

    // This seeding function is designed to seed project_timesheets data into the weekly_project_timesheet (parent table).
    // It seeds user_ref, project_ref, modified_by, comment and status from the project_timesheet table,
    // while adding weekly_timesheet_ref to gather timesheet week data.
    // Payload = {
    //   offset: 0 (it will increase according to API call),
    //   limit: 100 (this number can be adjusted as needed)
    // }
    seedWeeklyProjectTimesheet: async (req, res) => {
        let skip = +req.param('offset');
        let BATCH_SIZE = +req.param('limit');

        let timesheets = await sails.models.projecttimesheet_reader.find({
            limit: BATCH_SIZE,
            skip: skip,
            sort: 'id ASC'
        });
        if(!timesheets.length) {
            sails.log.info('No more timesheets to seed data');
            return ResponseService.successResponse(res,{'message': 'No more timesheets to seed data.'});
        }
        sails.log.info(`${timesheets.length} timesheets found, starting seeding process.`);
        let grouped = {};
        let projectIds = [...new Set(timesheets.map(ts => ts.project_ref))];
        const rawQuery = `SELECT id, custom_field->>'timesheet_week_end' AS timesheet_week_end FROM project WHERE id = ANY($1)`;
        const rawResults = await sails.sendNativeQuery(rawQuery, [projectIds]);

        sails.log.info('unique project ids', projectIds,);

        for (let i = 0; i < BATCH_SIZE; i++) {
            let ts = timesheets[i];
            if (!ts || !ts.day_of_yr || !ts.user_ref || !ts.project_ref) {
                sails.log.info('Skipping invalid timesheet:', ts);
                continue;
            }

            // Compute correct week_end_date
            const currentDate = ts?.day_of_yr;
            const associatedProject = rawResults.rows.find((ele) => ele.id === ts.project_ref);
            const timesheet_week_end_day = associatedProject.timesheet_week_end;
            sails.log.info(`Timesheet week end date:${timesheet_week_end_day} for project:${associatedProject.id}`);
            let endDate = computeWeekEndDate(currentDate, timesheet_week_end_day);

            const weekKey = `${ts.user_ref}_${ts.project_ref}_${endDate}`;

            if (!grouped[weekKey]) {
                grouped[weekKey] = {
                    user_ref: ts.user_ref,
                    project_ref: ts.project_ref,
                    week_end_date: endDate,
                    status: ts.status || 1,
                    modified_by: ts.modified_by || null,
                    comments: ts.comments || [],
                    timesheets: []
                };
            }
            grouped[weekKey].timesheets.push(ts.id);
        }


        const weeklyTimesheets = Object.values(grouped);
        const weeklyToInsert = weeklyTimesheets.map(ts => ({ ...ts }));
        sails.log.info('Preparing to insert weekly timesheets:', weeklyToInsert);

        const weeklyCreated = await Promise.all(weeklyToInsert.map(async (item) => {
            const existing = await sails.models.projectweeklytimesheet.findOne({
                user_ref: item.user_ref,
                project_ref: item.project_ref,
                week_end_date: item.week_end_date
            });
            if (existing) {
                sails.log.info(`Reusing existing weekly timesheet ID: ${existing.id} for ${item.user_ref}_${item.project_ref}_${item.week_end_date}`);
                return existing;
            } else {
                return await sails.models.projectweeklytimesheet.create(item);
            }
        }));

        sails.log.info('Successfully created timesheet weeks', weeklyCreated.length);

        const timesheetIds = {};
        for (const group of weeklyTimesheets) {
            const weekEndDate = group.week_end_date;
            const key = `${group.user_ref}_${group.project_ref}_${weekEndDate}`;
            sails.log.info('generated week key in weeklyTimesheets', key);
            timesheetIds[key] = group.timesheets;
        }
        sails.log.info(`Timesheets to sync with the weekly timesheets`, timesheetIds)

        const result = weeklyCreated.map((createdItem) => {
            const weekEndDate = dateToBeFormated(createdItem.week_end_date);
            const key = `${createdItem.user_ref}_${createdItem.project_ref}_${weekEndDate}`;
            return {
                weekly_timesheet_ref: createdItem.id,
                timesheets: timesheetIds[key] || []
            };
        });

        let updatePayloads = result.flatMap(({ weekly_timesheet_ref, timesheets }) =>
            timesheets.map(id => ({ id, weekly_timesheet_ref }))
        );

        if (!updatePayloads.length) {
            sails.log.warn('No timesheet updates to process.');
        }

        let groupedUpdates = {};
        for (const { id, weekly_timesheet_ref } of updatePayloads) {
            if (!groupedUpdates[weekly_timesheet_ref]) {
                groupedUpdates[weekly_timesheet_ref] = [];
            }
            groupedUpdates[weekly_timesheet_ref].push(id);
        }

        let updatedResults = [];
        for (const [weeklyRef, timesheetIds] of Object.entries(groupedUpdates)) {
            const updated = await sails.models.projecttimesheet.update({ id: timesheetIds }).set({
                weekly_timesheet_ref: parseInt(weeklyRef)
            });
            updatedResults.push(...updated);
        }

        sails.log.info(`Timesheets updated successfully ${updatedResults.length}`);
        return ResponseService.successResponse(res, {project_weekly_timesheet: weeklyCreated, project_timesheets: updatedResults});
    },

seedMaterials: async (req, res) => {
        let materials_en_gb = {
            'materials': ['Aggregate', 'Bituminous material such as roofing membrane and asphalt', 'Brick (inc. clay tiles and other ceramics)', 'Cement/mortar/lime', 'Concrete', 'Concrete blockwork', 'Concrete pavers', 'Concrete reinforced in-situ', 'Construction waste material', 'Fixture & fittings', 'Glass', 'Insulation', 'Joinery', 'MDF - Medium Density Fibreboard', 'Metals', 'Paint or timber treatment', 'Planed/machined wood (hard)', 'Planed/machined wood (soft)', 'Plasterboard & plaster', 'Plastics & rubbers', 'Reinforcement bars', 'Resin-based composites and materials', 'Sand', 'Sawnwood (hard)', 'Sawnwood (soft)', 'Structural steel', 'Timber/timber-based product', 'Vaneer'],
            'units': ['Box(s)', 'Bundle(s)', 'Canister(s)', 'Container(s)', 'Kilogram(s)', 'Length(s)', 'Load(s)', 'Meter(s)', 'Pallet(s)', 'Pallet Equivalent(s)', 'Sillage', 'Skip(s)', 'Unit(s)', 'Wooden Crate(s)', 'litre(s)', 'm2', 'm3', 'sheet(s)', 'tonne(s)']
        }
        await sails.models.inndexsetting.destroy({
            name: { in: [ "materials_en_gb", "materials_en_us", "materials_de_de" ] }
        });

        sails.log.info('truncate success, setting name: materials');

        sails.log.info('Seeding permit types');

        let records = await sails.models.inndexsetting.createEach([
            { name: 'materials_en_gb', value: materials_en_gb },
            { name: 'materials_en_us', value: materials_en_gb },
            { name: 'materials_de_de', value: materials_en_gb },
        ]);

        sails.log.info('insert success');
        return ResponseService.successResponse(res, {records});
    },
};
