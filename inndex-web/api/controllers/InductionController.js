/**
 * Created by spatel on 6/10/18.
 */
'use strict';

const {
    AccessLogService,
    WeatherSyncService,
} = require('./../services');
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {PdfUtil, TokenUtil, ResponseService, EmailService, NotificationService, SmartSheetService, HttpService} = require('./../services');
const {
    DVLA_API,
    DEFAULT_PAGE_SIZE,
    isShadowUser,
    COMPANY_SETTING_KEY,
    EMAIL_NOTIFICATION_FEATURE_CODES,
    fallback_locale,
    PROJECT_SETTING_KEY: {UK_DISTRICTS},
    displayDateFormat_DD_MM_YYYY,
    dbDateFormat_YYYY_MM_DD,
} = sails.config.constants;
const moment = require('moment');
const _uniq = require('lodash/uniq');
const momentTz = require('moment-timezone');
const dayjs = require('dayjs');
const {
    InductionRequestValidator,
    UserInfoValidator: {
        userEmploymentUpdate
    },
} = require('./../validators');

const {
    translateUserDocIntoPages,
    getUserVerifiableUserDocuments,
    attachUserDocument,
    expandUserDocs,
    populateDocumentChildren,
    populateUserFileRefs,
    getPendingAssessmentStatus,
    getMandatoryCompetencyExceptionList,
    expandUserDocFiles,
    updateUserInductionsWithProfileChanges,
    sendInductionStatusChangeAlert,
    getProjectTimezone,
    getUserFullName,
    constructDistanceMatrix,
    getActiveTravelTime,
    getDistanceMatrix,
    deriveDistanceMatrixRegion,
    getCoordinates,
    calculateDistance,
    updateTravelTime,
    getProjectDistrict,
    getDailyTimeEventForDay,
    isStillOnSite,
} = require('./../services/DataProcessingService');
const {
    HttpService: {makePOST, typeOf, decodeURIParam},
    VehicleService: {
        fetchVehicleRegDetails,
    },
    RightToWorkService: {
        subscribeToRtwStatusChangeForInduction,
        extractRtwStatus,
    },
    ProcoreService,
    SharedService: {
        s3UploaderWithExpiry,
        instantPdfGenerator,
        downloadPdfViaGenerator,
    },
    CSCSService: {
        getCompanyCSCSSettingInfo,
    },
    TokenUtil: {
        storeUACRecordsOrError,
        ROLES,
        resourceIdentifier,
        buildInductionStatusMessage
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
    },
    DataProcessingService: {
        getDailyTimeEventV2,
        populateUserRefs,
        populateProjectRefs,
        populateEmployerRefs,
        buildCompanyInductionStatus,
    },
    FeatureExclusionUtil: {
        profileHealthAssessmentExcluded,
        profileMedAssessmentExcluded,
        inductionHealthAssessmentExcluded,
        profileEmplymentNinExcluded,
        profileEmplymentMinWageExcluded,
        profileEmplymentTypeExcluded,
        profileEmplymentStartDateExcluded,
        isProfileEmploymentEmpNbrMandatory,
        checkProjectPostcodeLookup
    },
    EmailService: {
        queueEmailNotifications,
    },
    RekognitoService: {
        indexInductionUserFaceOrError,
        searchInductionUserFace,
        triggerDeleteFaceFromCollection,
    },
    ExcelService: {
        getInductionRecordsWorkbook,
        streamExcelDownload,
        getCompanyInductionsWorkbook,
    }
} = require('./../services');
const {DVLA_API_HOST_URL, DVLA_API_KEY} = sails.config.custom;
const STATUS_CODES = {
    BLACKLISTED: 5, // Company Block
    BLOCKED: 4,
    IN_REVIEW: 6,
    REJECTED: 0,
    PENDING: 1,
    APPROVED: 2,
    CHANGE_REQUESTED: 3,
};
const QRCode = require('qrcode');
const { v4: uuid4 } = require('uuid');
const _get = _.get;
const {
    companyFn: {getCompanyAccessibleProjects},
    inductionFn: {getInductedAdmins, getProjectInductionsPage, getProjectsAndCompaniesAsSeparateListsFn, getInductionUserEmployers, getInductionUserJobRoles, getProjectInductions, getLastOnSiteUsersByProject, getInductionEmployerByUserIds},
    timeLogFn: {getTotalWorkingDays, getTotalWorkingHours}
} = require('./../sql.fn');
const { runReadQuery } = require('../sql.fn/core.fn');

let getProject = async (projectId, completeRow = false) => {
    try{
        let project = await sails.models.project_reader.findOne({id: projectId});
        if(project && project.id){
            return completeRow ? project : project.created_by;
        }
    }catch(e){
        sails.log.error('Failed to get project admin id', e);
    }
    return null;
};

const getLangFromAllSubsets = ({media_resources, quiz_sets, iq_sets}) => {
    const reducer = (list, m) => {
        if (m && m.lang) {
            list.push(m.lang);
        }
        return list;
    };

    let available_locales = (media_resources || []).reduce(reducer, []);
    available_locales = (quiz_sets || []).reduce(reducer, available_locales);
    available_locales = (iq_sets || []).reduce(reducer, available_locales);

    return [...new Set(available_locales)];
}

let sendInductionCreationAlerts = async (project, induction_request, currentUser, additional_users = []) => {

    let sendToIds = additional_users ? additional_users.map(row => row.id) : [];
    try {
        // Disabled induction notification to creator.
        // if(project && project.created_by){
        //     sendToIds.push(project.created_by);
        // }
        sails.log.info('Alert will be sent to', sendToIds);
        let sendToUsers = await sails.models.user_reader.find({id: sendToIds, is_active: 1});
        let len = (sendToUsers || []).length;
        sails.log.info('Total alerts to send', len);
        let has_medications = (induction_request.on_long_medication && induction_request.on_long_medication.toString().toLowerCase().trim() === 'yes');
        let currentUserAge = (currentUser.dob) ? dayjs().diff(currentUser.dob, 'year') : 0;
        let tz = getProjectTimezone(project);
        let subject = `${project.custom_field.induction_phrase_singlr} Request: Project - "${project.name}"`;
        if(has_medications) { subject += ` ✚`; }
        if(currentUserAge < 18){ subject += ` ⓲`; }

        let recipientsInfo = sendToUsers.map(u => ({id: u.id, name: getUserFullName(u), email: u.email}));
        return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.NEW_INDUCTION_CREATED, recipientsInfo, {
            messageInfo: {
                mail_title: subject,
                project_link: `${sails.config.custom.PUBLIC_URL}project-portal/project/${project.id}/induction?request_id=${induction_request.id}`,
                link_label: `${project.custom_field.induction_phrase_singlr} Request`,
                induction_phrase_singlr: project.custom_field.induction_phrase_singlr,
                has_medications: !!(has_medications && induction_request.medications && (induction_request.medications.length > 0)),
                is_under_18: (currentUserAge < 18),
                creator: {
                    "id": currentUser.id,
                    "name": getUserFullName(currentUser),
                    "first_name": currentUser.first_name,
                    "dob": dayjs(currentUser.dob).tz(tz).format('DD-MM-YYYY'),
                    "currentAge": currentUserAge
                },
                induction: {
                    "id": induction_request.id,
                    "record_id": induction_request.record_id
                }
            },
            project: {
                id: project.id,
                name: project.name,
            },
        });

    } catch (e) {
        sails.log.info('Failed to send all alert ?', e);
    }
};

/*
@todo vishal: this fn can be removed in Jan 2025 after checking warn logs
*/
const getValidMedicationDate = (row, fieldName) => {
    let date = row[fieldName] || null;
    if (date && (moment(date, displayDateFormat_DD_MM_YYYY, true).isValid())) {
        sails.log.warn(`Got ${fieldName} in invalid date format.`);
        date =  moment(date, displayDateFormat_DD_MM_YYYY).format(dbDateFormat_YYYY_MM_DD);
    }
    return date;
};

const storeLongMedicationRecords = async (induction_request, records, skip_delete = false) => {
    try{
        let batchRecords = (records || []).map(row => {
            return {
                induction_ref: induction_request.id,

                medication_name: row.medication_name,
                medication_dosage: row.medication_dosage,
                medication_frequency: row.medication_frequency,
                medication_date_commenced: getValidMedicationDate(row, 'medication_date_commenced'),
                medication_date_of_completion: getValidMedicationDate(row, 'medication_date_of_completion'),
                infinite_completion: row.infinite_completion,
            }
        });

        if(!skip_delete){
            sails.log.info('deleting existing medication detail records');
            let deleted = await sails.models.longmedicationdetail.destroy({induction_ref: induction_request.id});
            sails.log.info('Deleted medications count', deleted ? deleted.length : null);
        }

        if(!batchRecords.length){
            // nothing to insert
            return [];
        }
        // insert all records
        sails.log.info('medication records', batchRecords);
        let medications = await sails.models.longmedicationdetail.createEach(batchRecords);
        sails.log.info('medications inserted successfully', medications ? medications.length : null);
        return medications;
    }catch (e) {
        sails.log.error('Failed to store Long Medication records', e);
        return [];
    }
};

/**
 *
 * @deprecated We need to update callers to stop using it
 *
 * @param projectId
 * @param filters
 * @param select
 * @param pageSize
 * @param pageNumber
 * @param ignorePagination
 * @param includeLongMedication
 * @param userSelect
 * @param inductorSelect
 * @returns {Promise<*>}
 */
const getProjectInductionRequests = async (projectId, filters, select, pageSize, pageNumber, ignorePagination, includeLongMedication, userSelect = [], inductorSelect = ['id', 'first_name', 'middle_name', 'last_name', 'email']) => {
    sails.log.info('Fetch project induction records, id:', projectId, filters, ignorePagination);
    let whereFilter
    if(pageSize !== null && !ignorePagination){
        whereFilter = {
            ...(select.length ? {select} : {}),
            where: {
                ...filters,
                project_ref: projectId
            },
            limit: pageSize,
            skip: pageSize*pageNumber
        }
    } else {
        whereFilter = {
            ...(select.length ? {select} : {}),
            where: {
                ...filters,
                project_ref: projectId
            },
        }
    }
    let induction_requests = await sails.models.inductionrequest_reader
        .find(whereFilter)
        .sort([
            //{status_code: 'DESC'},
            {record_id: 'DESC'},
            {id: 'ASC'},
        ]);

    if(userSelect !== false){
        induction_requests = await populateUserRefs(induction_requests, 'user_ref', userSelect);
    }
    if(inductorSelect !== false){
        induction_requests = await populateUserRefs(induction_requests, 'inductor_ref', inductorSelect);
    }

    sails.log.info(`includeLongMedication: ${includeLongMedication}`);
    if (includeLongMedication) {
        for (let index in induction_requests) {
            if (induction_requests[index].on_long_medication && induction_requests[index].on_long_medication == 'yes') {
                induction_requests[index].long_medication_detail = await sails.models.longmedicationdetail_reader.find({induction_ref: induction_requests[index].id});
            } else {
                induction_requests[index].long_medication_detail = [];
            }
        }
    }

    sails.log.info('got induction requests, total', induction_requests.length);
    return induction_requests;
};



const isAmongTestUser = (current_email, projectId) => {
    let conf_value = (sails.config.custom.TEST_USER_EMAIL_ADDRESSES && sails.config.custom.TEST_USER_EMAIL_ADDRESSES.toString() && sails.config.custom.TEST_USER_EMAIL_ADDRESSES.toString().trim()) || '';

    let confSet = (conf_value.length ? conf_value.split(',') : []).reduce((conf, emailAndId) => {
        let [email, project_ref] = emailAndId.toString().trim().split(':');
        conf[email] = +project_ref || true;
        return conf;
    }, {});

    return (confSet[current_email] === projectId) || (confSet[current_email] === true) || false;
};

const attachDistanceMatrixDetails = async (induction, {postcode: project_postcode, id: project_ref, custom_field: {country_code, location}}, update = false) => {
    // attach default travel info DM details.
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'exclusion_by_country_code' });

    let exclusion_setting = (record && record.value) || {};
    let validatorList = {};
    validatorList.visibility = (exclusion_setting[country_code] || {}).visibility || [];
    validatorList.globalVisibility = (exclusion_setting['ALL'] || {}).visibility || [];
    let isVisibility = checkProjectPostcodeLookup(validatorList).type === 'postcode-lookup';

    project_postcode = isVisibility ? project_postcode : location.region;

    induction.travel_time = await constructDistanceMatrix(induction, project_postcode, country_code);

    let overrides = (induction.travel_time && induction.travel_time.overrides) || [];
    sails.log.info(`recalculate DM details for project(${project_ref}) ${isVisibility ? 'postcode' : 'address'} is: ${project_postcode}, overrides count: ${overrides.length}, 'isVisibility', ${isVisibility}`);

    if(project_postcode && overrides.length){
        const region = deriveDistanceMatrixRegion(country_code);
        let lookOutDistanceFor = [];
        for (let i = 0, len = overrides.length; i < len; i++) {
            let record = overrides[i];
            if(!record.travel_time.to_work_dm && !lookOutDistanceFor.includes(record.from_postcode)){
                // get DM details
                lookOutDistanceFor.push(record.from_postcode);
            }
            if(!record.travel_time.to_home_dm && !lookOutDistanceFor.includes(record.to_postcode)){
                // get DM details
                lookOutDistanceFor.push(record.to_postcode);
            }
        }

        if(lookOutDistanceFor.length){
            sails.log.info(`Lookout for DM details ${project_postcode} <=> ${lookOutDistanceFor.join(',')}`);
            let destinations = `${project_postcode} ${region}`;
            let distanceMatrixRows = await getDistanceMatrix(lookOutDistanceFor.map(s => `${s} ${region}`).join('|'), destinations, region);
            let dm_details = lookOutDistanceFor.reduce((list, origin, index) => {
                list[origin] = _get(distanceMatrixRows, `[${index}]elements[0]`, {});
                return list;
            }, {});

            sails.log.info(`All DM details ${project_postcode} <=>`, dm_details);

            for (let i = 0, len = overrides.length; i < len; i++) {
                let record = overrides[i];
                if(!record.travel_time.to_work_dm && dm_details[record.from_postcode]){
                    record.travel_time.to_work_dm = dm_details[record.from_postcode];
                }
                if(!record.travel_time.to_home_dm && dm_details[record.to_postcode]){
                    record.travel_time.to_home_dm = dm_details[record.to_postcode];
                }
            }
        }

        let updated_travel_time = {
            ...(induction.travel_time || {}),
            overrides: overrides
        };
        if (update) {
            let updated = await sails.models.inductionrequest.updateOne({
                id: induction.id
            }).set({
                travel_time: updated_travel_time
            });
        }
        induction.travel_time = updated_travel_time;
    }

    return induction;
};

const cleanInductionFields = (inductionRequest) => {
    if(inductionRequest.user_doc_ids){
        inductionRequest.user_doc_ids = (inductionRequest.user_doc_ids || [])
            .map(id => typeOf(id, 'object') ? id.id : (id && +id))
            .filter(d => (d && +d));
    }
    return inductionRequest;
};

const sendMobileNotification = async (inductionRequest, userName, profile_pic, category, comments) => {
    sails.log.info('send notification for induction to mobile devices for user ', inductionRequest.user_ref, ' for status ', category);
    let project = await sails.models.project.findOne({id: inductionRequest.project_ref});
    let m_text;
    let title;
    let comment = null;
    if(category === NOTIFICATION_CATEGORY.INDUCTION_APPROVED) {
        title = `${project.custom_field.induction_phrase_singlr} Approved`;
        m_text = userName + ` has approved your ${project.custom_field.induction_phrase_singlr} on ` + project.name + ' project.';
        if(comments.length > 1 && project.is_passport_require) {
            comment = comments[comments.length-2].note;
        } else if(comments.length > 0 && !project.is_passport_require) {
            comment = comments[comments.length-1].note;
        }
    } else if(category === NOTIFICATION_CATEGORY.INDUCTION_REJECTED) {
        title = `${project.custom_field.induction_phrase_singlr} Rejected`;
        m_text = userName + ` has rejected your ${project.custom_field.induction_phrase_singlr} on ` + project.name + ' project.';
        if(comments.length) {
            comment = comments[comments.length-1].note;
            m_text = m_text + ' Comment: ' + comment;
        }
    } else if(category === NOTIFICATION_CATEGORY.INDUCTION_CHANGE_REQUESTED) {
        title = `${project.custom_field.induction_phrase_singlr} Request Change`;
        m_text = userName + ` has requested that you make changes to your ${project.custom_field.induction_phrase_singlr} submitted to ` + project.name + ' project.';
        if(comments.length) {
            comment = comments[comments.length-1].note;
            m_text = m_text + ' Comment: ' + comment;
        }
    }
    let notificationData = {
        user_ref: inductionRequest.user_ref,
        category: category,
        message: m_text,
        title: title,
        data: {
            category: category,
            project_ref: inductionRequest.project_ref,
            profile_pic: profile_pic,
            inductionId: inductionRequest.id,
            comment: comment
        },
    };
    let notification = await sails.models.usernotifications.create(notificationData);
    let deviceInfo = await sails.models.registereddevices.find({user_ref: inductionRequest.user_ref});
    sails.log.info('No of registered devices found: ', deviceInfo.length);
    let message = {};
    //sending the notification now to mobile devices
    for(const device of deviceInfo) {
        message = {
            notification: {
                title: title,
                body: m_text
            },
            android: {
                notification: {
                    icon: 'ic_icon_inndex',
                    color: '#14152D'
                }
            },
            data: {
                category: category,
                project_ref: inductionRequest.project_ref.toString(),
                notification_ref: notification.id.toString(),
                profile_pic: profile_pic || '',
                inductionId: inductionRequest.id.toString(),
                comment: comment || '',
            },
            token: device.token
        };
        await NotificationService.sendNotification(message);
    }

};

/**
 * Move induction requests b/w two states
 *
 * @param from_state
 * @param to_status_code
 * @param contractorName
 * @param user_ref
 * @param comment
 * @returns {Promise<boolean>}
 */
const updateAllInheritedInductionState = async (from_state, to_status_code, contractorName, user_ref, comment) => {
    // instead of below logic, should use companyFn.getUserInductionsIntoCompany
    let inherited_projects = await sails.models.project.find({where: {contractor: contractorName}, select: ['name', 'contractor']});
    let target_inductions = await sails.models.inductionrequest.find({
        select: ['project_ref', 'user_ref', 'status_code', 'record_id', 'comments'],
        where: {
            status_code: from_state,
            user_ref: user_ref,
            project_ref: inherited_projects.map(p => p.id)
        },
    });
    sails.log.info(`Moving inherited inductions from projects: ${inherited_projects.map(i => i.id)} ids: ${target_inductions.map(i => i.id)}`);
    for (let i = 0; i < target_inductions.length; i++) {
        await sails.models.inductionrequest.updateOne({
            id: target_inductions[i].id
        }).set({
            comments: [
                ...(target_inductions[i].comments || []),
                comment
            ],
            status_code: to_status_code
        });
    }
    return true;
};


const getLastMatchingComment = (comments, condition) => {
    for (let i = comments.length - 1; i >= 0; i--) {
        if (condition(comments[i])) {
            return comments[i];
        }
    }
    return null;
}

const processViewAndDownloadInductionRequests =  async (req, res, induction_request, type, target_date, embedRequest, {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts}) => {
    let additional_data = {};
    let form_template = `pages/induction-form-page`;
    sails.log.info('got induction request', induction_request.id, ` caller: ${req.user?.id} project: ${induction_request.project_ref} target_date:`, target_date.format());


    sails.log.info('project_logo_file', project_logo_file && project_logo_file.file_url);
    if(induction_request.additional_data){
        additional_data = induction_request.additional_data;
    }

    let country_of_work = (additional_data.user_info && additional_data.user_info.country_code);
    let validatorList = {};
    if(exclusion_setting && country_of_work) {
        validatorList.activeExclusions = (exclusion_setting[country_of_work] || {}).exclude || [];
        validatorList.globalExclusions = (exclusion_setting['ALL'] || {}).exclude || [];
        validatorList.activeMandatoryItems = (exclusion_setting[country_of_work] || {}).mandatory || [];
        validatorList.globalMandatoryItems = (exclusion_setting['ALL'] || {}).mandatory || [];
        sails.log.info('induction rendering will use exclusions', validatorList.activeExclusions, validatorList.globalExclusions);
        sails.log.info('induction rendering will use mandatory', validatorList.activeMandatoryItems, validatorList.globalMandatoryItems);
    }
    let {enabled} = getPendingAssessmentStatus({}, contractor.features_status);
    let cscs_info = {};
    // get user docs
    if(induction_request.user_doc_ids && induction_request.user_doc_ids.length){
        let saved_docs = (induction_request.additional_data && induction_request.additional_data.user_docs) || [];
        sails.log.info(`reusing existing saved doc copy from induction`);
        let user_docs = saved_docs.filter(d => d && !d.parent_doc_ref).map(doc => {
            doc.children = (doc.children || []).filter(c => (c.is_deleted_manually !== 1));
            return doc;
        });

        if (cscs_check_enabled) {
            let verifiable_user_documents = await getUserVerifiableUserDocuments(user_docs, induction_request.user_ref, country_of_work);
            verifiable_user_documents = (verifiable_user_documents || []).map(m => m.name);
            sails.log.info('verifiable_user_documents', verifiable_user_documents);
            user_docs = user_docs.map(d => {
                d._is_verifiable = verifiable_user_documents.includes(d.name);
                return d;
            });
        }
        let cscs_doc = user_docs.find(d => (d.name) && (d.name).toString().trim().toLowerCase() === 'cscs') || {};
        cscs_info = cscs_doc.verification_info || {};
        induction_request.document_pages = await translateUserDocIntoPages(user_docs);
    }
    sails.log.info(`User: ${induction_request.user_ref} cscs info`, cscs_info);

    if(additional_data.project && additional_data.project.template_identifier){
        let template = await sails.models.formtemplate.findOne({identifier: additional_data.project.template_identifier});

        if(template && template.template_path){
            form_template = template.template_path;
        }else{
            sails.log.error('Using fallback template', template);
        }
    }
    sails.log.info(`using template path: "${form_template}"`);

    let hide_medication_block = false;
    if (medication_disabled_projects && medication_disabled_projects.value && medication_disabled_projects.value.length) {
        sails.log.info('hide medications details for projects:', medication_disabled_projects.value);
        hide_medication_block = medication_disabled_projects.value.includes(+induction_request.project_ref);
    }
    let show_side_effects_block = false;
    if(induction_request.on_long_medication && induction_request.on_long_medication.toString().toLowerCase().trim() === 'yes'){
        show_side_effects_block = true;
    }
    let hide_site_health_block = (inductionHealthAssessmentExcluded(validatorList));
    if(!induction_request.reportable_medical_conditions && !induction_request.on_long_medication && !(induction_request.any_side_effects && induction_request.any_side_effects.toString().toLowerCase().trim() === 'yes')) {
        hide_site_health_block = true;
    }
    if(contractor.features_status && contractor.features_status.induction_health_q !== undefined){
        hide_site_health_block = !contractor.features_status.induction_health_q;
    }

    let assessment_questions = [];
    // extracting question reference out of answers, to save DB call
    additional_data.health_assessment_answers = (additional_data.health_assessment_answers || []).map(record => {
        if(record.question_ref && record.question_ref.id){
            assessment_questions.push(record.question_ref);
            record.question_ref = record.question_ref.id;
        }
        return record;
    });

    sails.log.info('got user health_assessment_questions, total', assessment_questions.length);
    let assessment_categories = _.groupBy(assessment_questions || [], (i) => i.category);

    // More than 4 tabs can make pdf rendering slow. it is one of known issue
    // https://www.npmjs.com/package/html-minifier

    let travelTimeOverride = getActiveTravelTime(induction_request, target_date);
    // sails.log.info('got induction request', induction_request);
    let is_shadow_user = isShadowUser((additional_data.user_info || {}));

    /*sails.log.info('Data is: ', JSON.stringify({
        title: `Induction Form`,
        user: additional_data.user_info,
        contact_detail: additional_data.contact_detail,
        employment_detail: additional_data.employment_detail,
        project: additional_data.project,
        inductor: induction_request.inductor_ref,
        induction_request,
        has_medications: (induction_request.on_long_medication && induction_request.on_long_medication.toString().toLowerCase().trim() === 'yes'),
        project_logo_file,
        user_docs: induction_request.user_docs,
        declarations: _.get(additional_data, 'project.declarations', []),

        health_assessment_answers: additional_data.health_assessment_answers,
        assessment_categories,
        categories_names: Object.keys(assessment_categories),
    }));*/
    let showProfileMedicalAssessment = !(profileMedAssessmentExcluded(validatorList)) && enabled.medical_answers_required;
    let showProfileHealthAssessment = !(profileHealthAssessmentExcluded(validatorList)) && enabled.health_answers_required;
    let p2_not_needed = (
        (is_shadow_user && (!(additional_data.health_assessment_answers || []).length) && (!(additional_data.medical_assessments_answers || []).length) && hide_site_health_block) ||
        (!showProfileMedicalAssessment && !showProfileHealthAssessment && hide_site_health_block)
    );

    let ir_comments = induction_request.comments || [];
    let lastStatusChangeComment = (getLastMatchingComment(ir_comments,(comment) => comment.module && !['pr-out-log', 'pr-in-log', 'member', 'travel-time'].includes(comment.module)));

    let html = await sails.renderView(form_template, {
        title: `${additional_data.project.custom_field.induction_phrase_singlr} Form`,
        user: additional_data.user_info || {},
        is_shadow_user,
        local_worker_status: (projectDistricts.length && induction_request.district && projectDistricts.includes(induction_request.district)) ? 'Yes' : 'No',
        contact_detail: additional_data.contact_detail || {},
        employment_detail: additional_data.employment_detail || {},
        travel_time_override: travelTimeOverride,
        project: additional_data.project || {},
        inductor: induction_request.inductor_ref || additional_data.inductor_data || null,
        accepted_at: (ir_comments.length && lastStatusChangeComment && lastStatusChangeComment.timestamp) ? (lastStatusChangeComment.timestamp)  : ( additional_data.accepted_at || null ),
        cscs_info,
        induction_request,
        hide_medication_block,
        show_side_effects_block,
        hide_site_health_block,
        has_medications: (induction_request.on_long_medication && induction_request.on_long_medication.toString().toLowerCase().trim() === 'yes'),
        project_logo_file,
        // user_docs: induction_request.user_docs || [],
        document_pages: induction_request.document_pages || [],
        declarations: _.get(additional_data, 'project.declarations', []),
        status_message: buildInductionStatusMessage(induction_request.status_code),
        health_assessment_answers: additional_data.health_assessment_answers,
        has_health_assessment_answers: !!(additional_data.health_assessment_answers || []).length,
        medical_assessments_answers: additional_data.medical_assessments_answers || [],
        assessment_categories,
        categories_names: Object.keys(assessment_categories),
        p2_not_needed,

        moment: moment,
        momentTz,
        timezone,
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm:ss',
        dateTimeFormat: 'DD/MM/YYYY HH:mm:ss',
        type,
        embedRequest,
        numberToYesNo: (num) => {
            if(+num === 1){
                return 'Yes';
            }else if(+num === 0){
                return 'No';
            }
            else if(num === null){
                return '-';
            }
            return num;
        },
        createTime: (iso_string) => {
            if(iso_string){
                let d = moment.duration(iso_string);
                return moment.isDuration(d) ? d.asMinutes() + ' minutes' : null;
            }
            return iso_string;
        },
        totalTravelTime: (travel_time = {}, defaultValue = '-') => {
            let d = moment.duration();
            if(travel_time.to_work){
                let to_work = moment.duration(travel_time.to_work);
                d = moment.isDuration(to_work) ? d.add(to_work) : d;
            }
            if(travel_time.to_home){
                let to_home = moment.duration(travel_time.to_home);
                d = moment.isDuration(to_home) ? d.add(to_home) : d;
            }
            let str = [];
            let h = d.hours();
            let m = d.minutes();
            if(h){
                str.push(`${h} hr`);
            }
            if(m){
                str.push(`${m} minutes`);
            }
            return str.length ? str.join(' ') : defaultValue;
        },
        createEmploymentTime: (epoch_ms) => {
            if(epoch_ms && !isNaN(+epoch_ms)){
                let m = moment(+epoch_ms);
                let d = moment.duration(moment().diff(m));
                return moment.isDuration(d) ? (
                    (d.years() ? (d.years() > 1) ? d.years() + ' years' : d.years() + ' year' : '')
                    + ' '
                    + (d.months() ? (d.months() > 1) ? d.months() + ' months' : d.months() + ' month' : '')
                    + ' '
                    + (!d.years() ? (d.days() ? ((d.days() > 1) ? d.days() + ' days' : d.days() + ' day') : ''): '')
                ) : null;
            }
            return epoch_ms;
        },
        findIdInObj: (list, id, indentifier = 'id') => {
            let o = list.find(o => o[indentifier] === id);
            return o ? o : {};
        },
        sortBy: (list, key = 'timestamp' ) => {
            return (list || []).sort((a, b) => {
                return b[key] - a[key];
            });
        },
        sortCodeMask: (number) => {
            if (number) {
                let str = number.toString();
                let numberArr = str.match(/.{1,2}/g);
                return numberArr.join('-');
            }
        },
        displayEmploymentCompany: (employment_detail) => {
            return (employment_detail && employment_detail.employment_company) ? `(${employment_detail.employment_company})` : '';
        },
        canShowUnder18Badge: (user) => {
            return (user.dob) ? dayjs().diff(user.dob, 'year') < 18 : false;
        },
        correctDateFormat: (date) => {
            if(!date) return '';
            return (dayjs(date, 'YYYY-MM-DD').isValid()) ? dayjs(date, 'YYYY-MM-DD').format('DD-MM-YYYY') : date;
        },
        showProfileMedicalAssessment,
        showProfileHealthAssessment,
        showNIN: !(profileEmplymentNinExcluded(validatorList)),
        showMinWage: !(profileEmplymentMinWageExcluded(validatorList)),
        showEmpNbr: !(isProfileEmploymentEmpNbrMandatory(validatorList)),
        showTypeOfEmployment: !(profileEmplymentTypeExcluded(validatorList)),
        showEmploymentStartDate: !(profileEmplymentStartDateExcluded(validatorList)),
        layout: false
    });
    if(type === 'html'){
        sails.log.info('Rendering html view');
        return html;
    }

    let file_name = `${additional_data.project.custom_field.induction_phrase_singlr}-Request-Report-${induction_request.record_id}`;
    if(induction_request.additional_data) {
        let name = [];
        if (induction_request.additional_data.project && induction_request.additional_data.project.name) {
            name.push(induction_request.additional_data.project.name);
        }
        if (induction_request.record_id) {
            name.push(induction_request.record_id);
        }
        if (induction_request.additional_data.user_info) {
            let full_name = getUserFullName(induction_request.additional_data.user_info);
            name.push(encodeURI(full_name.replace(/ /g, '-')));
        }
        if (induction_request.additional_data.employment_detail) {
            name.push(induction_request.additional_data.employment_detail.employer);
        }
        file_name = name.join(' - ');
    }
    let project_line = `${additional_data.project.project_number != null ? `${additional_data.project.project_number} - ${additional_data.project.name}` : `${additional_data.project.name}`} (#${additional_data.project.id}): ${additional_data.project.contractor}`;
    let date_line = `Record No. ${induction_request.record_id}`;

    return await downloadPdfViaGenerator({
        req,
        res,
        html,
        tool: 'induction-request',
        file_name,
        heading_line: `${additional_data.project.custom_field.induction_phrase_singlr} Form`,
        project_line,
        date_line,
        logo_file: project_logo_file,
        has_cover: false,
        has_one_page: true,
        responseType: 'pdf',
    });
}

const updateTravelTimeOverride = async (req, validate_induction_owner = false, user_ref = null) => {
    let {validationError, payload: override_details} = InductionRequestValidator.overrideTravelTime(req);
    if(validationError){
        return ResponseService.errorObject('All fields required.', {validationError});
    }
    let inductionRequestId = req.param('inductionRequestId', 0);
    let where_clause = {
        id: inductionRequestId,
        ...(validate_induction_owner ? {user_ref} : {}),
    };
    sails.log.info('Store travel time overrides, inductionRequestId:', inductionRequestId, 'where_clause', where_clause);
    let induction_request = await sails.models.inductionrequest_reader.findOne({
        select: ['project_ref', 'user_ref', 'travel_time', 'travel_method', 'status_code', 'comments'],
        where: where_clause
    });
    if(!induction_request || !induction_request.id){
        return ResponseService.errorObject('induction record not found');
    }
    let project = await sails.models.project_reader.findOne({where: {id: induction_request.project_ref}, select: ['name', 'postcode', 'custom_field']});
    sails.log.info(`Project(${project.id}) postcode is: ${project.postcode}`);
    const country_code = (project.custom_field && project.custom_field.country_code);

    // recalculate distance details
    // sails.log.info('Data is', override_details);
    if(project.postcode && override_details.overrides && override_details.overrides.length){
        let lookOutDistanceFor = [];
        const region = deriveDistanceMatrixRegion(country_code);
        for (let i = 0, len = override_details.overrides.length; i < len; i++) {
            let record = override_details.overrides[i];
            if(!record.travel_time.to_work_dm && !lookOutDistanceFor.includes(record.from_postcode)){
                // get DM details
                lookOutDistanceFor.push(record.from_postcode);
            }
            if(!record.travel_time.to_home_dm && !lookOutDistanceFor.includes(record.to_postcode)){
                // get DM details
                lookOutDistanceFor.push(record.to_postcode);
            }
        }

        if(lookOutDistanceFor.length){
            sails.log.info(`Lookout for DM details ${project.postcode} <=> ${lookOutDistanceFor.join(',')}`);
            let destinations = `${project.postcode} ${region}`;
            let distanceMatrixRows = await getDistanceMatrix(lookOutDistanceFor.map(s => `${s} ${region}`).join('|'), destinations, region);
            let dm_details = lookOutDistanceFor.reduce((list, origin, index) => {
                list[origin] = _get(distanceMatrixRows, `[${index}]elements[0]`, {});
                return list;
            }, {});

            sails.log.info(`All DM details ${project.postcode} <=>`, dm_details);

            for (let i = 0, len = override_details.overrides.length; i < len; i++) {
                let record = override_details.overrides[i];
                if(!record.travel_time.to_work_dm && dm_details[record.from_postcode]){
                    record.travel_time.to_work_dm = dm_details[record.from_postcode];
                }
                if(!record.travel_time.to_home_dm && dm_details[record.to_postcode]){
                    record.travel_time.to_home_dm = dm_details[record.to_postcode];
                }
            }
        }
    }
    let updated_travel_time = {
        ...(induction_request.travel_time || {}),
        overrides: override_details.overrides
    };
    let updated_comments = [
        ...(induction_request.comments || []),
        override_details.comment
    ];
    return await sails.models.inductionrequest.updateOne({id: inductionRequestId})
        .set({
            comments: updated_comments,
            travel_time: updated_travel_time
        });
};


const attachLastOnsiteOfUsers = async (projectId, records, inductedUsers) => {
    if(!records.length){
        return records;
    }
    let startingNoOfEscaped = 1;
    let inClause = inductedUsers.map(() => {
        startingNoOfEscaped++;
        return `$${startingNoOfEscaped}`;
    }).join(',');
    let timeLogsRawResult = await sails.sendNativeQuery(`SELECT id, first_in, last_out, user_ref
                                                                 FROM user_daily_log
                                                                 WHERE id IN (
                                                                     SELECT MAX(id)
                                                                     FROM user_daily_log
                                                                     WHERE user_ref IN (${inClause})
                                                                       AND project_ref = $1
                                                                     GROUP BY user_ref
                                                                 )`, [projectId, ...inductedUsers]);
    if (HttpService.typeOf(timeLogsRawResult.rows, 'array') && timeLogsRawResult.rows.length) {
        sails.log.info(`time log result results: ${timeLogsRawResult.rows.length}`);
        return records.map(record => {
            let timeLog = (timeLogsRawResult.rows).find(row => row.user_ref === record.user_ref.id);
            record.user_last_on_site = (timeLog) ? (timeLog.last_out || timeLog.first_in) : null;
            return record;
        });
    }
    return records;
};

const getProjectUsers = async (projectId, userIds = []) => {
    let where = {
        resource: resourceIdentifier.PROJECT(projectId),
        role: ROLES.SITE_ADMIN
    };
    if(userIds.length){
        where.user_ref = userIds;
    }
    sails.log.info(`get all site-admin UAC of projectId: ${projectId} userId filter: ${userIds.length}`);
    let ur_records = await sails.models.userrole_reader.find({where, sort: ['id ASC'], select: ['role', 'resource', 'sub_resource', 'designation', 'permission', 'flags', 'user_ref']});
    return ur_records;
};

const prepareReceivedBriefingsItems = async (records, projectId, userId, recordTitle, toolKey) => {
    let items = [];
    let briefedByUserIds = [];
    let recIdList = records.map(r => r.id);
    let briefings = await sails.models.toolbriefings_reader.find({
        select: ['register', 'briefed_at', 'briefed_by', 'briefed_by_name', 'project_ref', 'tool_key', 'tool_record_ref'],
        where : {tool_key: toolKey, tool_record_ref: recIdList, project_ref: projectId}
    });
    briefings = await populateUserRefs(briefings, 'briefed_by', ['first_name', 'last_name']);

    for (let record of records) {
        let brieflist = (briefings || []).filter(b => b.tool_record_ref == record.id);
        if (brieflist && brieflist.length) {
            if (record.company_ref) {
                brieflist.map(r => {
                    if (r.project_ref === projectId && r.register && r.register.some(reg => reg.user_ref == userId)) {
                        if(r.briefed_by && r.briefed_by.id) {
                            briefedByUserIds.push(r.briefed_by.id);
                        }
                        let briefed_by_nm = (r.briefed_by && r.briefed_by.first_name) ? getUserFullName(r.briefed_by) : r.briefed_by_name;
                        items.push({
                            briefing_title: record[recordTitle],
                            briefed_at_formatted: (r.briefed_at) ? dayjs(+r.briefed_at).format('DD/MMM/YYYY HH:mm:ss') : '',
                            briefed_by: briefed_by_nm,
                            briefed_by_user_ref: (r.briefed_by && r.briefed_by.id) ? (r.briefed_by.id) : 0,
                            briefed_at: (r.briefed_at) ? r.briefed_at : '',
                        });
                    }
                    return r;
                });
            } else if (record.project_ref) {
                brieflist.map(r => {
                    if (r.register && r.register.some(reg => reg.user_ref === userId)) {
                        if(r.briefed_by && r.briefed_by.id) {
                            briefedByUserIds.push(r.briefed_by.id);
                        }
                        let briefed_by_nm = (r.briefed_by && r.briefed_by.first_name) ? getUserFullName(r.briefed_by) : r.briefed_by_name;
                        items.push({
                            briefing_title: record[recordTitle],
                            briefed_at_formatted: (r.briefed_at) ? dayjs(+r.briefed_at).format('DD/MMM/YYYY HH:mm:ss') : '',
                            briefed_by: briefed_by_nm,
                            briefed_by_user_ref: (r.briefed_by && r.briefed_by.id) ? (r.briefed_by.id) : 0,
                            briefed_at: (r.briefed_at) ? r.briefed_at : '',
                        });
                    }
                    return r;
                });
            }
        }
    }

    items.sort((a,b) => (a.briefed_at < b.briefed_at) ? 1 : ((b.briefed_at < a.briefed_at) ? -1 : 0));
    return { items, briefedByUserIds };
};

const associateUserToBriefingItems = (items, userInfo) => {
    items = (items || []).map(item => {
        let user = userInfo.find(user => user.id == item.briefed_by_user_ref);
        item.briefed_by = (user) ? getUserFullName(user) : item.briefed_by;
        delete item.briefed_by_user_ref;
        return item;
    });

    return items;
};

const checkIfUserIsCompanyBlocked = async (user, contractor) => {
    // Checking if user is Blacklisted for given Company
    sails.log.info('Checking company block status', contractor);
    let projects_of_same_contractor = await sails.models.project.find({
        select: ['id'],
        where: {
            disabled_on: null,
            is_active: 1,
            project_category: 'default', // Only standard project will be searchable
            contractor: contractor,
        }
    });

    let blacklisted_inductions = await sails.models.inductionrequest_reader.find({
        where: {project_ref: projects_of_same_contractor.map(p => p.id), user_ref: user.id, status_code: 5},
        select: ['id', 'record_id', 'status_code', 'project_ref']
    });
    if(blacklisted_inductions.length){
        sails.log.info('User got blacklisted inductions', blacklisted_inductions);
        return blacklisted_inductions;

    }
    return false;
};

const getMatchingInductionRecordsMessage = async (projectId, irId, frErrorResult) => {
    let {alreadyExists, userId} = frErrorResult || {};
    if(alreadyExists && userId){
        let records = await sails.models.inductionrequest_reader.find({
            where: {
                project_ref: projectId,
                user_ref: userId,
                fr_face_id: {'!=': null},
                id: {'!=': irId}
            },
            select: ['id', 'record_id', 'status_code', 'creator_name', 'user_ref'],
            sort: ['id DESC'],
        });
        let [firstRecord] = records;
        sails.log.info(`[FR] Total matching inductions of project:${projectId} user:${userId} count: ${records.length}`);
        if(firstRecord){
            frErrorResult.message = sails.__('facial_recognition_induction_face_already_exists_for_ir', firstRecord.record_id);
        }
    }
    return frErrorResult;
};

const notifyOnFirstInduction = async (project) => {
    let messages = [];
    messages.push(`*1st Induction on ${(project.project_initial && project.use_prefix) ? (project.project_initial.toString().trim().toUpperCase() + project.id.toString()) : project.id} : "${project.project_number} - ${project.name}"* :rocket: `);
    messages.push(`Principal Contractor: "${project.contractor}" - ${project.custom_field?.country_code} (ID: ${project.parent_company})`);
    const content = messages.join('\n');
    // sails.log.info(`Slack notification content:`, content);
    await EmailService.innDexSlackNotifier({message: content}, sails.config.custom.INNDEX_FIRST_INDUCTION_HOOK_URL);
};

const getConductCardsOfUsers = async (projectId, userIds, inductionId = 0) => {
    sails.log.info(`[getConductCardsOfUsers] projectId: ${projectId} users: ${userIds.length} active only`);
    let where = {
        project_ref: projectId,
        or     : [
            { expire_on : null }, //indefinite
            { expire_on : {'>=': dayjs().valueOf()} },
        ]
    }

    if (userIds.length) {
        where.user_ref = userIds;
    }

    if (inductionId) {
        where.induction_ref = inductionId;
    }

    let conduct_cards = await sails.models.userconductcard_reader.find({
        where,
        select: ['conduct_card_ref','card_detail', 'createdAt', 'assigned_by_ref', 'updatedAt', 'comment', 'expire_on', 'induction_ref', 'user_ref'],
    });
    conduct_cards = await populateUserRefs(conduct_cards, 'assigned_by_ref', ['id', 'first_name', 'last_name']);
    sails.log.info(`[getConductCardsOfUsers] projectId: ${projectId} records: ${conduct_cards.length}`);
    return conduct_cards;
};

const projectInductionsList = async (req, res, project_ref = null) => {
    let projectId = project_ref || +req.param('projectId', 0);
    let pageSize = +req.param('pageSize', 50);
    if(req.project){
        pageSize = 5000;
    }
    let pageNumber = +req.param('pageNumber', 0);
    let sortKey = req.param('sortKey', 'id');
    let sortDir = req.param('sortDir', 'asc');
    let searchTerm = (req.param('q', '')).toString().trim();
    let statusCodes = (req.param('statusCodes', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
    let employer = req.param('employer') ? decodeURIParam((req.param('employer', '')).toString().trim()).split(",") : [];
    let approvedCount = req.param('approvedCount', 'false') === 'true';
    let extraColumns = req.param('extra', 'false') === 'true';

    let additionalCols = [
        `additional_data->'user_info'->>'first_name' as first_name`,
        `additional_data->'user_info'->>'middle_name' as middle_name`,
        `additional_data->'user_info'->>'last_name' as last_name`,
        `additional_data->'user_info'->'profile_pic_ref' as profile_pic_ref`,
        `additional_data->'employment_detail'->>'employer' as employer`,
        `comments`,
        `optima_badge_number::numeric`
    ];
    if(extraColumns){
        additionalCols.push(...[
            `reportable_medical_conditions`,
            `on_long_medication`,
            `induction_slot`,
            `recent_changes`,
            `fr_face_id`,
            `rtw_doc_code`,
            `rtw_check_result`,
            `additional_data`
        ]);
    }
    if(searchTerm){
        searchTerm = decodeURIParam(searchTerm);
    }
    let {
        total: totalCount,
        records
    } = await getProjectInductionsPage(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {searchTerm, statusCodes, employer}, additionalCols);

    if (records.length) {
        if(extraColumns){
            let userIds = records.reduce((list, r) => {
                if(!list.includes(r.user_ref)){
                    list.push(r.user_ref);
                }
                return list;
            }, []);
            let all_conduct_cards = await getConductCardsOfUsers(projectId, userIds);
            let all_uac_records = await getProjectUsers(projectId, userIds);
            records = records.map(r => {
                r.status_message = buildInductionStatusMessage(r.status_code);
                r.conduct_cards = all_conduct_cards.filter(cc => cc.induction_ref === r.id);
                r.uac = all_uac_records.find(ue => ue.user_ref === r.user_ref);
                return r;
            });
        }else{
            records = records.map(r => {
                r.status_message = buildInductionStatusMessage(r.status_code);
                return r;
            });
        }

    }
    let total_approved_count = undefined;
    if(approvedCount){
        sails.log.info(`fetching approved inductions count, projectId: ${projectId}`);
        total_approved_count = await sails.models.inductionrequest.count({
            where: {
                project_ref: projectId,
                status_code: STATUS_CODES.APPROVED
            }
        });
    }
    return ResponseService.successResponse(res, {
        records,
        q: searchTerm,
        statusCodes,
        pageSize,
        pageNumber,
        sortKey,
        sortDir,
        totalCount,
        ...(approvedCount ? {total_approved_count} : {}),
    });
};

const getDailyLogsForTimesheets = async (projectId, {from_date, to_date}, user_ids = [], extraColumns = false) => {

    let from_date_str = from_date.format(dbDateFormat_YYYY_MM_DD);
    let to_date_str = to_date.add(1, 'days').format(dbDateFormat_YYYY_MM_DD);
    sails.log.info(`get daily time logs for project: ${projectId}, from, to: ${from_date_str}  <= date < ${to_date_str}, filter.userIds: ${user_ids}`);
    let daily_logs = await getDailyTimeEventV2(projectId, from_date_str, to_date_str, user_ids);
    let userIds = [...new Set(daily_logs.map(l => l.user_id))];
    sails.log.info(`Total daily logs for project: ${projectId}, count ${daily_logs.length}, users: ${userIds.length}`);

    if(!userIds.length){
        return {
            projectId,
            daily_logs,
            userIds,
            timesheets: [],
            from_date_str,
            to_date_str,
        }
    }
    sails.log.info(`get timesheet rows for project: ${projectId}, from, to: ${from_date_str}  <= date < ${to_date_str}, users ${userIds}`);

    const week_end_date = to_date.format(dbDateFormat_YYYY_MM_DD);
    const timesheetWeek = await sails.models.projectweeklytimesheet_reader.find({
        where: {
            project_ref: projectId,
            user_ref: userIds,
            week_end_date: week_end_date,
        }
    })
    sails.log.info(`Got timesheet weeks:- ${timesheetWeek.length}`);
    let timesheets = [];
    let weekly_timesheets = [];
    if(timesheetWeek.length){
        const timesheetWeekIds = timesheetWeek.map((ele) => ele.id);
        sails.log.info('Got timesheet references', timesheetWeekIds);

        timesheets = await sails.models.projecttimesheet_reader.find({
            where: {
                project_ref: projectId,
                user_ref: userIds,
                weekly_timesheet_ref: timesheetWeekIds
            },
            select: [
                'id', 'day_of_yr', 'user_ref', 'status',
                'actual_seconds', 'day_seconds', 'night_seconds',
                'hours_state',
                'weekly_timesheet_ref',
                ...(extraColumns ? [
                    'travel_seconds', 'overtime_seconds', 'training_seconds', 'manager_auth_seconds', 'price_work_amount',
                    'comments',
                    // 'change_logs'
                ] : [])
            ]
        });

        const groupedTimesheets = timesheets.reduce((acc, ts) => {
            if (!acc[ts.weekly_timesheet_ref]) {
                acc[ts.weekly_timesheet_ref] = [];

            }
            acc[ts.weekly_timesheet_ref].push(ts);
            return acc;
        }, {});

        weekly_timesheets = timesheetWeek.map(week => ({
            ...week,
            timesheets: groupedTimesheets[week.id] || []
        }));
    }

    sails.log.info(`Total timesheet rows for project: ${projectId}, from, to: ${from_date_str}  <= date < ${to_date_str}, count ${timesheets.length}`);
    return {
        projectId,
        daily_logs,
        userIds,
        timesheets,
        from_date_str,
        to_date_str,
        weekly_timesheets
    }
}

// It should help in replacing calls for:
// resource planner
// time entry provider
// download records
// ProjectController.getProjectInductedUsers
const getAllInductedUsers = async (req, res) => {
    let projectId = +req.param('projectId', 0);
    let isDeliveryManagerOnly = ((req.project_uac?.designation) || '') === 'delivery_management';
    let extraColumns = req.param('extra', '').split(',');
    let additionalCols = [
        // `record_id::int`,
        // `"createdAt"`,
        `status_code`,
    ];
    if (extraColumns.includes('pic')) {
        additionalCols.push(...[`additional_data -> 'user_info' -> 'profile_pic_ref'   as profile_pic_ref`]);
    }
    if (extraColumns.includes('email')) {
        additionalCols.push(...[`additional_data -> 'user_info' ->> 'email'   as email`]);
    }
    if (extraColumns.includes('travel')) {
        additionalCols.push(...['travel_time', 'travel_method']);
    }
    if (extraColumns.includes('employment')) {
        additionalCols.push(...[
            `additional_data -> 'employment_detail' ->> 'employer' as employer`,
            `additional_data -> 'employment_detail' ->> 'job_role' as job_role`
        ]);
    }
    if (extraColumns.includes('employment-type')) {
        additionalCols.push(...[
            `additional_data -> 'employment_detail' ->> 'type_of_employment' as type_of_employment`,
        ]);
    }
    if (extraColumns.includes('names')) {
        additionalCols.push(...[
            `additional_data -> 'user_info' ->> 'first_name'       as first_name`,
            `additional_data -> 'user_info' ->> 'middle_name'      as middle_name`,
            `additional_data -> 'user_info' ->> 'last_name'        as last_name`,
        ]);
    }

    let defaultStatusCodes = '2,6';
    let statusCodes = (req.param('statusCodes', defaultStatusCodes)).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
    let employer;

    // Fetching user's employer if user is only deliveryManager
    if (isDeliveryManagerOnly) {
        sails.log.info(`[getAllInductedUsers] Fetching user's employer for projectId: ${projectId}, userId: ${req.user.id}`);
        let [userInfo] = await getInductionEmployerByUserIds([req.user.id], projectId, Object.values(STATUS_CODES));
        if (userInfo && userInfo.user_employer) {
            employer = userInfo.user_employer;
        } else {
            sails.log.info(`[getAllInductedUsers] No employer found for projectId: ${projectId}, userId: ${req.user.id}`);
            return ResponseService.successResponse(res, {
                records: [],
                statusCodes,
            });
        }
    }

    // Fetch inductions if user is more then deliveryManager or user is deliveryManager and having employer
    sails.log.info(`[getAllInductedUsers] projectId: ${projectId}, filter.status: ${statusCodes || '-'}, extra: ${extraColumns} employer: ${employer}`);
    let { records } = await getProjectInductions(projectId, {
        statusCodes,
        limit: -1,
        employer: employer
    }, additionalCols);

    return ResponseService.successResponse(res, {
        records,
        statusCodes,
    });
};

const getInductionEmployers = async (req, res) => {
    let projectId = +req.param('projectId', 0);
    let defaultStatusCodes = ''; // Allow all by default
    let statusCodes = (req.param('statusCodes', defaultStatusCodes)).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
    let inducted_employers = await getInductionUserEmployers(projectId, {statusCodes}, false);
    return ResponseService.successResponse(res, {
        projectId,
        statusCodes,
        inducted_employers,
    });
};

const updateRamsRegister = async (ramsRegister, userId, projectId) => {
    sails.log.info(`Updating RAMS ${ramsRegister.rams_id} to add induction register on project ${projectId}.`);

    let startDay = moment().startOf('day').valueOf();
    let endDay = moment().endOf('day').valueOf();

    //checking if any new briefing created today.
    let existingBriefing = await sails.models.toolbriefings.findOne({
        select: ['register'],
        where : {
            tool_key: 'rams',
            project_ref: projectId,
            tool_record_ref: ramsRegister.rams_id,
            briefed_by_name: `${ramsRegister.induction_phrase} Briefing`,
            briefed_at: {'>=': startDay, '<=': endDay}
        }
    });

    if(existingBriefing) { //@TODO :: Additional logs to be removed after some time.
        sails.log.info(`Updating existing RAMS briefing ${existingBriefing.id} with new register/sign.`);
        await sails.models.toolbriefings.updateOne({id: existingBriefing.id}).set({
            register: [...existingBriefing.register, {"user_ref": userId}]
        });
        sails.log.info("Briefing updated, registering signature.");
        await sails.models.usersignature.create({"sign": ramsRegister.signature, "briefing_ref": existingBriefing.id, "user_ref": userId});
    } else {
        let briefing = {
            "tool_key": "rams",
            "briefed_by": null,
            "project_ref": projectId,
            "briefed_at": moment().valueOf(),
            "register": [{ "user_ref": userId }],
            "tool_record_ref": ramsRegister.rams_id,
            "briefed_by_name": `${ramsRegister.induction_phrase} Briefing`,
        };
        sails.log.info("Creating a new RAMS briefing.");
        let newBriefing = await sails.models.toolbriefings.create(briefing);
        sails.log.info("Briefing created, registering signature.");
        await sails.models.usersignature.create({"sign": ramsRegister.signature, "briefing_ref": newBriefing.id, "user_ref": userId});
    }

    sails.log.info(`RAMS record ${ramsRegister.rams_id} updated successfully.`);
    return;
};

const getHeatMapData = async (req, res) => {
    let projectId = +req.param("projectId", 0);
    const isLivePage = (req.body.isLive ? req.body.isLive : false)
    const liveUserIds = ((req.body.liveUserIds && req.body.liveUserIds.length) ? req.body.liveUserIds : [])
    let variables = [projectId];
    if(isLivePage){
        variables.push(...liveUserIds);
    }
    let variableIndex = 1;
    const sql = `Select
                id,
                travel_time-> 'coordinate' as coordinate,travel_time->'distance_matrix'->'distance'->>'value' as distance from public.induction_Request
                where
                project_ref = $1
                and status_code in(2,6)
                ${isLivePage ? ` AND user_ref IN (${(liveUserIds || []).map(() =>`$${++variableIndex}`)})` : ''}`

    const induction_requests =(isLivePage && !liveUserIds.length) ? [] : await runReadQuery(sql,variables);

    let distanceList = [];
    let coordinateList = [];
        for (let induction of induction_requests) {
            const { coordinate, distance } = induction;
            if (distance && coordinate) {
                coordinateList.push({
                    type: "Feature",
                    properties: {
                        mag: 6,
                    },
                    geometry: {
                        type: "Point",
                        coordinates: [coordinate.long, coordinate.lat],
                    },
                });

                if(!isNaN((+distance / 1069).toFixed(2))){
                    distanceList.push((+distance / 1069).toFixed(2));
                }
            }
        }

    const counts = {
        5: 0,
        10: 0,
        20: 0,
        30: 0,
        50: 0,
        100: 0,
    };

    const ranges = Object.keys(counts);
    for (let i = 0;i<distanceList.length;i++){
        let distance = distanceList[i];
        for (let val of ranges) {
            if (+distance < +val) {
                counts[val]++;
            }
        }
    }
    return ResponseService.successResponse(res, {
        coordinates: coordinateList,
        proximityList: counts,
        total:distanceList.length,
    });
};

const getDistrictByPostcode = async (project, postcode) => {
    let country_code = project.custom_field && project.custom_field.country_code;
    sails.log.info('getting district by postcode', `project country code is ${country_code}`);

    let district_name = null;
    if (country_code === 'GB') {
        //remove last 3 digit from postcode to form area code
        let areaCode = (postcode || '').substring(0, postcode.length - 3).trim();
        sails.log.info(`Area code is ${areaCode} for postcode ${postcode}.`);
        let meta_districts = await sails.models.metadistrict_reader.find({
            select: ['district_name', 'area_codes']
        });

        let matchedDistrict = meta_districts.find(item => item.area_codes.includes(areaCode));
        district_name = (matchedDistrict && matchedDistrict.district_name) || null;
    }
    sails.log.info(`district for postcode ${postcode} is ${district_name}.`);
    return district_name;
};

const prepareInductionRecordExcel = async (req, res) => {
    let projectId = (req.body.projectId || '');
    let employers_filter = (req.body.employers_filter || []);
    let timezone = (req.body.tz || 'UTC');
    let downloadBasedOn = (req.body.download_based_on || 'induction_date');
    let fromDateEpoch = moment(req.body.fromDate).startOf('day').valueOf();
    let toDateEpoch = moment(req.body.toDate).endOf('day').valueOf();
    let fromDate = moment(fromDateEpoch).format(dbDateFormat_YYYY_MM_DD);
    let toDate = moment(toDateEpoch).format(dbDateFormat_YYYY_MM_DD);

    if (!fromDateEpoch || !toDateEpoch) {
        return ResponseService.errorResponse(res, 'from date and to date are required.');
    }

    let where = {
        project_ref: projectId
    }
    let lastOnSiteUsersByProject = [];
    if (downloadBasedOn === 'last_onsite_date') {
        lastOnSiteUsersByProject = await getLastOnSiteUsersByProject(projectId, moment(fromDateEpoch).unix(), moment(toDateEpoch).unix());
        where.user_ref = lastOnSiteUsersByProject.map(item => item.user_ref);
    } else {
        where.createdAt = {'>=': fromDateEpoch, '<=': toDateEpoch}
    }

    sails.log.info(`fetch all inductions from ${fromDateEpoch} to ${toDateEpoch} and filter with companies: `, employers_filter);
    let allInductionRequest = await sails.models.inductionrequest_reader.find({
        where,
        select: ['id', 'record_id', 'user_ref', 'additional_data', 'createdAt', 'inductor_ref', 'travel_method', 'vehicle_reg_number', 'project_ref', 'induction_question_answers', 'travel_time', 'status_code', 'reportable_medical_conditions', 'on_long_medication', 'district'],
        sort: ['id DESC']
    });
    allInductionRequest = await populateUserRefs(allInductionRequest, 'inductor_ref', []);
    allInductionRequest = await populateUserRefs(allInductionRequest, 'user_ref', []);

    sails.log.info(`found ${allInductionRequest.length} induction requests.`);
    if(employers_filter.length) {
        allInductionRequest = (allInductionRequest || []).filter(induction_request => {
            if (induction_request.additional_data && induction_request.additional_data.employment_detail
                && induction_request.additional_data.employment_detail.employer) {
                return employers_filter.includes(induction_request.additional_data.employment_detail.employer);
            }
        });
        sails.log.info(`found ${allInductionRequest.length} induction requests after applying company filter.`);
    }
    let inductedUsers = [];

    for (let i = 0; i < allInductionRequest.length; i++) {
        let record = allInductionRequest[i]
        if ([2, 3, 6].includes(record.status_code) && record.user_ref && record.user_ref.id) {
            inductedUsers.push(record.user_ref.id);
        }
        allInductionRequest[i].total_working_hours = await getTotalWorkingHours(record.user_ref.id, record.project_ref, fromDate, toDate, downloadBasedOn);
        allInductionRequest[i].total_working_days = await getTotalWorkingDays(record.user_ref.id, record.project_ref, fromDate, toDate, downloadBasedOn);
    }

    if(downloadBasedOn === 'last_onsite_date') {
        allInductionRequest= allInductionRequest.map(record => {
            let timeLog = (record.user_ref && record.user_ref.id) ? (lastOnSiteUsersByProject).find(row => row.user_ref === record.user_ref.id) : {};
            record.user_last_on_site = (timeLog && timeLog.user_last_on_site) || '';
            return record;
        });
    } else if (inductedUsers.length) {
        allInductionRequest = await attachLastOnsiteOfUsers(projectId, allInductionRequest, inductedUsers);
    }
    allInductionRequest = allInductionRequest.map(r => {
        r.status_message = buildInductionStatusMessage(r.status_code);
        return r;
    });
    let project = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['id', 'name', 'custom_field']});
    let workbook = await getInductionRecordsWorkbook(allInductionRequest, project, timezone);
    let fileName = `${project.custom_field.induction_phrase} requests${project.name ? ' for ' + project.name : ''}.xlsx`;
    return streamExcelDownload(res, workbook, fileName);
};

const inductionDownloadMetaData = async (projectId) => {
    let project = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['custom_field', 'parent_company']});

    let timezone = getProjectTimezone(project);
    let record = await sails.models.inndexsetting_reader.findOne({ name: 'exclusion_by_country_code' });
    let exclusion_setting = (record && record.value) || {};

    let contractor = await sails.models.createemployer_reader.findOne({
        where: {
            id: project.parent_company,
        },
        select: ['id', 'name', 'company_initial', 'logo_file_id', 'features_status'],
    });

    let cscs_check_enabled = false;
    if (!contractor) {
        sails.log.warn(`Project contractor info not found, company: "${project.parent_company}"`);
        contractor = {};
    } else {
        let {error, id, value} = await getCompanyCSCSSettingInfo(project.parent_company);
        if (!error) {
            cscs_check_enabled = true;
        }
    }
    let project_logo_file = await sails.models.userfile_reader.findOne({
        where: {id: contractor.logo_file_id}
    });

    let medication_disabled_projects = await sails.models.inndexsetting_reader.findOne({name: "medication_disabled_projects"});

    let projectDistricts = await getProjectDistrict(projectId);

    return {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts};
};

module.exports = {

    getMyInductions: async (req, res) => {
        let extended = !!(req.param('extended') || false); // this is coming from mobile app only
        sails.log.info(`get my inductions id: ${req.user.id} extended: ${extended}`);

        let allowed_projects = [];
        let user_projects = [];
        if(extended){
            // run this only when requested to do so,
            user_projects = await TokenUtil.getAllowedDefaultProjectsOfUser(req.user.id);
            allowed_projects = user_projects.map(r => r.resource);
            sails.log.info('This user has, Allowed project ids:', JSON.stringify(allowed_projects));
        }
        let induction_requests = await sails.models.inductionrequest_reader.find({
            user_ref: req.user.id,
            status_code: {'!=': [4, 5]}
        })
            .sort([
                //{status_code: 'DESC'},
                {id: 'DESC'},
            ]);
        induction_requests = await populateProjectRefs(induction_requests, 'project_ref', [], {is_active: 1});
        induction_requests = induction_requests.filter(ir => ir.project_ref && ir.project_ref.id);
        induction_requests = await populateUserRefs(induction_requests, 'inductor_ref', []);
        sails.log.info('got induction requests, total', induction_requests.length);

        let employer_logos = [];
        // Populate project logo (logo_file_id) with contractor's logo ref, DEFAULT project doesn't need parent_company logo
        let distinct_contractors = induction_requests.reduce((ids, ir) => {
            if(ir.project_ref.parent_company && !ids.includes(ir.project_ref.parent_company)) {
                ids.push(ir.project_ref.parent_company);
            }
            return ids;
        }, []);

        if (distinct_contractors.length) {
            sails.log.info('merging logo of distinct_contractors:', JSON.stringify(distinct_contractors));
            employer_logos = await sails.models.createemployer_reader.find({
                where: {id: distinct_contractors},
                select: ['name', 'logo_file_id', 'country_code']
            });

            employer_logos = await populateUserFileRefs(employer_logos, 'logo_file_id', ['id', 'name', 'file_url', 'sm_url', 'md_url', 'file_mime']);
        }

        for (let index in induction_requests) {
            let ir = induction_requests[index];
            let projectId = ir.project_ref && ir.project_ref.id || null;
            ir.is_project_admin = (allowed_projects.includes(projectId));

            let permission = user_projects.find(up => up.resource === projectId);
            ir.project_ref._my_access_id = (permission && permission.id) || undefined;
            ir.project_ref._my_designations = (permission && permission.designation) || [];
            ir.project_ref._my_flags = (permission && permission.flags) || {};
            ir.project_ref._my_permission = (permission && permission.permission) || [];

            let logo = employer_logos.find(el => el.id === (ir.project_ref && ir.project_ref.parent_company));
            ir.project_ref.logo_file_id = logo && logo.logo_file_id;


            if(extended) {
                sails.log.info(`Include long medication detail.`);
                if (ir.on_long_medication && ir.on_long_medication == 'yes') {
                    ir.long_medication_detail = await sails.models.longmedicationdetail_reader.find({induction_ref: ir.id});
                } else {
                    ir.long_medication_detail = [];
                }
            }

            induction_requests[index] = ir;
        }

        return ResponseService.successResponse(res, {induction_requests});
    },

    searchProjectsForInduction: async (req, res) => {
        let q = (req.query.q || '').toString().trim();
        let text = (req.query.text || '').toString().trim();
        let initial = null;
        if(text && text.length){
            text = text.toUpperCase();
            initial = text.slice(0, text.search(/\d/));
            q = text.replace(initial, '');
        }

        let user_onboard_status = req.user.user_onboard_status || {};
        if (!q || !q.length) {
            return ResponseService.errorResponse(res, 'search query is requires');
        }
        // if (text.length && !initial) {
        //     return ResponseService.errorResponse(res, 'No records found for given project reference number');
        // }
        let profile_pic_missing = !((req.user.profile_pic_ref && req.user.profile_pic_ref.id) || req.user.profile_pic_ref);
        sails.log.info(`search project for induction, q: '${q}', initial: '${initial}', text: '${text}', user: ${req.user.id}, profile-pic-missing: ${profile_pic_missing}`);
        if(q.length && isNaN(+q)){
            // we want search by id only at the moment
            return ResponseService.successResponse(res, {projects: [], valid: false, ci_enabled: false, ci_pending: false,ci_expired: false, user_onboard_status});
        }
        let searchFilters = {id: q};
        if (text.length) {
            searchFilters = {id: q, project_initial: (initial ? [initial] : null)};
        }
        sails.log.info('Search filter is:', searchFilters);
        try {
            let projects = await sails.models.project.find({}).where({
                disabled_on: null,
                is_active: 1,
                project_category: 'default', // Only standard project will be searchable
                ...searchFilters,
            }).sort([
                {disabled_on: 'DESC'},
                {name: 'ASC'},
                {id: 'ASC'},
            ]).limit(1)
                .populate('created_by');
            //.populate('logo_file_id');
            // https://stackoverflow.com/a/37271269

            sails.log.info('got projects, total', projects.length);
            if(!projects.length){
                return ResponseService.successResponse(res, {projects, valid: true, ci_enabled: false, ci_pending: false,ci_expired: false, user_onboard_status});
            }
            // Since We're filtering/searching projects using ID. Only one will be returned.
            let project = projects[0];
            let isCompanyBlocked = await checkIfUserIsCompanyBlocked(req.user, project.contractor);
            if(isCompanyBlocked){
                return ResponseService.errorResponse(res,
                    `Your access has been denied. Please see a ${project.contractor} admin for any further information`,
                    // You are currently blacklisted from all ${project.contractor} projects. Please contact a ${project.contractor} representative to query or for any further actions
                    {blacklisted_inductions: isCompanyBlocked}
                );
            }

            let contractor = await sails.models.createemployer_reader.findOne({
                where: {
                    id: project.parent_company,
                },
                select: ['id', 'name', 'company_initial', 'logo_file_id', 'features_status', 'country_code'],
            }).populate('logo_file_id');
            if(!contractor){
                sails.log.info('contractor info not found');
                return ResponseService.successResponse(res, {projects: [], valid: false, ci_enabled: false, ci_pending: false,ci_expired: false, user_onboard_status});
            }

            let project_phrasing = (project.custom_field && project.custom_field.induction_phrase_singlr) || 'Induction';
            let ci_enabled = false;
            let message = undefined;

            sails.log.info(`Looking for Company settings of company: ${contractor.id}`);
            let company_settings = await sails.models.companysetting_reader.find({
                where: {
                    company_ref: contractor.id,
                    name: [
                        COMPANY_SETTING_KEY.COMPANY_INDUCTION,
                        COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG,
                        COMPANY_SETTING_KEY.RTW_CHECK_CONFIG,
                        COMPANY_SETTING_KEY.SKILL_MATRIX_EXEMPTION_CONFIG,
                    ],
                    // enabled_on: {'!=': null},
                }
            });
            if (profile_pic_missing) {
                let company_fr_setting = company_settings.find(s => s.name === COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG);
                let photo_required = !((company_fr_setting && company_fr_setting.value && company_fr_setting.value.photo_required) === false);
                if (photo_required) {
                    // photo is required, but missing
                    sails.log.info(`User doesn't profile pic, but is required by company`);
                    return ResponseService.errorResponse(res, `Before you submit your ${project_phrasing} on this project you must upload a valid profile photo first.`, {
                        projects: [],
                        user_onboard_status,
                        valid: true,
                        ci_enabled: false,
                        ci_pending: false,
                        ci_expired: false,
                        company: contractor,
                        photo_required,
                        message: `Before you submit your ${project_phrasing} on this project you must upload a valid profile photo first.`,
                        project_phrasing: project_phrasing,
                    });
                }
            }
            let company_induction_setting = company_settings.find(s => s.name === COMPANY_SETTING_KEY.COMPANY_INDUCTION);
            const rtw_setting = (company_settings.find(s => s.name === COMPANY_SETTING_KEY.RTW_CHECK_CONFIG && s.enabled_on));
            ci_enabled = company_induction_setting && company_induction_setting.value && company_induction_setting.value.status;
            if(ci_enabled){
                let company_phrasing = (company_induction_setting.value.phrasing && company_induction_setting.value.phrasing.singlr) || 'Company Induction';
                sails.log.info('Validating if user has completed Company induction');
                // IF have already Company induction which is not expired yet
                //      then allow to continue.
                // IF have NOT done Company induction OR it is expired,
                //      return empty project [], and ask to do Company Induction first.
                let available_locales = getLangFromAllSubsets(company_induction_setting.value);
                let [user_company_induction] = await sails.models.companyinduction_reader.find({
                    where: {
                        company_ref: contractor.id,
                        user_ref: req.user.id,
                        status_code: [1, 2],    // Not including rejected one
                        // as we need expiry detail on UI,
                        // so need to get expired record too
                        // expire_on: {
                        //     '>=': dayjs().valueOf()
                        // }
                    },
                    select: ['id', 'expire_on'],
                    sort: ['expire_on DESC'],
                    limit: 1
                });
                if(!user_company_induction){
                    sails.log.info(`User doesn't any Company induction`);
                    return ResponseService.successResponse(res, {
                        projects: [project],
                        user_onboard_status,
                        valid: true,
                        ci_enabled: ci_enabled,
                        ci_pending: true,
                        ci_expired: false,
                        company: contractor,
                        available_locales,
                        message: `Before you submit your ${project_phrasing} on this project you must firstly complete the ${contractor.name} ${company_phrasing}`,
                        phrasing: company_induction_setting.value.phrasing,
                        project_phrasing: project_phrasing,
                    });
                }
                else if(+(user_company_induction.expire_on) < dayjs().valueOf()){
                    sails.log.info(`Last Company induction is expired for user, id: ${user_company_induction.id}`);
                    return ResponseService.successResponse(res, {
                        projects: [project],
                        user_onboard_status,
                        valid: true,
                        ci_enabled: ci_enabled,
                        ci_pending: true,
                        ci_expired: true,
                        previous_ci: user_company_induction,
                        company: contractor,
                        available_locales,
                        message: `Your ${contractor.name} ${company_phrasing} has expired. You will need to resubmit it to carry out the site specific ${project_phrasing}`,
                        phrasing: company_induction_setting.value.phrasing,
                        project_phrasing: project_phrasing,
                    });
                }else{
                    sails.log.info(`User has non-expired Company induction, id: ${user_company_induction.id}, expire_on: ${user_company_induction.expire_on}`);
                    message = `Now you have completed the ${contractor.name} ${company_phrasing} you can proceed with your site specific ${project_phrasing} on ${project.name}`;
                }
            }

            let {ask} = getPendingAssessmentStatus(user_onboard_status, contractor.features_status);
            let pending_assessments = null;
            if(ask.health || ask.medical){
                let needed = [];
                if(ask.health){
                    needed.push(`Health`);
                }
                if(ask.medical){
                    needed.push(`Medical`);
                }
                sails.log.info(`Given project requires user to answer Health / Medical assessment details`, ask);
                message = `This project requires that you complete a preliminary ${needed.join(' & ')} assessment as part of your ${project_phrasing} on ${project.name}`;
                pending_assessments = ask;
            }

            let ids = projects.map(p => p.id) || [];
            let isTestUser = isAmongTestUser(req.user.email, project.id);
            if(!isTestUser){

                let existing_inductions = await sails.models.inductionrequest_reader.find({
                    where: {project_ref: ids, user_ref: req.user.id, status_code: {'!=': 0}},
                    select: ['id', 'status_code', 'project_ref']
                });

                if (existing_inductions && existing_inductions.length) {
                    sails.log.info('User got existing induction');
                    return ResponseService.errorResponse(res,
                        `You've already submitted an ${project.custom_field.induction_phrase_singlr} for this project. Check the 'My Projects' list to view its status`,
                        {existing_inductions}
                    );
                }
            }

            project.logo_file_id = contractor.logo_file_id;
            let competency_exception_list_ids = await getMandatoryCompetencyExceptionList(project.id, false);
            let exemption_config = company_settings.find(s => s.name === COMPANY_SETTING_KEY.SKILL_MATRIX_EXEMPTION_CONFIG) || {};
            let exempted_users = (exemption_config.value || {}).exempted_users || [];
            // Only respond with bool status
            project._user_excluded_from_mandatory_doc = competency_exception_list_ids.includes(req.user.id) || exempted_users.includes(req.user.id);
            // No need to call expand because we are not populating it..
            return ResponseService.successResponse(res, {
                projects: [project],
                user_onboard_status,
                ci_enabled: ci_enabled,
                ci_pending: false,
                pending_assessments: pending_assessments,
                rtw_status: extractRtwStatus(rtw_setting, project.id),
                ci_expired: false,
                company: contractor,
                phrasing: company_induction_setting && company_induction_setting.value && company_induction_setting.value.phrasing,
                project_phrasing: (project.custom_field && project.custom_field.induction_phrase_singlr),
                message,
                valid: true
            });

        } catch (fetchError) {
            sails.log.info('Failed to fetch projects', fetchError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
        }
    },

    /**
     * create induction:
        + @DB: IF not a test user, check existing induction for the user, abort process if found.
        + @IF: request got `induction_slot` ?
            + Book induction slot, abort process on error
        + @DB: IF project is from `GB`, get district by user's postcode
        + @DB: get nominated and full access site-admin of project, with mail notification enabled
        + @Google: attach distance matrix details user's postcode <=> project's postcode
        + @DB: IF request got quiz answers, fetch project's induction-question (quiz) and populate `induction_question_answers`.
        + @DB: get project
        + @DB: attach latest copy of user docs by fetching doc_ids provided into request.
            + @DB: populate children
            + @DB: populate document files
        + populate rams briefing comment if request has rams_register info.
        + @DB: create induction record
        + IF request has rams_register info?
            + @DB: find existing tool-briefing record for rams
            + @DB: update existing if found OR create new one.
            + @DB: store rams briefing user-sign.
        + store Long Medication records if request has records
            + @DB: create each long medication record
        + ASYNC: send induction creation alerts
            + @DB: get user info of mail notification enabled site-admins (nominated and full access)
            + @Lambda: Queue Email notification
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    createInduction: async (req, res) => {
        sails.log.info('create induction request by', req.user.id);
        let inductionRequest = _.pick((req.body || {}), [
            'project_ref',
            'user_ref',
            'user_sign',
            'inductor_ref',
            'travel_time',
            'travel_method',
            'vehicle_reg_number',

            'fit_undertake_role',
            'fit_to_work',
            'comply_hour_agreement',
            'site_directive_selection',
            'accept_drug_alcohol_pol',
            'confirm_detail_valid',
            'accepting_media_declaration',
            'all_media_watched',

            // @deprecated
            //'site_health_assessment',
            'reportable_medical_conditions',
            'rmc_detail',
            'on_long_medication',
            'any_side_effects',
            'any_side_effect_detail',

            'user_doc_ids',
            'accepted_declarations',
            'declarations',
            'status_code',
            'comments',
            'additional_data',
            'induction_slot',
            'rtw_doc_code',
            'rtw_check_result',
            'induction_answers',
            'rams_register'
        ]);
        if (!(+inductionRequest.project_ref)) {
            return ResponseService.errorResponse(res, 'Required fields are missing');
        }
        // check if induction already exists??
        let isTestUser = isAmongTestUser(req.user.email, +inductionRequest.project_ref);
        if(!isTestUser){
            sails.log.info('checking existing induction for same user');
            let existing_inductions = await sails.models.inductionrequest_reader.find({
                where: {project_ref: inductionRequest.project_ref, user_ref: req.user.id, status_code: {'!=': 0}},
                select: ['id', 'record_id', 'status_code', 'project_ref']
            });

            if (existing_inductions && existing_inductions.length) {
                sails.log.info('User got existing induction');
                return ResponseService.errorResponse(res,
                    `You've already submitted an induction for this project. Check the 'My Projects' list to view its status`,
                    {existing_inductions}
                );
            }
        }
        let existing_ir_count = await sails.models.inductionrequest.count({project_ref: +inductionRequest.project_ref});
        sails.log.info(`[IR] project ${inductionRequest.project_ref} existing inductions count: ${existing_ir_count}`);
        if(inductionRequest.induction_slot && inductionRequest.induction_slot.id){
            // Book induction slot
            sails.log.info(`Booking induction-slot, project(${inductionRequest.project_ref}) slot`, inductionRequest.induction_slot);

            let rawResult = await sails.sendNativeQuery(
                `UPDATE project_induction_slot set booked_slots = (booked_slots + 1)
            WHERE project_ref = $1 AND booked_slots < total_slots AND id = $2;`,
                [inductionRequest.project_ref, inductionRequest.induction_slot.id]
            );
            sails.log.info(`Slot booked? affected rows:`, rawResult.rowCount);
            if(!rawResult.rowCount){
                sails.log.info(`Failed to book induction-slot, project(${inductionRequest.project_ref}) slot`, inductionRequest.induction_slot);
                return ResponseService.errorResponse(res, sails.__('induction_all_slots_got_booked'));
            }
        }
        if (inductionRequest.additional_data && inductionRequest.additional_data.contact_detail && inductionRequest.additional_data.contact_detail.country === 'United Kingdom') {
            inductionRequest.district = await getDistrictByPostcode(inductionRequest.additional_data.project, inductionRequest.additional_data.contact_detail.post_code);
        }

        //Temporary log to trace competency missing issue in induction
        sails.log.info("Induction Competencies: ", inductionRequest.user_doc_ids);
        if ((!inductionRequest.user_doc_ids || (inductionRequest.user_doc_ids && !inductionRequest.user_doc_ids.length)) && inductionRequest.additional_data.project &&
            (inductionRequest.additional_data.project.is_cscs_require ||
                (typeOf(inductionRequest.additional_data.project.other_doc_required, 'array') && inductionRequest.additional_data.project.other_doc_required.length))) {
            sails.log.warn("Required competencies not uploaded. ", `User: ${req.user.id}, `, `Platform: ${req.headers['platform'] || '-'}`);
        }
        inductionRequest = cleanInductionFields(inductionRequest);
        let additional_qa = (req.body.additional_induction_question && req.body.additional_induction_question.induction_questions && req.body.additional_induction_question.induction_questions.length) ? req.body.additional_induction_question.induction_questions : [];
        let additional_qa_title = (req.body.additional_induction_question && req.body.additional_induction_question.section_title) ? req.body.additional_induction_question.section_title : 'Induction Questions';
        let additional_qa_lang = (req.body.additional_induction_question && req.body.additional_induction_question.lang) ? req.body.additional_induction_question.lang : fallback_locale;

        //send mail to project nominator
        let designatedProjectUsers = await TokenUtil.allResourceAdminsWithOneOfDesignations(inductionRequest.project_ref, [ 'nominated', 'custom'], false);
        designatedProjectUsers = TokenUtil.filterProjectUsersEmailEligibility(designatedProjectUsers, 'induction');
        let sendToIds = designatedProjectUsers.map(uac => ({id: uac.user_ref}));

        inductionRequest.user_ref = req.user.id;
        inductionRequest.creator_name = getUserFullName(req.user);
        inductionRequest.edited_by = req.user.id;
        // let employment = (inductionRequest.additional_data && inductionRequest.additional_data.employment_detail) || {};
        // inductionRequest.employer_name = employment.employer;
        // inductionRequest.job_role = employment.job_role;
        // Update user travel distance Matrix.
        // Added distance Matrix code.
        if(inductionRequest.additional_data && inductionRequest.additional_data.project && inductionRequest.additional_data.project.postcode) {
            inductionRequest = await attachDistanceMatrixDetails(inductionRequest, inductionRequest.additional_data.project);
            // inductionRequest.travel_time = await constructDistanceMatrix(inductionRequest, inductionRequest.additional_data.project.postcode);
        }
        if(inductionRequest.induction_answers && inductionRequest.induction_answers.length > 0) {
            let lang = (req.body._induction_quiz && req.body._induction_quiz.lang) ? req.body._induction_quiz.lang : fallback_locale;
            let data = await sails.models.inductionquestions.findOne({project_ref: inductionRequest.project_ref, data_type: 'quiz', lang});
            let p = {
                'questions': data.induction_questions,
                'answers': data.induction_answers,
                'quiz_lang': lang,
                'id': data.id,
            }
            inductionRequest.induction_question_answers = p;
        }

        if (additional_qa.length) {
            inductionRequest.induction_question_answers = (inductionRequest.induction_question_answers) ? inductionRequest.induction_question_answers : {};
            inductionRequest.induction_question_answers.additional_qa = additional_qa;
            inductionRequest.induction_question_answers.additional_qa_title = additional_qa_title;
            inductionRequest.induction_question_answers.additional_qa_lang = additional_qa_lang;
        }

        let project = await getProject(inductionRequest.project_ref, true);
        if (!project) {
            return ResponseService.errorResponse(res, 'Request contains invalid project reference');
        }
        let country_code = (project.custom_field && project.custom_field.country_code);
        inductionRequest = await attachUserDocument(inductionRequest, {
            user_ref: req.user.id,
            last_name: req.user.last_name,
            country_code,
            parent_company: project.parent_company,
        }, true, true);
        // sails.log.info('create induction-request with', inductionRequest);

        if (inductionRequest.rams_register) {
            inductionRequest.comments.push({
                "timestamp": moment().valueOf(),
                "user_id": req.user.id,
                "name": req.user.name,
                "origin": "rams_briefing",
                "note": `${inductionRequest.rams_register.rams_phrase} sign off: ${inductionRequest.rams_register.rams_title}`,
                "signature": inductionRequest.rams_register.signature || ''
            });
        }
        let rams_register = Object.assign({},  inductionRequest.rams_register);

        let induction_request = await sails.models.inductionrequest.create(inductionRequest);

        if(!existing_ir_count){
            notifyOnFirstInduction(project).catch(not_err => sails.log.info(`Failed while notifying for first induction`, not_err));
        }
        if (induction_request.id && rams_register.rams_id) {
            await updateRamsRegister(rams_register, req.user.id, +inductionRequest.project_ref);
        }
        if (induction_request.rtw_doc_code) {
            const subscription_result = await subscribeToRtwStatusChangeForInduction(project.parent_company, induction_request.id, {
                ppac_code: induction_request.rtw_doc_code,
                surname: induction_request.additional_data?.user_info?.last_name,
                dob: dayjs(induction_request.additional_data?.user_info?.dob, dbDateFormat_YYYY_MM_DD).toISOString(),
            }).catch(ex => {
                sails.log.info(`[RTW] error while subscribing for status change of id: ${induction_request.id}, ppac_code: ${induction_request.rtw_doc_code}`);
                sails.log.info(`[RTW] exception:`, ex);
                return {};
            });
            if (subscription_result.success) {
                await sails.models.inductionrequest.updateOne({id: induction_request.id}).set({
                    rtw_doc_code: subscription_result.rtw_doc_code,
                    rtw_check_result: subscription_result.rtw_check_result,
                }).catch(sails.log.info);
            }
        }

        sails.log.info('induction created successfully, id', induction_request.id);
        let medications = await storeLongMedicationRecords(induction_request, req.body.medications, true);
        induction_request.medications = medications;
        sendInductionCreationAlerts(project, induction_request,  req.user, sendToIds).catch(sails.log.error);
        return ResponseService.successResponse(res, {induction_request});
    },

    /**
     * @deprecated: in favor of `getProjectInductedUsers`
     *
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    getProjectInductions: async (req, res) => {
        let called_at = (new Date()).getTime();

        let projectId = +req.param('projectId', 0);
        let approved_only = (req.param('all', 'false') === 'false');
        let induction_requests = await getProjectInductionRequests(projectId, {
            ...(approved_only ? {status_code: 2} : {})
        }, [
            'record_id', 'project_ref', 'user_ref', 'inductor_ref', 'additional_data', 'status_code', 'createdAt',
            'optima_badge_number', 'comments', 'travel_time'
        ], null, null, false, false, ['id', 'first_name', 'middle_name', 'last_name'], false);

        let finished_at = (new Date()).getTime();

        AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.INNDEX, projectId, true, 200, {
            method: req.method,
            url: req.path,
            payload: {userId: req.user.id, userName: req.user.name, roles: req.user.roles, uac: req.user.raw_uac},
            headers: req.headers || {},
            response_body: {},
            response_headers: (res.headers || {})
        }, (finished_at - called_at), 'get-project-inductions', 'rest').catch(sails.log.error);

        return ResponseService.successResponse(res, {induction_requests});
    },

    getProjectInductedUsers: getAllInductedUsers,

    getProjectInductedUsersCA: getAllInductedUsers,

    /**
     * Search Among inducted users (for TypeAhead)
     * @param req
     * @param res
     * @returns {Promise<{records}>}
     */
    searchProjectInductedUsers: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let searchTerm = (req.param('q', '')).toString().trim();
        let searchUserIds = (req.param('users', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let extraColumns = req.param('extra', '').split(',');
        let pageSize = (searchUserIds.length > 19) ? searchUserIds.length : 20;

        let defaultStatusCodes = '2,6';
        let statusCodes = (req.param('statusCodes', defaultStatusCodes)).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let additionalCols = [
        ];
        if (extraColumns.includes('pic')) {
            additionalCols.push(...[`additional_data -> 'user_info' -> 'profile_pic_ref'   as profile_pic_ref`]);
        }
        if (extraColumns.includes('gender')) {
            additionalCols.push(`additional_data -> 'user_info' ->> 'gender' as gender`);
        }
        if (extraColumns.includes('email')) {
            additionalCols.push(`additional_data -> 'user_info' ->> 'email' as email`);
        }
        if (extraColumns.includes('contact')) {
            additionalCols.push(...[
                `additional_data -> 'contact_detail' ->> 'mobile_no'   as contact`,
                `additional_data -> 'contact_detail' -> 'mobile_number'   as contact_number`,
                `additional_data -> 'contact_detail' ->> 'street'   as address`,
            ]);
        }
        if (extraColumns.includes('employment')) {
            additionalCols.push(...[
                `additional_data -> 'employment_detail' ->> 'employer' as employer`,
                `additional_data -> 'employment_detail' ->> 'job_role' as job_role`
            ]);
        }
        if (extraColumns.includes('names')) {
            additionalCols.push(...[
                `additional_data -> 'user_info' ->> 'first_name'       as first_name`,
                `additional_data -> 'user_info' ->> 'middle_name'      as middle_name`,
                `additional_data -> 'user_info' ->> 'last_name'        as last_name`,
            ]);
        }
        let {
            records
        } = await getProjectInductions(projectId, {
            statusCodes,
            limit: pageSize,
            searchTerm,
            searchUserIds
        }, additionalCols);
        return ResponseService.successResponse(res, {
            records,
            q: searchTerm,
            statusCodes,
        });
    },

    // @deprecated: in favor of `getInductionsListInnTime`
    getProjectInductionsInnTime: async (req, res) => {
        let approved_only = (req.param('all', 'false') === 'false');
        let induction_requests = await getProjectInductionRequests(req.project.id, {
            ...(approved_only ? {status_code: [STATUS_CODES.APPROVED, STATUS_CODES.IN_REVIEW]} : {})
        }, [
            'record_id', 'additional_data', 'status_code'
        ], null, null, false, false, false, false);

        let inductions = (induction_requests || []).map(record => {
            let {additional_data: {user_info, employment_detail}} = record;
            let profile_pic_ref = (user_info ? user_info.profile_pic_ref : {});
            let employment_detail_ref = (employment_detail || {});
            return {
                id: record.id,
                record_id: record.record_id,
                status_code: record.status_code,
                // status_message: record.status_message,
                user_ref: (user_info ? user_info.id : null),
                first_name: (user_info ? user_info.first_name : null),
                middle_name: (user_info ? user_info.middle_name : null),
                last_name: (user_info ? user_info.last_name : null),
                profile_pic_ref: profile_pic_ref,
                employer: employment_detail_ref.employer || null,
                employment_company: employment_detail_ref.employment_company || null,
                job_role: employment_detail_ref.job_role || null,
            }
        });
        return ResponseService.successResponse(res, {inductions});
    },

    /**
     * @deprecated
     * Was used to download xlsx report on frontend but it is now API driven and replaced by downloadInductionRecordsXLSX
     */
    getProjectInductionRequests: async (req, res) => {
        let called_at = (new Date()).getTime();

        let projectId = req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let ignorePagination = (req.param('all', 'false') === 'true');
        let search = req.param('search');
        let status = req.param('status');
        let employer = req.param('employer');
        let filter = {}
        let selectedIds = []
        if (!projectId) {
            return ResponseService.errorResponse(res, 'project id is required');
        }

        let induction_search = await getProjectInductionRequests(projectId, {}, ['additional_data', 'on_long_medication', 'createdAt', 'record_id', 'status_code'], null, null, false, false, false, false);
        let total_approved_count = induction_search.filter(ir => ir.status_code === STATUS_CODES.APPROVED).length;
        let inductedEmployers
        inductedEmployers = induction_search.reduce((list, item) => {
            let employer = ((item.additional_data && item.additional_data.employment_detail && item.additional_data.employment_detail.employer) || '').toString().trim();
            if(employer.length && !list.includes(employer)){
                list.push(employer);
            }
            return list;
        }, []).sort((a, b) => {
            return a.localeCompare(b);
        });

        if(search != undefined || status != undefined || employer != undefined) {
            let searchText
            if(search == undefined) {
                searchText = false
            } else {
                searchText = search
            }

            let statusCode = status
            let employerName = (employer || '').toLowerCase();
            induction_search.map(d => {
                if(
                    (!searchText ||
                        (d.additional_data && d.additional_data.user_info && d.additional_data.user_info.name && d.additional_data.user_info.name.toLowerCase().indexOf(searchText) !== -1) ||
                        (d.additional_data && d.additional_data.user_info && d.additional_data.user_info.first_name && d.additional_data.user_info.first_name.toLowerCase().indexOf(searchText) !== -1) ||
                        (d.additional_data && d.additional_data.employment_detail && d.additional_data.employment_detail.employer && d.additional_data.employment_detail.employer.toLowerCase().indexOf(searchText) !== -1) ||
                        (d.additional_data && d.additional_data.employment_detail && d.additional_data.employment_detail.employment_company && d.additional_data.employment_detail.employment_company.toLowerCase().indexOf(searchText) !== -1) ||
                        (d.createdAt && dayjs(d.createdAt).format('D/MMM/YYYY').toLowerCase().indexOf(searchText) !== -1) ||
                        (d.status_message && d.status_message.toLowerCase().indexOf(searchText) !== -1) ||
                        (d.record_id && d.record_id.toString().toLowerCase().indexOf(searchText) !== -1)) && (!statusCode || d.status_code == statusCode) &&
                    (!employerName || d.additional_data && d.additional_data.employment_detail && d.additional_data.employment_detail.employer && d.additional_data.employment_detail.employer.toLowerCase().indexOf(employerName) !== -1)
                ){
                    selectedIds.push(d.id);
                }
            });
            filter = {id:selectedIds}
        }
        let induction_requests = await getProjectInductionRequests(projectId,filter,[], pageSize, pageNumber, ignorePagination, true);
        let countFilter
        if(selectedIds.length>0 || (search != undefined || status != undefined)){
            countFilter = { where: { project_ref: projectId, id: selectedIds} }
        } else {
            countFilter = { where: { project_ref: projectId} }
        }
        let total_record_count = await sails.models.inductionrequest_reader.count(countFilter)
        // @todo: spatel: Once we start freezing user doc, below call won't be needed
        induction_search = await expandUserDocs(induction_requests);

        let projectUsers = await getProjectUsers(projectId);
        let inductedUsers = [];
        induction_search = induction_search.map(record => {
            if (record.user_ref && record.user_ref.id) {
                record.uac = projectUsers.find(ue => ue.user_ref === record.user_ref.id);
            }

            //In Review, Approved, Change Request Inductions
            if ([2, 3, 6].includes(record.status_code) && record.user_ref && record.user_ref.id) {
                inductedUsers.push(record.user_ref.id);
            }
            return record;
        });

        if (ignorePagination) {
            induction_search = await attachLastOnsiteOfUsers(projectId, induction_search, inductedUsers);
        } else {
            let where = {
                project_ref: projectId,
                or     : [
                    { expire_on : null }, //indefinite
                    { expire_on : {'>=': dayjs().valueOf()} },
                ],
            };

            let userConductCards = await sails.models.userconductcard_reader.find(where);
            userConductCards = await populateUserRefs(userConductCards, 'assigned_by_ref', []);
            induction_search = (induction_search || []).map(ir => {
                ir.conduct_cards = (userConductCards || []).filter(record => record.user_ref == ((ir.user_ref && ir.user_ref.id) || ir.user_ref));
                return ir;
            });
        }

        // sails.log.info(induction_search);

        let finished_at = (new Date()).getTime();

        AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.INNDEX, projectId, true, 200, {
            method: req.method,
            url: req.path,
            payload: {userId: req.user.id, userName: req.user.name, roles: req.user.roles, uac: req.user.raw_uac},
            headers: req.headers || {},
            response_body: {},
            response_headers: (res.headers || {})
        }, (finished_at - called_at), 'get-project-induction-requests', 'rest').catch(sails.log.error);

        //@todo: 541 mobile app using project_users from induction_requests.additional_data
        return ResponseService.successResponse(res, {induction_requests: induction_search, total_record_count, total_approved_count, inducted_employers: inductedEmployers});
    },

    // Being used from download-inductions component in Company project area.
    getProjectInductionRequestsAlias: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let induction_requests = await getProjectInductionRequests(projectId, {},[], null, 0, true, true,
            ['id', 'first_name', 'middle_name', 'last_name', 'email', 'dob'],
            ['id', 'first_name', 'middle_name', 'last_name']);
        induction_requests = await expandUserDocs(induction_requests);
        let inductedUsers = induction_requests.reduce((list, record) => {
            if([2, 3, 6].includes(record.status_code) && record.user_ref && record.user_ref.id){
                list.push(record.user_ref.id);
            }
            return list;
        }, [])
        induction_requests = await attachLastOnsiteOfUsers(projectId, induction_requests, inductedUsers);
        return ResponseService.successResponse(res, {induction_requests});
    },

    viewOrDownloadInduction: async (req, res) => {
        let inductionRequestId = +req.param('inductionRequestId');
        let nowMs = +req.query.nowMs;
        let embedRequest = (req.query.embed === 'frame');
        // let timezone = (req.query.tz || 'UTC');
        let type = req.param('type','html');
        let createdAt = +req.param('createdAt', 0);

        sails.log.info('Render induction-request:', inductionRequestId, `type: ${type} createdAt: ${createdAt} embed: ${embedRequest}`);
        if (!inductionRequestId) {
            return ResponseService.errorResponse(res, 'Invalid Request');
        }

        let target_date = isNaN(nowMs) ? moment() : moment(nowMs);
        let induction_request = await sails.models.inductionrequest
            .findOne({
                id: inductionRequestId,
                createdAt,
            })
            .populate('medications')
            .populate('inductor_ref');

        if (induction_request && induction_request.id) {
            let {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts} = await inductionDownloadMetaData(induction_request.project_ref);
            let response = await processViewAndDownloadInductionRequests(req, res, induction_request, type, target_date, embedRequest, {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts});
            if(type === 'html') {
                sails.log.info('Rendering html view');
                return res.send(response);
            }
            //pdf
            return response;
        }
        sails.log.info('Failed to find induction request.');
        return ResponseService.errorResponse(res, 'Failed to find induction request.');
    },

    fetchVehicleRegDetails: async (req, res) => {
        let registrationNumber = req.param('registrationNumber');
        let outcome = await fetchVehicleRegDetails(registrationNumber, true, sails.__('vehicle_reg_not_found_on_dvla'));
        ResponseService.sendResponse(res, outcome);
    },

    updateInductionRequest: async (req, res) => {
        let inductionRequestId = req.param('inductionRequestId');
        let origin = req.param('origin', 'user');
        let from_screen = req.param('from', '-');
        sails.log.info(`Update induction-request: ${inductionRequestId} origin: ${origin} from_screen: ${from_screen}`);
        if (!inductionRequestId) {
            return ResponseService.errorResponse(res, 'induction request id is required');
        }
        let ir = await sails.models.inductionrequest_reader.findOne({id: inductionRequestId}).populate('project_ref');
        if (!ir) {
            return ResponseService.errorResponse(res, 'Induction record not found');
        }
        sails.log.info('update induction request by', req.user.id);
        let inductionRequest = _.pick((req.body || {}), [
            // 'project_ref',
            // 'user_ref',
            // 'inductor_ref',
            'travel_time',
            'travel_method',
            'user_sign',
            'vehicle_reg_number',


            'fit_undertake_role',
            'fit_to_work',
            'confirm_detail_valid',
            'comply_hour_agreement',
            'site_directive_selection',
            'accept_drug_alcohol_pol',

            // @deprecated
            //'site_health_assessment',
            'reportable_medical_conditions',
            'rmc_detail',
            'on_long_medication',
            'any_side_effects',
            'any_side_effect_detail',

            'accepting_media_declaration',
            'all_media_watched',
            'user_doc_ids',
            'accepted_declarations',
            'declarations',
            'status_code',
            'comments',
            'additional_data',
            'induction_slot',
            'rtw_doc_code',
            'rtw_check_result',
            'optima_badge_number',
            'induction_answers'
        ]);
        inductionRequest = cleanInductionFields(inductionRequest);
        let additional_qa = (req.body.additional_induction_question && req.body.additional_induction_question.induction_questions && req.body.additional_induction_question.induction_questions.length) ? req.body.additional_induction_question.induction_questions : [];
        let additional_qa_title = (req.body.additional_induction_question && req.body.additional_induction_question.section_title) ? req.body.additional_induction_question.section_title : 'Induction Questions';
        let additional_qa_lang = (req.body.additional_induction_question && req.body.additional_induction_question.lang) ? req.body.additional_induction_question.lang : fallback_locale;

        let inductorName = 'An admin';
        let inductor_info;
        //push inductor id if available
        if (req.body.inductor_email) {
            inductor_info = await sails.models.user_reader.findOne({
                where: {email: req.body.inductor_email},
                select: ['id', 'email', 'first_name', 'middle_name', 'last_name', 'profile_pic_ref']
            }).populate('profile_pic_ref');
            let additionalData = ir.additional_data || inductionRequest.additional_data || {};
            additionalData.accepted_at = moment().valueOf(); // @todo: need better check??
            if (inductor_info && inductor_info.id) {
                inductionRequest.inductor_ref = inductor_info.id;
                inductorName = getUserFullName(inductor_info, true);
            }
            inductionRequest.additional_data = additionalData;
        } else {
            inductorName = getUserFullName(req.user, true);
        }

        if (inductionRequest.additional_data && inductionRequest.additional_data.contact_detail && inductionRequest.additional_data.contact_detail.country === 'United Kingdom') {
            inductionRequest.district = await getDistrictByPostcode(inductionRequest.additional_data.project, inductionRequest.additional_data.contact_detail.post_code);
        }

        let skip_deleting_medication = req.body.medications === undefined;

        // Update user travel distance Matrix.
        // Added distance Matrix code.
        if (inductionRequest.travel_time) {
            inductionRequest = await attachDistanceMatrixDetails(inductionRequest, ir.project_ref);
            // inductionRequest.travel_time = await constructDistanceMatrix(inductionRequest, ir.project_ref.postcode);
        }
        if (inductionRequest.induction_answers && inductionRequest.induction_answers.length > 0) {
            let lang = (req.body._induction_quiz && req.body._induction_quiz.lang) ? req.body._induction_quiz.lang : fallback_locale;
            let data = await sails.models.inductionquestions.findOne({
                project_ref: req.body.additional_data.project.id,
                data_type: 'quiz',
                lang
            });
            if(data && data.id){
                let p = {
                    'questions': data.induction_questions,
                    'answers': data.induction_answers,
                    'quiz_lang': lang,
                    'id': data.id,
                }
                inductionRequest.induction_question_answers = p;
            }
        }

        if (additional_qa.length) {
            inductionRequest.induction_question_answers = (inductionRequest.induction_question_answers) ? inductionRequest.induction_question_answers : {};
            inductionRequest.induction_question_answers.additional_qa = additional_qa;
            inductionRequest.induction_question_answers.additional_qa_title = additional_qa_title;
            inductionRequest.induction_question_answers.additional_qa_lang = additional_qa_lang;
        }

         if(!inductionRequest.comments || !inductionRequest.comments.length) {
            inductionRequest.comments = [...ir.comments];
        }


        let newlyAddedComments = [];
        let recentCommentObject = (req.body.recent_comments) || [];         // Added provision to only pass new comment into update call, instead of passing all comments.
        if(!recentCommentObject || !recentCommentObject.length){
            //to get the recent comments to find the user added comment
            let comms = inductionRequest.comments || [];
            newlyAddedComments = comms.slice(ir.comments.length, comms.length);
        }else{
            newlyAddedComments = recentCommentObject;
            inductionRequest.comments = [...(ir.comments || []), ...newlyAddedComments];
        }

        inductionRequest.edited_by = req.user.id;
        if (ir.user_ref === req.user.id) {
            inductionRequest.creator_name = getUserFullName(req.user);
        }
        // target status Approved / CR only
        let re_reviewed_logs = [...(ir.re_review_logs || [])];

        if ([STATUS_CODES.APPROVED, STATUS_CODES.CHANGE_REQUESTED].includes(inductionRequest.status_code) && ir.recent_changes && ir.recent_changes.length) {
            sails.log.info('Attaching re-reviewed log, making recent_changes empty');
            ir.recent_changes.map(change => {
                re_reviewed_logs.push({
                    reviewer_ref: req.user.id,
                    reviewer_name: getUserFullName(req.user),
                    reviewed_on: (new Date()).getTime(),
                    ...change
                })
            });
            inductionRequest.recent_changes = null;
            inductionRequest.re_review_logs = re_reviewed_logs;
        }
        sails.log.info('skip deleting induction medications?', skip_deleting_medication);
        // sails.log.info('update induction-request with', inductionRequest);

        let project_id = (ir.project_ref && ir.project_ref.id) || ir.project_ref;
        if (inductionRequest.induction_slot && inductionRequest.induction_slot.id) {
            // Unblock existing booking
            if (ir.induction_slot && ir.induction_slot.id) {
                sails.log.info(`Unblock existing booking, project(${project_id}), slot`, ir.induction_slot);
                let updateResult = await sails.sendNativeQuery(
                    `UPDATE project_induction_slot
                     set booked_slots = (booked_slots - 1)
                     WHERE project_ref = $1
                       AND booked_slots > 0
                       AND id = $2;`,
                    [project_id, ir.induction_slot.id]
                );
                sails.log.info(`Slot unblocked? affected rows:`, updateResult.rowCount);
            }
            // Book induction slot
            sails.log.info(`Booking induction-slot, project(${project_id}) slot`, inductionRequest.induction_slot);

            let rawResult = await sails.sendNativeQuery(
                `UPDATE project_induction_slot
                 set booked_slots = (booked_slots + 1)
                 WHERE project_ref = $1
                   AND booked_slots < total_slots
                   AND id = $2;`,
                [project_id, inductionRequest.induction_slot.id]
            );
            sails.log.info(`Slot booked? affected rows:`, rawResult.rowCount);
            if (!rawResult.rowCount) {
                sails.log.info(`Failed to book induction-slot, project(${project_id}) slot`, inductionRequest.induction_slot);
                return ResponseService.errorResponse(res, sails.__('induction_all_slots_got_booked'));
            }
        }
        /*
        if(inductionRequest.additional_data){
            // No need to refresh these fields if caller is not updating employment details at all.
            let employment = (inductionRequest.additional_data.employment_detail) || {};
            inductionRequest.employer_name = employment.employer;
            inductionRequest.job_role = employment.job_role;
        }
        */
        if (!inductionRequest.additional_data) {
            // additional document update call doesn't have additional data into request.
            inductionRequest.additional_data = ir.additional_data;
        }

        let country_code = (ir.project_ref && ir.project_ref.custom_field && ir.project_ref.custom_field.country_code);
        let user_info = (ir.additional_data || {}).user_info || {};
        inductionRequest = await attachUserDocument(inductionRequest, {
            user_ref: ir.user_ref,
            last_name: user_info.last_name,
            country_code,
            parent_company: ir.project_ref.parent_company,
        }, true, false, false, false);

        if([STATUS_CODES.APPROVED, STATUS_CODES.CHANGE_REQUESTED].includes(inductionRequest.status_code) && ir.fr_face_id){
            // given induction already have face indexed
            // remove previous faceId
            sails.log.info(`[FR]: induction record ${ir.id} already having faceId: ${ir.fr_face_id}`);
            let {
                success: deleteSuccess,
                data: deleteData
            } = await triggerDeleteFaceFromCollection(project_id, [ir.fr_face_id]);
            if (!deleteSuccess) {
                sails.log.warn(`[FR]: Failed while deleting induction: ${ir.id} faceId: ${ir.fr_face_id}`);
                return ResponseService.errorResponse(res, sails.__('facial_recognition_enrolment_failed'));
            }
            ir.fr_face_id = null;
        }
        if (inductionRequest.status_code === STATUS_CODES.APPROVED) {
            const post_code =
                inductionRequest &&
                inductionRequest.additional_data &&
                inductionRequest.additional_data.contact_detail &&
                inductionRequest.additional_data.contact_detail.post_code;
            const country_code = inductionRequest.additional_data.user_info.country_code || WeatherSyncService.DEFAULT_COUNTY_CODE_GB;
            const projectCoordinate =
                inductionRequest &&
                inductionRequest.additional_data &&
                inductionRequest.additional_data.project &&
                inductionRequest.additional_data.project.custom_field &&
                inductionRequest.additional_data.project.custom_field.location;
            const coordinate = await getCoordinates(post_code, country_code && country_code.trim());

            let {travel_time} = await updateTravelTime(ir, coordinate, [], projectCoordinate)
            inductionRequest.travel_time = travel_time;

            // index new face only if FR is enabled & existing face is not there
            let optima_setting = await sails.models.optimasetting_reader.findOne({
                project_ref: project_id,
                has_fr: true,
            });
            let project_has_fr = !!(optima_setting && optima_setting.id);
            sails.log.info(`[FR]: Project : ${project_id} FR status: ${project_has_fr}`);
            if (project_has_fr) {
                let indexingResult = await indexInductionUserFaceOrError(project_id, ir);
                if (indexingResult.error) {
                    indexingResult = await getMatchingInductionRecordsMessage(project_id, ir.id, indexingResult);
                    return ResponseService.sendResponse(res, indexingResult);
                }
                inductionRequest.fr_face_id = indexingResult.fr_face_id;
            }

        }
        if (inductionRequest.rtw_doc_code && (+inductionRequest.rtw_doc_code !== +ir.rtw_doc_code)) {
            const subscription_result = await subscribeToRtwStatusChangeForInduction(ir.project_ref.parent_company, ir.id, {
                ppac_code: inductionRequest.rtw_doc_code,
                surname: ir.additional_data?.user_info?.last_name,
                dob: dayjs(ir.additional_data?.user_info?.dob, dbDateFormat_YYYY_MM_DD).toISOString(),
            }).catch(ex => {
                sails.log.info(`[RTW] error while subscribing for status change of id: ${ir.id}, ppac_code: ${inductionRequest.rtw_doc_code}`);
                sails.log.info(`[RTW] exception:`, ex);
                return {};
            });
            if (subscription_result.success) {
                inductionRequest.rtw_doc_code = subscription_result.rtw_doc_code;
                inductionRequest.rtw_check_result = subscription_result.rtw_check_result;
            }
        }
        let induction_request = await sails.models.inductionrequest.updateOne({id: inductionRequestId}).set(inductionRequest);
        sails.log.info('updated successfully, id', induction_request ? induction_request.id : '');

        // Unblock existing booking
        if (inductionRequest.status_code === 0 && ir.induction_slot && ir.induction_slot.id) {
            sails.log.info(`Unblock existing booking, project(${project_id}), slot`, ir.induction_slot);
            sails.sendNativeQuery(
                `UPDATE project_induction_slot
                 set booked_slots = (booked_slots - 1)
                 WHERE project_ref = $1
                   AND booked_slots > 0
                   AND id = $2;`,
                [project_id, ir.induction_slot.id]
            ).exec(function updateCallback(unblockError, unblockResult) {
                if (unblockError) {
                    sails.log.error('Failed while unblocking user', unblockError);
                } else {
                    sails.log.info(`Slot unblocked? affected rows:`, unblockResult.rowCount);
                }
            });
        }

        if (req.body.medications) {
            induction_request.medications = req.body.medications;
        }
        let profile_pic = null;
        if (inductor_info && inductor_info.profile_pic_ref && inductor_info.profile_pic_ref.file_url) {
            profile_pic = inductor_info.profile_pic_ref.file_url;
        }
        if (inductionRequest.status_code === 2) {
            sails.log.info('Induction request Accepted. Id:', induction_request.id);
            // Should send email for all bio-metric sources, except optima
            //sending notification of induction approval to mobile devices
            let induction_copy = Object.assign({}, induction_request);
            induction_copy.inductorName = inductorName;
            SmartSheetService.exportOnInductionApproval(project_id, [induction_copy]).catch(sails.log.error);
            if ((ir.project_ref.custom_field && ir.project_ref.custom_field.procore_integration) &&
                !(ir.project_ref.custom_field && ir.project_ref.custom_field.disable && ir.project_ref.custom_field.disable.induction_export_on_procore)
            ) {
                // fetch company setting
                let company_setting = await sails.models.companysetting_reader.findOne({
                    select: ['id', 'company_ref', 'value'],
                    where: {
                        name: COMPANY_SETTING_KEY.PROCORE_CONFIG,
                        company_ref: ir.project_ref.parent_company
                    }
                });
                let procorePermissionID = req.body.procorePermissionId || null;
                let companyConf = ((company_setting && company_setting.id) ? company_setting.value : {});
                sails.log.info(`Exporting induction to procore, permissionID: ${procorePermissionID} companyConf:`, companyConf);
                ProcoreService.exportUsersOnInductionApproval(project_id, [induction_copy], procorePermissionID, companyConf).catch(sails.log.error);
            }
            sails.log.info('Now sending the mobile notification');
            sendMobileNotification(induction_request, inductorName, profile_pic, NOTIFICATION_CATEGORY.INDUCTION_APPROVED, newlyAddedComments).catch(sails.log.error);


            if (req.body.access && req.body.access.designation && req.body.access.designation.length) {
                let accessData = req.body.access;
                if (accessData && accessData.id) {
                    sails.log.info(`Updating project access for SITE-ADMIN, project: ${project_id}, user: ${accessData.user_ref} record id`, accessData.id);
                    await sails.models.userrole.updateOne({
                        id: accessData.id,
                        resource: TokenUtil.resourceIdentifier.PROJECT(project_id),
                    }).set({
                        designation: (accessData.designation || []).join(','),
                        permission: accessData.permission || [],
                    });
                } else {
                    sails.log.info(`Creating project access for SITE-ADMIN, project: ${project_id}, user: ${accessData.user_ref}`);
                    await storeUACRecordsOrError([{
                        user_ref: accessData.user_ref,
                        role: TokenUtil.ROLES.SITE_ADMIN,
                        resource: TokenUtil.resourceIdentifier.PROJECT(project_id),
                        sub_resource: '',
                        designation: (accessData.designation || []).join(','),
                        permission: accessData.permission || [],
                        flags: {}
                    }]).catch(e => sails.log.info(`Failed while adding site-admin role for user: ${accessData.user_ref}, project: ${project_id}`, e));
                }
            }
        } else {
            // caller's profile pic, can be optimized
            let rUser = await sails.models.user_reader.findOne({
                where: {id: req.user.id},
                select: ['id', 'profile_pic_ref']
            }).populate('profile_pic_ref');
            if (rUser && rUser.profile_pic_ref && rUser.profile_pic_ref.file_url) {
                profile_pic = rUser.profile_pic_ref.file_url;
            }
            if (inductionRequest.status_code === 0) {
                sendMobileNotification(induction_request, inductorName, profile_pic, NOTIFICATION_CATEGORY.INDUCTION_REJECTED, newlyAddedComments).catch(sails.log.error);
            } else if (inductionRequest.status_code === 3) {
                sendMobileNotification(induction_request, inductorName, profile_pic, NOTIFICATION_CATEGORY.INDUCTION_CHANGE_REQUESTED, newlyAddedComments).catch(sails.log.error);
            }
        }
        sendInductionStatusChangeAlert(induction_request, req.user, origin).catch(sails.log.error);
        induction_request.medications = await storeLongMedicationRecords(induction_request, req.body.medications, skip_deleting_medication);
        return ResponseService.successResponse(res, {induction_request});
    },

    checkDuplicateFaceIntoFR: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        // index new face only if FR is enabled & existing face is not there
        let optima_setting = await sails.models.optimasetting_reader.findOne({
            where: { project_ref: projectId, has_fr: true,},
            select: ['id', 'has_fr']
        });
        let project_has_fr = !!(optima_setting && optima_setting.id);
        sails.log.info(`[FR]: Project : ${projectId} FR status: ${project_has_fr}, search request for induction: ${inductionRequestId}`);
        if (!project_has_fr) {
            return ResponseService.successResponse(res, {optima_setting, message: sails.__('facial_recognition_not_enabled')});
            // return ResponseService.errorResponse(res, sails.__('facial_recognition_not_enabled'));
        }
        let ir = await sails.models.inductionrequest_reader.findOne({
            where: {
                id: inductionRequestId,
                project_ref: projectId,
                // status_code: [STATUS_CODES.APPROVED]
            },
            select: ['id', 'fr_face_id', 'additional_data', 'user_ref'],
        });
        if (!ir) {
            return ResponseService.errorResponse(res, sails.__('record not found'), {code: `induction_not_found`});
        }
        sails.log.info(`[FR]: Project : ${projectId} search induction face: ${ir.id}`);

        let searchResult = await searchInductionUserFace(projectId, ir);
        if (searchResult.error) {
            return ResponseService.successResponse(res, {code: searchResult.code});
        }

        let [matching_induction] = await sails.models.inductionrequest_reader.find({
            where: {project_ref: projectId, fr_face_id: searchResult.data.matchingFaceId, id: {'!=': ir.id}},
            select: ['id', 'fr_face_id', 'additional_data', 'user_ref', 'record_id', 'creator_name', 'status_code'],
            limit: 1,
        });
        if (!matching_induction) {
            sails.log.info(`error: No matching induction found for faceId: ${searchResult.data.matchingFaceId} auto-removing it from collection`);
            let {success: deleteSuccess, data: deleteData} = await triggerDeleteFaceFromCollection(projectId, [searchResult.data.matchingFaceId]);
            return ResponseService.successResponse(res, {fr_face_id: searchResult.data.matchingFaceId});
        }
        sails.log.info(`[FR]: Project: ${projectId} face on induction: ${ir.id} is matching with induction: ${matching_induction.id}`);
        let {additional_data: {user_info, employment_detail}} = matching_induction;
        let profile_pic_ref = null;
        if (user_info && user_info.profile_pic_ref && user_info.profile_pic_ref.file_url) {
            profile_pic_ref = user_info.profile_pic_ref;
        }
        matching_induction = {
            id: matching_induction.id,
            fr_face_id: matching_induction.fr_face_id,
            user_ref: matching_induction.user_ref,
            record_id: matching_induction.record_id,
            creator_name: matching_induction.creator_name,
            profile_pic_ref: profile_pic_ref,
            status_code: matching_induction.status_code,
            employer: (employment_detail && employment_detail.employer) || null,
            job_role: (employment_detail && employment_detail.job_role) || null,
        };
        return ResponseService.errorResponse(res, `This user's profile photo appears to be a duplicate of another inducted user's photo. Please update one of the profile photos to resolve the duplication.`, {
            matching_inductions: [matching_induction],
            data: searchResult.data
        });
    },

    enrolInductionIntoFR: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let project_has_fr = !!(req.project_optima_setting && req.project_optima_setting.id && req.project_optima_setting.has_fr);
        sails.log.info(`[FR]: Project : ${projectId} FR status: ${project_has_fr}, enrol request for induction: ${inductionRequestId}`);
        if (!project_has_fr) {
            return ResponseService.errorResponse(res, sails.__('facial_recognition_not_enabled'));
        }
        let ir = await sails.models.inductionrequest_reader.findOne({
            where: {
                id: inductionRequestId,
                project_ref: projectId,
                status_code: [STATUS_CODES.APPROVED]
            },
            select: ['id', 'fr_face_id', 'additional_data', 'user_ref'],
        });
        if (!ir || ir.fr_face_id) {
            return ResponseService.errorResponse(res, sails.__('facial_recognition_enrolment_failed'), {code: `induction_not_found`});
        }
        sails.log.info(`[FR]: Project : ${projectId} enrolling induction: ${ir.id}`);
        let indexingResult = await indexInductionUserFaceOrError(projectId, ir);
        if (indexingResult.error) {
            indexingResult = await getMatchingInductionRecordsMessage(projectId, ir.id, indexingResult);
            return ResponseService.sendResponse(res, indexingResult);
        }
        sails.log.info(`[FR]: induction: ${ir.id} enrolled with faceId: ${indexingResult.fr_face_id}`);
        let updated = await sails.models.inductionrequest.update({id: inductionRequestId}).set({
            fr_face_id: indexingResult.fr_face_id,
        });
        return ResponseService.successResponse(res, {induction_request: updated});
    },

    getInductionRequest: async (req, res) => {

        let inductionRequestId = +req.param('inductionRequestId');
        let appVersion = req.param('version', 'v1');
        sails.log.info('get induction-request:', inductionRequestId, `for current user`);
        if (!inductionRequestId) {
            sails.log.info('induction request id is required');
            return ResponseService.errorResponse(res, 'induction request id is required');
        }

        let induction_request = {};
        try{
            induction_request = await sails.models.inductionrequest
                .findOne({
                    id: inductionRequestId,
                    user_ref: req.user.id,
                }).populate('medications')
                .populate('inductor_ref');

            if(!(induction_request && induction_request.id)) {
                sails.log.info('invalid induction request id');
                return ResponseService.errorResponse(res, 'invalid induction request id');
            }

            //@todo: Vishal: This condition block can be removed by Jan 11, 2025.
            if (appVersion === 'v1' && induction_request.medications.length) {
                induction_request.medications = induction_request.medications.map(medication => {
                    medication.medication_date_commenced =  (medication.medication_date_commenced) ? dayjs(medication.medication_date_commenced, 'YYYY-MM-DD').format('DD-MM-YYYY') : medication.medication_date_commenced;
                    medication.medication_date_of_completion = (medication.medication_date_of_completion) ? dayjs(medication.medication_date_of_completion, 'YYYY-MM-DD').format('DD-MM-YYYY') : medication.medication_date_of_completion;
                    return medication;
                });
            }
            sails.log.info('got induction request data', induction_request.id);
            return ResponseService.successResponse(res, {induction_request});
        }catch (e) {
            sails.log.info('Failed to fetch induction record', e);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, e);
        }
    },

    getInductionMedications: async (req, res) => {
        let inductionRequestId = +req.param('inductionRequestId', 0);
        sails.log.info('get medications of induction-request:', inductionRequestId);
        let induction = await sails.models.inductionrequest_reader.findOne({
            where: {
                id: inductionRequestId,
                // user_ref: req.user.id,
            },
            select: ['id', 'reportable_medical_conditions', 'rmc_detail', 'on_long_medication', 'any_side_effects', 'any_side_effect_detail']
        }).populate('medications');
        sails.log.info('got induction', induction.id, 'medications', (induction.medications || []).length);
        return ResponseService.successResponse(res, {induction});
    },

    updateInductionMedications: async (req, res) => {
        let inductionRequestId = +req.param('inductionRequestId', 0);
        sails.log.info('update medications of induction-request:', inductionRequestId);

        let update_request = _.pick((req.body || {}), [
            'reportable_medical_conditions',
            'rmc_detail',
            'on_long_medication',
            'any_side_effects',
            'any_side_effect_detail',
        ]);
        let updated_row = await sails.models.inductionrequest.updateOne({id: inductionRequestId}).set(update_request);
        updated_row.medications = await storeLongMedicationRecords(updated_row, req.body.medications || []);
        let show_side_effects_block = false;
        if(updated_row.on_long_medication && updated_row.on_long_medication.toString().toLowerCase().trim() === 'yes'){
            show_side_effects_block = true;
        }
        // send email to user
        let recipientUser = (updated_row.additional_data && updated_row.additional_data.user_info) || {};
        if(recipientUser.email){
            sails.log.info('Notifying user about medication change, recipientUser:', recipientUser.id);
            try {
                let admin = req.user;
                let project = (updated_row.additional_data && updated_row.additional_data.project) || {};
                admin.name = getUserFullName(admin);
                recipientUser.name = getUserFullName(recipientUser);
                let subject = `Your medication info change for ${project.name}`;
                let html = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'induction-medication-changed-email',
                    project: project,
                    admin: admin,
                    induction_request: updated_row,
                    has_medications: (updated_row.on_long_medication && updated_row.on_long_medication.toString().toLowerCase().trim() === 'yes'),
                    show_side_effects_block,
                    recipient: recipientUser,
                    layout: false
                });
                await EmailService.sendMail(subject, [recipientUser.email], html);
            } catch (e) {
                sails.log.info('Unable to send email to user about medication change', e);
            }
        }
        ResponseService.successResponse(res, {induction: updated_row});
    },

    updateUserEmployment: async (req, res) => {
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let createdAt = +req.param('createdAt', 0);
        sails.log.info('update user employment detail of induction-request:', inductionRequestId);

        let request = _.pick((req.body || {}), [
            'job_role',
            'other_job_role',
            'type_of_employment',
            'employment_company',
            'other_type_of_employment',
            'employer',
            'parent_company'
        ]);

        let {validationError, payload} = userEmploymentUpdate(request);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }

        if (payload.job_role == "Other" && payload.other_job_role) {
            payload.job_role = `${payload.job_role} (${payload.other_job_role})`;
        }

        if (payload.type_of_employment == "Other" && payload.other_type_of_employment) {
            payload.type_of_employment = `${payload.type_of_employment} (${payload.other_type_of_employment})`;
        }

        let induction = await sails.models.inductionrequest_reader.findOne({
            where: {
                id: inductionRequestId,
                createdAt
            },
            select: ['id', 'additional_data', 'user_ref']
        });

        if(!induction || !induction.id) {
            sails.log.info('Invalid Request or ID of induction');
            return ResponseService.errorResponse(res, 'invalid request data');
        }

        let previousEmpDetails = _.pick((induction.additional_data.employment_detail || {}), [
            'job_role',
            'type_of_employment',
            'employment_company',
            'employer',
            'parent_company'
        ]);

        sails.log.info("Updating user employment detail in induction.");
        let updated_additional_data = (induction.additional_data || {});
        updated_additional_data.user_info = {
            ...(updated_additional_data.user_info || {}),
            parent_company: payload.parent_company
        };
        updated_additional_data.employment_detail = {
            ...(updated_additional_data.employment_detail || {}),
            job_role: payload.job_role,
            type_of_employment: payload.type_of_employment,
            employment_company: payload.employment_company,
            employer: payload.employer
        };
        let updated_row = await sails.models.inductionrequest.updateOne({id: inductionRequestId}).set({
            additional_data: updated_additional_data,
            // employer_name: payload.employer,
            // job_role: payload.job_role,
        });

        sails.log.info('Updated user employment detail in induction', inductionRequestId);

        let recipientUser = updated_additional_data.user_info || {};
        if(recipientUser.email){
            sails.log.info('Notifying user about employment details change, recipientUser:', recipientUser.id);
            try {
                let admin = req.user;
                let project = updated_additional_data.project || {};
                admin.name = getUserFullName(admin);
                recipientUser.name = getUserFullName(recipientUser);
                let subject = `Your employment details changed for ${project.name}`;
                let html = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'induction-company-changed-email',
                    project: project,
                    admin: admin,
                    recipient: recipientUser,
                    previousEmpDetails,
                    updatedEmpDetails: updated_additional_data.employment_detail,
                    layout: false,
                    isInfoChanged: (field) => {
                        return !(previousEmpDetails[field] == updated_additional_data.employment_detail[field]);
                    },
                });
                await EmailService.sendMail(subject, [recipientUser.email], html);
            } catch (e) {
                sails.log.info('Unable to send email to user about company change', e);
            }
        }

        ResponseService.successResponse(res, {induction: updated_row});
    },

    // for site-admin only
    overrideInductionTravelTime: async (req, res) => {
        let outcome = await updateTravelTimeOverride(req);
        if(outcome.error){
            return ResponseService.sendResponse(res, outcome);
        }
        return ResponseService.successResponse(res, {
            induction: outcome,
        });
    },

    // for induction owner, from mobile app
    updateTravelTimeDetails: async (req, res) => {
        let outcome = await updateTravelTimeOverride(req, true, req.user.id);
        if(outcome.error){
            return ResponseService.sendResponse(res, outcome);
        }
        let project_managers = await TokenUtil.allProjectAdminsByOneOfDesignations(outcome.project_ref, ['nominated', 'custom']);
        project_managers = TokenUtil.filterProjectUsersEmailEligibility(project_managers, 'induction');
        let user_name = getUserFullName((outcome.additional_data && outcome.additional_data.user_info) || {});
        let project_name = ((outcome.additional_data && outcome.additional_data.project) || {}).name || '';
        sails.log.info('Send travel-time updated email to nom-managers, count:', project_managers.length);
        let subject = `Travel details Updated: ${user_name} @ ${project_name}`;

        for (let i = 0; i < project_managers.length; i++) {
            let manager_ref = (project_managers[i] && project_managers[i].user_ref) || {};

            let emailHtml = await sails.renderView('pages/mail/mail-content', {
                title: subject,
                mail_body: 'induction-travel-time-updated-by-user',
                user_name,
                induction: outcome,
                project_name: project_name,
                receiver_name: manager_ref.first_name,
                layout: false
            });
            sails.log.info('Sending mail to', manager_ref.email);
            await EmailService.sendMail(subject, [manager_ref.email], emailHtml);
            sails.log.info('induction travel-time updated email has been sent');
        }
        return ResponseService.successResponse(res, {
            induction: outcome,
        });
    },

    updateInductionToBlacklistUser: async (req, res) => {
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let project_id = +req.param('projectId');
        let override_details = _.pick((req.body || {}), [
            'status_code',
            'comment'
        ]);
        let from_state = +(req.body.from_state || 0);
        let admin_id = +(req.body.admin_id || 0);
        let notify = (req.body.notify || false);
        let induction_request = await sails.models.inductionrequest.findOne({
            select: ['project_ref', 'user_ref', 'status_code', 'record_id', 'comments'],
            where: {
                id: inductionRequestId,
                project_ref: project_id,
            }
        }).populate('user_ref').populate('project_ref');
        if (!induction_request || !induction_request.id) {
            return ResponseService.errorResponse(res, 'induction record not found');
        }
        sails.log.info('Update induction-request:', inductionRequestId, `from: ${from_state} to: ${override_details.status_code}, notify: ${notify}`);
        let updated_induction = await sails.models.inductionrequest.updateOne({id: inductionRequestId})
            .set({
                comments: [
                    ...(induction_request.comments || []),
                    override_details.comment
                ],
                status_code: override_details.status_code
            });
        let project = induction_request.project_ref || {};
        let affected_user = induction_request.user_ref || {};
        affected_user.name = getUserFullName(affected_user);

        if (override_details.status_code === STATUS_CODES.BLACKLISTED) {
            sails.log.info('Notifying CA about company block of user:', updated_induction.user_ref);
            // send email to all company admins of this project
            let {employer: contractor} = await TokenUtil.getCompanyInfo(project);

            let companyAdmins = await TokenUtil.allResourceAdminsWithOneOfDesignations(contractor.id, ['nominated'], true, TokenUtil.ROLES.COMPANY_ADMIN);
            sails.log.info('total company admins to notify, count:', companyAdmins.length);

            for (let i = 0, len = companyAdmins.length; i < len; i++) {
                let companyAdmin = companyAdmins[i].user_ref || {};
                companyAdmin.name = getUserFullName(companyAdmin);
                try {
                    let subject = `Company block request`;
                    let html = await sails.renderView('pages/mail/mail-content', {
                        title: subject,
                        mail_body: 'user_blacklisted-email',
                        comment: override_details.comment,
                        affected_user,
                        project,
                        recipient: companyAdmin,
                        layout: false
                    });
                    await EmailService.sendMail(subject, [companyAdmin.email], html);
                } catch (e) {
                    sails.log.info('Unable to send email to company admin about blacklisting', e);
                }
            }

            // Block all other related inductions of same user, for same company projects
            await updateAllInheritedInductionState(STATUS_CODES.APPROVED, STATUS_CODES.BLACKLISTED, contractor.name, updated_induction.user_ref, override_details.comment);
        }else if(induction_request.status_code === STATUS_CODES.BLACKLISTED && notify){
            sails.log.info(`Notifying site admin: ${admin_id} about unblocking user: `, updated_induction.user_ref);

            try {
                let site_admin = await sails.models.user_reader.findOne({id: admin_id, is_active: 1});
                site_admin.name = getUserFullName(site_admin);
                let subject = `Users access reinstated`;
                let html = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'user_unblocked-email',
                    comment: override_details.comment,
                    affected_user,
                    project,
                    recipient: site_admin,
                    layout: false
                });
                await EmailService.sendMail(subject, [site_admin.email], html);
            } catch (e) {
                sails.log.info('Unable to send email to company admin about blacklisting', e);
            }
            // Unblock all other related inductions of same user, for same company projects
            await updateAllInheritedInductionState([STATUS_CODES.BLACKLISTED], STATUS_CODES.APPROVED, project.contractor, updated_induction.user_ref, override_details.comment);
        }
        return ResponseService.successResponse(res, {
            induction: updated_induction,
        });
    },

    getBlackListedUserByCompany: async (req, res) => {
        let employer_id = +req.param('companyId', 0);
        sails.log.info('get blacklisted users, in all projects of company:', employer_id);
        let projects = await getCompanyAccessibleProjects(employer_id);
        let induction_requests = await sails.models.inductionrequest_reader.find({
            where: {
                status_code: STATUS_CODES.BLACKLISTED,
                project_ref: projects.map(p => p.id)
            },
            select: ['record_id', 'user_ref', 'additional_data', 'status_code', 'comments']
        });

        sails.log.info(`Found ${induction_requests.length} blacklisted inductions in projects of "${employer_id}" company`);
        return ResponseService.successResponse(res, {
            blacklisted_users: induction_requests.map(induction_data => {
                let {additional_data: {user_info, employment_detail, project}} = induction_data;
                let black_listing_comment = (induction_data.comments || [])
                    .filter(c => (c.origin === 'admin' && c.module === 'access-change') || (c.origin === 'system' && c.module === `conduct-card-assign`))
                    .sort((a, b) => a.timestamp - b.timestamp).pop() || {};
                return {
                    user_id: induction_data.user_ref,
                    name: getUserFullName(user_info),
                    first_name: user_info.first_name,
                    middle_name: user_info.middle_name,
                    last_name: user_info.last_name,
                    status_code: induction_data.status_code,
                    employer: employment_detail && employment_detail.employer,
                    blacklisted_on: black_listing_comment.timestamp,
                    note: black_listing_comment.note,
                    admin_name: black_listing_comment.name,
                    admin_id: black_listing_comment.user_id,
                    project_id: project && project.id,
                    project_name: project && project.name,
                    induction_id: induction_data.id,
                    record_id: induction_data.record_id,
                    comments: induction_data.comments,
                };
            })
        });
    },

    getProjectsAndCompaniesAsSeparateLists: async(req, res) => {
        let userId = req.user.id;
        let defaultRes = { userId, projectsList:[], companiesList:[] };
        let projectsList = await getProjectsAndCompaniesAsSeparateListsFn(userId,'project');
        let companiesList = [];
        if(projectsList.length){
            companiesList = await getProjectsAndCompaniesAsSeparateListsFn(userId,'company');
        }

        ResponseService.successResponse(res,{...defaultRes, projectsList, companiesList});
    },

    updateInductionWithNewCompetencies: async (req, res) => {
        const user_ref = req.user.id;
        sails.log.info(`updating user inductions with new docs, user: ${user_ref}`);

        let document_filter = {
            doc_owner_id: user_ref,
            parent_doc_ref: null, // get only parent docs
            is_deleted : { '!=': 1 },
            expiry_date: { '>': moment().valueOf() },
        };
        let user_docs = await sails.models.userdoc_reader.find(document_filter);
        user_docs = await populateDocumentChildren(user_docs, document_filter);
        user_docs = await (expandUserDocFiles(user_docs, (expandErr, expandedList) =>  expandedList || user_docs));

        let verifiable_user_documents = await getUserVerifiableUserDocuments(user_docs, user_ref, req.user.country_code);

        let result = await updateUserInductionsWithProfileChanges(req.user, ['doc'], {user_docs}, {verifiable_documents: verifiable_user_documents});

        return ResponseService.successResponse(res, result);
    },

    getProjectInductionsUsersEmployer: async (req, res) => {
        let status_code = +(req.param('status_code') || 2); //2 for approved only
        let project_id = +req.param('projectId', 0);
        if (!project_id) {
            return ResponseService.errorResponse(res, 'project id is required.');
        }
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                    additional_data->'user_info'->'parent_company' as user_employer
                FROM induction_request
                WHERE
                    status_code = $1 AND
                    project_ref = $2`,
            [status_code, project_id]
        );

        let employerIds = [];
        let users_employer = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            employerIds = (rawResult.rows || []).reduce((arr, row) => {
                if (row.user_employer && (typeof row.user_employer === "number")) {
                    arr.push(row.user_employer)
                } else if (row.user_employer && (typeof row.user_employer === "object") && row.user_employer.id) {
                    arr.push(row.user_employer.id);
                }
                return arr;
            }, []);

            users_employer = await sails.models.createemployer.find({
                where: {id: _.uniq(employerIds)},
                select: ['id', 'name']
            }).sort([
                {name: 'ASC'},
            ]);
        }

        sails.log.info(`Found ${users_employer.length} Companies of the users who have had an approved induction on the project ${project_id}.`);
        return ResponseService.successResponse(res, {users_employer});
    },

    downloadInductionRecords: async (req, res) => {
        let projectId = (req.body.projectId || '');
        let employers_filter = (req.body.employers_filter || []);
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let nowMs = req.body.nowMs;
        // let timezone = (req.body.tz || 'UTC');
        let embedRequest = (req.body.embed === 'frame');

        let target_date = isNaN(nowMs) ? moment() : moment(nowMs);

        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }

        sails.log.info(`fetch all inductions from ${fromDate} to ${toDate} and filter with companies: `, employers_filter);
        let allInductionRequest = [];
        let variables = [projectId, fromDate, toDate];
        if (employers_filter.length) {
            variables.push(...employers_filter);
        }
        let variableIndex = 0;
        let sql = `SELECT ir.*,
                          (CASE WHEN u1.id IS NOT NULL THEN json_build_object('id', u1.id, 'first_name', u1.first_name, 'middle_name', u1.middle_name, 'last_name', u1.last_name, 'name', CONCAT(u1.first_name, ' ', u1.last_name)) ELSE NULL END) as inductor_ref,
                          COALESCE(
                              ARRAY_AGG(
                                  (CASE WHEN lmd.id IS NOT NULL THEN JSON_BUILD_OBJECT('id', lmd.id, 'medication_name', lmd.medication_name, 'medication_dosage', lmd.medication_dosage, 'medication_frequency', lmd.medication_frequency, 'medication_date_commenced', lmd.medication_date_commenced, 'infinite_completion', lmd.infinite_completion, 'medication_date_of_completion', lmd.medication_date_of_completion)  END)
                              ), ARRAY[]::JSON[]
                          ) as medications

                              FROM induction_request ir
                               LEFT JOIN users u1 ON u1.id = ir.inductor_ref
                               LEFT JOIN long_medication_detail lmd ON lmd.induction_ref = ir.id
                              WHERE ir.project_ref = ${`$${++variableIndex}`}
                                AND ir."createdAt" >= ${`$${++variableIndex}`}
                                AND ir."createdAt" <= ${`$${++variableIndex}`}
                                ${((employers_filter.length) ? `AND additional_data->'employment_detail'->> 'employer' IN (${employers_filter.map(() => `$${++variableIndex}`).join(',')})` : '')}
                   GROUP BY ir.id, u1.id ORDER BY ir.id DESC`;
        sails.log.info(`fetch all inductions from ${sql}`);
        let inductionRequestsResult = await sails.sendNativeQuery(sql, variables);
        if (HttpService.typeOf(inductionRequestsResult.rows, 'array') && inductionRequestsResult.rows.length) {
            allInductionRequest = inductionRequestsResult.rows;
        }

        sails.log.info(`Found ${inductionRequestsResult.rows.length} induction request to download.`);

        if (allInductionRequest.length) {
            fromDate = moment(req.body.fromDate).format('DD-MM-YYYY');
            toDate = moment(req.body.toDate).format('DD-MM-YYYY');
            let outputFileName = `Induction-Request-Report-[${fromDate}-${toDate}]`;
            let featureName = 'induction-request';
            let masterFileDir = `${featureName}/pdf/${moment().add(10, 'seconds').valueOf()}`;
            let masterFilePath = `${masterFileDir}/${outputFileName}.json`;
            let childFilePath = `${featureName}/json/${moment().valueOf()}`;
            let pdfFilesPath = [];
            for (let index in allInductionRequest) {
                let induction_request = allInductionRequest[index];
                induction_request.medications = induction_request.medications.filter(med => med);
                let filename = `${induction_request.additional_data.project.custom_field.induction_phrase_singlr}-Request-Report-${induction_request.record_id}`;
                if(induction_request.additional_data) {
                    let name = [];
                    if (induction_request.additional_data.project && induction_request.additional_data.project.name) {
                        name.push(induction_request.additional_data.project.name);
                    }
                    if (induction_request.record_id) {
                        name.push(induction_request.record_id);
                    }
                    if (induction_request.additional_data.user_info) {
                        let full_name = getUserFullName(induction_request.additional_data.user_info);
                        name.push(encodeURI(full_name.replace(/ /g, '-')));
                    }
                    if (induction_request.additional_data.employment_detail) {
                        name.push(induction_request.additional_data.employment_detail.employer);
                    }
                    filename = name.join(' - ');
                }

                pdfFilesPath.push(`${filename}.pdf`);
            }

            let {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts} = await inductionDownloadMetaData(projectId);

            let mail_style = await sails.renderView('pages/mail-style', {layout: false});
            let mail_footer = await sails.renderView('pages/mail/mail-footer', {layout: false});
            // json data
            let jsonObj = {
                masterFileDir,
                "date_range": `${fromDate} - ${toDate}`,
                "feature_name": featureName,
                "email_subject": `innDex: ${outputFileName}`,
                "email_body": `Please click on the link to get ${project.custom_field.induction_phrase_singlr} request reports from ${fromDate} to ${toDate}: `,
                mail_style,
                mail_footer,
                "sender_email": `innDex${(process.env.APP_ENV !== 'production') ? ` ${process.env.APP_ENV}` : ''} <${sails.config.custom.SOURCE_MAIL_ADDRESS}>`,
                "receiver_email": req.user.email,
                "receiver_id": req.user.id,
                "receiver_name": getUserFullName(req.user),
                "output_file_name": outputFileName,
                pdfFilesPath
            };

            // stringify JSON Object
            let s3UploadResponse = await s3UploaderWithExpiry(masterFilePath, JSON.stringify(jsonObj), 'application/json', 'public-read', 1);
            sails.log.info(`Uploaded file ${s3UploadResponse.key || s3UploadResponse.Key} on s3 in bucket ${s3UploadResponse.Bucket}.`);

            for (let index in allInductionRequest) {
                let induction_request = allInductionRequest[index];
                let html = await processViewAndDownloadInductionRequests(req, res, induction_request, 'html', target_date, embedRequest, {project, timezone, exclusion_setting, contractor, cscs_check_enabled, project_logo_file, medication_disabled_projects, projectDistricts});

                let filename = `${induction_request.additional_data.project.custom_field.induction_phrase_singlr}-Request-Report-${induction_request.record_id}`;
                if(induction_request.additional_data) {
                    let name = [];
                    if (induction_request.additional_data.project && induction_request.additional_data.project.name) {
                        name.push(induction_request.additional_data.project.name);
                    }
                    if (induction_request.record_id) {
                        name.push(induction_request.record_id);
                    }
                    if (induction_request.additional_data.user_info) {
                        let full_name = getUserFullName(induction_request.additional_data.user_info);
                        name.push(encodeURI(full_name.replace(/ /g, '-')));
                    }
                    if (induction_request.additional_data.employment_detail) {
                        name.push(induction_request.additional_data.employment_detail.employer);
                    }
                    filename = name.join(' - ');
                }
                let jsonContent = JSON.stringify({html, featureName, pdfFileName: `${filename}.pdf`, masterFileDir, masterFilePath});
                await s3UploaderWithExpiry(`${childFilePath}/${filename}.json`, jsonContent, 'application/json', 'public-read', 1);
            }
            sails.log.info(`Processed ${allInductionRequest.length} induction request records.`);
            return ResponseService.successResponse(res, {message: `${allInductionRequest.length} ${project.custom_field.induction_phrase_singlr} request reports will be sent to your email address ${req.user.email}`});
        }

        sails.log.info('No record found between selected duration.');
        return ResponseService.successResponse(res, {message: 'No record found between selected duration.'});
    },

    downloadQRPoster: async (req, res) => {
        let projectId = req.param('projectId');
        let companyId = req.param('companyId');

        let projectInfo = await sails.models.project.findOne({
            where: { id: projectId },
            select: ['name', 'project_initial', 'use_prefix', 'custom_field', 'contractor']
        });

        let qrConfig = await sails.models.companysetting_reader.findOne({
            name: COMPANY_SETTING_KEY.QR_CODE_CONFIG,
            company_ref: companyId,
        });

        if (projectInfo && qrConfig) {
            let form_template = `pages/qr-poster/qr-poster`;
            sails.log.info(`using template path: "${form_template}"`);

            let projectQRPosterId = projectInfo.id;
            if (projectInfo.project_initial && projectInfo.use_prefix) {
                projectQRPosterId = `${projectInfo.project_initial.toString().trim().toUpperCase()}${projectInfo.id}`
            }

            let qr_data = await QRCode.toDataURL(`https://www.inndex.co.uk/app?p=${projectQRPosterId}&t=scan`, {
                color: {
                    dark: qrConfig.value.font_color,
                    light: qrConfig.value.background_color
                },
                margin: 0,
                width: 165,
                height: 165
            })

            let html = await sails.renderView(form_template, {
                title: 'QR Poster',
                projectId: projectQRPosterId,
                projectName: projectInfo.name,
                qrCodeConfig: qrConfig.value,
                qrCode: qr_data,
                inductionPhrase: projectInfo.custom_field.induction_phrase_singlr,
                layout: false
            });

            const fileName = `${projectInfo.custom_field.induction_phrase_singlr} Poster-${projectInfo.contractor}-${projectQRPosterId}-${projectInfo.name}`

            return await instantPdfGenerator(req, res, html, 'qr-code', fileName, req.headers['user-agent'], { format: 'A4' }, "url");
        }
        return ResponseService.successResponse(res, {message: 'No QR poster found.'});
    },

    /**
     * The API is returning RAMS, Toolbox Talks, Task Briefings, WPP received by user
     * */
    getReceivedBriefings: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');

        sails.log.info(`Fetching induction received briefings of user.`);
        let ramsRecords = await sails.models.projectrams_reader.find({
            where: {
                or: [{project_ref: projectId}, {project_ref: null}],
            },
            select: ['briefing_title', 'project_ref']
        });

        let toolboxTalkRecords = await sails.models.toolboxtalks_reader.find({
            where: {
                or: [{project_ref: projectId}, {project_ref: null}],
            },
            select: ['talk_title', 'project_ref', 'company_ref']
        });

        let taskBriefingsRecords = await sails.models.projecttaskbriefings_reader.find({
            where: {
                or: [{project_ref: projectId}, {project_ref: null}],
            },
            select: ['briefing_title', 'project_ref']
        });

        let wppRecords = await sails.models.projectworkpackageplans_reader.find({
            where: {
                or: [{project_ref: projectId}, {project_ref: null}],
            },
            select: ['briefing_title', 'project_ref']
        });
        let {items: ramsItems, briefedByUserIds: ramsBriefedByUserIds} = await prepareReceivedBriefingsItems(ramsRecords, projectId, userId, 'briefing_title', 'rams');
        let {items: toolboxTalksItems, briefedByUserIds: ttBriefedByUserIds} = await prepareReceivedBriefingsItems(toolboxTalkRecords, projectId, userId, 'talk_title', 'toolbox_talks');
        let {items: taskBriefingsItems, briefedByUserIds: tbBriefedByUserIds} = await prepareReceivedBriefingsItems(taskBriefingsRecords, projectId, userId, 'briefing_title', 'task_briefings');
        let {items: wppItems, briefedByUserIds: wppBriefedByUserIds} = await prepareReceivedBriefingsItems(wppRecords, projectId, userId, 'briefing_title', 'work_package_plan');

        let userInfo = await sails.models.user_reader.find({
            select: ['first_name', 'last_name'],
            where:{
                id: _uniq([...ramsBriefedByUserIds, ...ttBriefedByUserIds, ...tbBriefedByUserIds, ...wppBriefedByUserIds])
            }
        });

        ramsItems = associateUserToBriefingItems(ramsItems, userInfo);
        sails.log.info(`Found ${ramsItems.length} RAMS records.`);

        toolboxTalksItems = associateUserToBriefingItems(toolboxTalksItems, userInfo);
        sails.log.info(`Found ${toolboxTalksItems.length} toolbox talks records.`);

        taskBriefingsItems = associateUserToBriefingItems(taskBriefingsItems, userInfo);
        sails.log.info(`Found ${taskBriefingsItems.length} task briefings records.`);

        wppItems = associateUserToBriefingItems(wppItems, userInfo);
        sails.log.info(`Found ${wppItems.length} wpp records.`);

        return ResponseService.successResponse(res, {ramsItems, toolboxTalksItems, taskBriefingsItems, wppItems});
    },

    /**
     * get all inductions of given Project
     *  Supports Pagination, search, sorting, filtering.
     *
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    projectInductionsListSA: async (req, res) => {
        return await projectInductionsList(req, res);
    },
    getInductionEmployersSA: getInductionEmployers,

    getInductionJobRolesSA: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let defaultStatusCodes = ''; // Allow all by default
        let statusCodes = (req.param('statusCodes', defaultStatusCodes)).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let transform = req.param('transform', 'true') === 'true';
        let job_roles = await getInductionUserJobRoles(projectId, {statusCodes}, transform);
        return ResponseService.successResponse(res, {
            projectId,
            statusCodes,
            list: job_roles,
        });
    },

    getInductionsListInnTime: async (req, res) => {
        return await projectInductionsList(req, res, req.project.id);
    },

    projectInductionsListCA: async (req, res) => {
        return await projectInductionsList(req, res);
    },
    getInductionEmployersCA: getInductionEmployers,

    getActiveUsersTimesheetList: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let pageSize = +req.param('pageSize', 50);
        let pageNumber = +req.param('pageNumber', 0);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'asc');
        let searchTerm = (req.param('q', '')).toString().trim();
        let statusCodes = (req.param('statusCodes', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let employer = req.param('employer', '').split(',').filter(s => s !== "");
        let jobRole = req.param('jobRole', '').split(',').filter(s => s !== "");
        let extraColumns = req.param('extra', '').split(',');
        let from_date = dayjs(req.param('from_date', '--'), dbDateFormat_YYYY_MM_DD);
        let to_date = dayjs(req.param('to_date', '--'), dbDateFormat_YYYY_MM_DD);

        if (
            !projectId ||
            !from_date.isValid() ||
            !to_date.isValid()
        ) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid date filter provided', {from_date, to_date});
        }
        sails.log.info(`filter project:${projectId}, q:"${searchTerm}", employer: "${employer}", jobRole: "${jobRole}"`);
        let {
            daily_logs,
            userIds,
            timesheets,
            weekly_timesheets
        } = await getDailyLogsForTimesheets(projectId, {from_date, to_date});

        let defaultResponse = {
            records: [],
            q: searchTerm,
            statusCodes,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            totalCount: 0,
            daily_logs: [],
            timesheets: [],
            weekly_timesheets:[]
        };
        if(!userIds.length){
            return ResponseService.successResponse(res, defaultResponse);
        }
        let additionalCols = [];
        if(extraColumns.includes('name')){
            additionalCols.push(...[
                `additional_data->'user_info'->>'first_name' as first_name`,
                `additional_data->'user_info'->>'last_name' as last_name`,
            ]);
        }
        if(extraColumns.includes('employment')){
            additionalCols.push(...[
                `additional_data -> 'employment_detail' ->> 'employer' as employer_name`,
                `additional_data -> 'employment_detail' ->> 'job_role' as job_role`,
                `additional_data->'employment_detail'->>'type_of_employment' as type_of_employment`,
                `additional_data->'employment_detail'->>'employment_company' as employment_company`,
                // `travel_time`,
            ]);
        }
        if(searchTerm){
            searchTerm = decodeURIParam(searchTerm);
        }
        let {
            total: totalCount,
            records
        } = await getProjectInductionsPage(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
            searchTerm, statusCodes, employer, jobRole, userIds
        }, additionalCols);

        return ResponseService.successResponse(res, {
            ...defaultResponse,
            records,
            totalCount,
            daily_logs,
            timesheets,
            weekly_timesheets
        });
    },

    getProjectInductionsWithOnSiteStatus: async (req, res) => {
        let nowMs = +req.param('nowMs', 0);
        let projectId = req.project.id;

        if (!projectId || !nowMs) {
            return ResponseService.errorResponse(res, 'project id & nowMs is required');
        }
        let additionalCols = [
            `status_code`,
            `additional_data -> 'user_info' -> 'profile_pic_ref'   as profile_pic_ref`,
            `additional_data -> 'employment_detail' ->> 'employer' as employer`,
            `additional_data -> 'employment_detail' ->> 'job_role' as job_role`
        ];

        let statusCodes = [2,6];
        sails.log.info(`getAllInductedUsers projectId: ${projectId}, nowMs: ${nowMs}, filter.status: ${statusCodes || '-'}, extra: pic, employer}`);
        let {
            records
        } = await getProjectInductions(projectId, {
            statusCodes,
            limit: -1,
        }, additionalCols);

        let userRefs = (records || []).map(ir => +ir.user_ref);

        let time_log_for_day = await getDailyTimeEventForDay(projectId, moment(nowMs), userRefs);
        let i = 0;
        records = records.map(record => {
            let dailyLog = time_log_for_day.find(ou => +ou.user_id === +record.user_ref && isStillOnSite(ou));
            record.on_site = (dailyLog) ? true : false;
            i += (record.on_site);
            return record;
        });
        sails.log.info(`Got ${records.length} induction requests on projectId ${projectId} with ${i} is on site.`);

        return ResponseService.successResponse(res, {
            induction_requests: records
        });
    },

    getUserTimesheetDetails: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let from_date = dayjs(req.param('from_date', '--'), dbDateFormat_YYYY_MM_DD);
        let to_date = dayjs(req.param('to_date', '--'), dbDateFormat_YYYY_MM_DD);
        let userId = +req.param('userId', 0);

        if (
            !projectId ||
            !userId ||
            !from_date.isValid() ||
            !to_date.isValid()
        ) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid date filter provided', {from_date, to_date});
        }

        let {
            daily_logs,
            timesheets,
            weekly_timesheets
        } = await getDailyLogsForTimesheets(projectId, {from_date, to_date}, [userId], true);

        return ResponseService.successResponse(res, {
            daily_logs,
            timesheets,
            weekly_timesheets
        });
    },

    getProjectInductionById: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let extraColumns = req.param('extra', '').split(',');
        sails.log.info(`get project:${projectId} induction record, id:${inductionRequestId}, caller: ${req.user.id} extra: ${extraColumns}`);
        let induction_request = await sails.models.inductionrequest_reader
            .findOne({
                id: inductionRequestId,
                project_ref: projectId,
            }).populate('medications')
            //.populate('user_ref')
            .populate('inductor_ref');
        if (extraColumns.includes('uac') && induction_request) {
            const user_uac = await getProjectUsers(projectId, [induction_request.user_ref]);
            induction_request.uac = (user_uac || []).pop() || null;
        }
        [induction_request] = await expandUserDocs([induction_request]);

        let projectDistricts = await getProjectDistrict(projectId);
        induction_request.is_local_worker = (projectDistricts.length && induction_request.district && projectDistricts.includes(induction_request.district)) ? 'Yes': 'No';
        induction_request.conduct_cards =  await getConductCardsOfUsers(projectId, [], induction_request.id);
        return ResponseService.successResponse(res, {induction_request});
    },

    downloadInductionRecordsXLSX: prepareInductionRecordExcel,

    downloadInductionRecordsXlsxCA: prepareInductionRecordExcel,

    getHeatMapDataCA:async(req,res)=>{
        return getHeatMapData(req,res)
    },
    getHeatMapDataSA:async(req,res)=>{
        return getHeatMapData(req,res)
    },
    getHeatMapDataLiveTv:async(req,res)=>{
        return getHeatMapData(req,res)
    },

    downloadCompanyInductionsXLSX: async (req, res) => {
        let employerId = (req.body.employerId || '');
        // let employers_filter = (req.body.employers_filter || []);
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let timezone = (req.body.tz || 'UTC');

        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }
        sails.log.info(`fetch all inductions from ${fromDate} to ${toDate}`);
        let allInductionRequest = await sails.models.companyinduction_reader.find({
            where: {
                createdAt: {'>=': fromDate, '<=': toDate},
                company_ref: employerId
            },
            select: ['*'],
            sort: ['id DESC']
            });
        allInductionRequest = await populateUserRefs(allInductionRequest, 'user_ref', []);
        allInductionRequest = await populateEmployerRefs(allInductionRequest, 'company_ref', []);

        sails.log.info(`found ${allInductionRequest.length} induction requests.`);

        allInductionRequest = allInductionRequest.map(r => {
            r = buildCompanyInductionStatus(r);
            return r;
        });

        let companyInductionSetting  = await sails.models.companysetting_reader.findOne({
            where: {
                company_ref: employerId,
                name: COMPANY_SETTING_KEY.COMPANY_INDUCTION,
            }
        });
        let companyInctPhrasing = (companyInductionSetting.value.phrasing && companyInductionSetting.value.phrasing.singlr) || 'Company Induction';
        let workbook = await getCompanyInductionsWorkbook(allInductionRequest, companyInctPhrasing, timezone);
        let fileName = `${companyInctPhrasing} Requests.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    getProjectInductedAdmins: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'asc');
        let searchTerm = decodeURIParam((req.param('q', '')).toString().trim());
        let designations = ['other', 'restricted', 'delivery_management', 'custom']; //req.param('designations') ? req.param('designations').split(',') : ['other'];
        let statusCodes = [2, 6];

        let {
            total: totalCount,
            records
        } = await getInductedAdmins(sortKey, sortDir, {searchTerm, projectId, designations, statusCodes});

        sails.log.info(`${totalCount} admins are inducted on project ${projectId}`);

        return ResponseService.successResponse(res, {
            records,
            q: searchTerm,
            sortKey,
            sortDir,
            totalCount,
        });
    },
};
