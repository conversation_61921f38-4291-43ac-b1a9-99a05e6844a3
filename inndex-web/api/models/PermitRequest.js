const buildStatusLabel = TokenUtil.getPermitRequestStatusLabel;

module.exports = {
    tableName: 'permit_request',

    attributes: {
        record_id:{
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        permit_ref: {
            model: 'permittemplate'
        },
        config_ref: {
            model: 'projectpermitconfig'
        },
        project_ref: {
            model: 'project'
        },
        requestor_ref: {
            model: 'user'
        },
        mandatory_attachments: {
            type: 'json',
            defaultsTo: []
        },
        pdf_fields_value: {
            type: 'json',
            defaultsTo: []
        },
        signatures: {
            type: 'json',
            defaultsTo: []
        },
        start_on: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        expire_on: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        state: {
            type: 'number',
            allowNull: true
        },
        status: {
            type: 'number',
            defaultsTo: 0
        },
        status_logs: {
            type: 'json',
            defaultsTo: []
        },
        closeout_requested: {
            type: 'boolean',
            defaultsTo: false
        },
        has_register: {
            type: 'boolean',
            defaultsTo: false
        },
        requestor_company: {
            type: 'string',
            allowNull: true
        },
        tags: {
            type: 'string',
            allowNull: true
        },
    },

    //attributes methods
    customToJSON: function () {
        if (this.status == null) {
            this.status = 1;
        }
        this.status_meta = buildStatusLabel(this.status);
        return _.omit(this, []);
    },

    beforeCreate: function (values, cb) {
        // get next value for record id of this project
        // else use baseValue + 1
        const baseValue = 0;

        sails.models.permitrequest_reader.find({
            where: {project_ref: values.project_ref},
            select: ['id', 'record_id'],
            limit: 1,
            sort: ['id DESC', 'record_id DESC']
        })
            .exec(function(e,rows){
                if(e){
                    sails.log.info('Error is', e);
                    return cb(e);
                }
                let row = {};
                if(rows && rows.length){
                    row = rows[0];
                }
                let next_record_id =  parseInt(row.record_id ? row.record_id : baseValue) + 1;
                sails.log.info('next_record_id will be', row, next_record_id);
                values.record_id = next_record_id;
                cb();
            });
    },

    afterUpdate: function(values, cb){
        values.status_meta = buildStatusLabel(values.status);
        cb();
    }
}
