/**
 * Created by spatel on 10/3/19.
 */

const dbDateFormat = 'YYYY-MM-DD';
const momentTz = require('moment-timezone');
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const ResponseService = require('./../services/ResponseService');
const HttpService = require('./../services/HttpService');
const {
    TimeLogValidator,
} = require('./../validators');
const {
    OptimaSyncService: {
        eventListener,
        callOptimaBoxGateway,
        getOptimaConnectionStatus,
        getAllReadersList,
        importOptimaLogs,
        createBadge,
        updateBadge,
        deleteBadge,
        getRemoteEnrolment,
        remoteEnrolFace,
    },
    HttpService: {
        resizeOrCropImageToFace,
    },
    CacheService,
    RekognitoService: {
        detectFaces,
        triggerCreateCollection,
    },
    AccessLogService,
    UserRevisionService: {getLatestUserRevision},
    ExcelService: {
        getProjectEmissionsReportWorkbook,
        badgeEventExcel,
        streamExcelDownload
    }
} = require('./../services');
const moment = require('moment');
const _get = _.get;
const _uniq = require('lodash/uniq');
const _groupBy = _.groupBy;
const {
    getActiveTravelTime,
    convertDecimalHrToDuration,
    expandUserDocs,
    sendInductionStatusChangeAlert,
    getDailyBadgeEvent,
    getDailyGeoFenceTimeEvent,
    getDailyTimeEventV2,
    getVisitorsTimeLogForDates,
    getDailyTimeEventForDay,
    getAllDailyTimeEvent,
    sortBadgeLogsByDay,
    eventOfToday,
    isStillOnSite,
    getFilteredInductionBySearch,
    getUserFullName,
    isFirstAider,
    haveSMSTSOrSSSTSDoc,
    haveFireMarshal,
    populateUserRefs,
    populateJobRoles,
    populateEmployerRefs,
    attachProfilePicWithUsersInfo,
    populateProjectRefs,
    getProjectTimezone,
    deriveOnSiteUsersLocation,
    getCompetencyMappings,
} = require('./../services/DataProcessingService');

const {
    ingestTimeLogs,
    getShiftConfigOfUser,
    reActOnShiftConfigChange,
    expandDailyLogInfo,
    EVENT_TYPE,
    VALID_SOURCES,
    reIndexTimeLogs,
    determineEventType
} = require('./../services/TimeLogService');
const {inductionFn} = require("./../sql.fn");
const {
    DEFAULT_PAGE_SIZE,
    fall_back_timezone,
    COMPANY_SETTING_KEY,
    isShadowUser,
} = sails.config.constants;
const cut_off_hour = (isNaN(sails.config.custom.CUT_OFF_HOUR) ?  0 : sails.config.custom.CUT_OFF_HOUR);

const cleanOptimaSettingFields = (data) => {
    if (data.project_ref && data.project_ref.id) {
        data.project_ref = data.project_ref.id;
    }

    if(data.site_id){
        // removing unwanted spaces
        data.site_id = (data.site_id).toString().trim().toLowerCase();
    }

    if(data.geo_fence_locations && data.geo_fence_locations.length === 0){
        // don't insert empty array, keep it null
        data.geo_fence_locations = null;
    }else if(data.geo_fence_locations){
        data.geo_fence_locations = data.geo_fence_locations.map(loc => ({
            lat: +loc.lat,
            long: +loc.long,
            radius: +loc.radius,
            name: loc.name,
        }));
    }
    return data;
};

const CARBON_EMITTING_TRAVEL_METHOD = {
    CAR_DRIVER: "Car (Driver)",
    CAR_VAN_DRIVER: "Car/Van (Driver)",
    BUS: "Bus",
    MOTORBIKE: "Motorbike",
    TRAIN: "Train"
};
const DISTANCE_TRAVELLED_COUNT_FOR = [
    "Car (Driver)",
    "Car/Van (Driver)"
];
const meterToMile = 0.*********;

const CARBON_EMITTING_TRAVEL_METHOD_LABEL = Object.values(CARBON_EMITTING_TRAVEL_METHOD);

const getCarbonFootprint = (travel_method, matrix, vehicleInfo = {}) => {
    let tm = (travel_method || '').toString().trim();
    let outcome = {
        distance_in_meter: 0,
        emission: 0
    };
    if(CARBON_EMITTING_TRAVEL_METHOD_LABEL.includes(tm) && matrix && matrix.distance && matrix.distance.value){

        outcome.distance_in_meter = matrix.distance.value;
        if(vehicleInfo && +vehicleInfo.co2Emissions){
            // use DVLA Co2 Emission value
            // return kg emission
            outcome.emission = (
                (vehicleInfo.co2Emissions / 1000) *     // Emission in kg/km
                (matrix.distance.value / 1000)          // distance into km
            );
        }
        // Car (Driver): 0.2842102kg/mile
        // Bus: 0.********* kg/mile/passenger
        // Motorbike: 0.418 kg/mile/passenger
        // Train: 0.07242048 kg/mile/passenger
        else if(tm === CARBON_EMITTING_TRAVEL_METHOD.CAR_DRIVER || tm === CARBON_EMITTING_TRAVEL_METHOD.CAR_VAN_DRIVER){
            outcome.emission = (matrix.distance.value * meterToMile * 0.2842102);
        }
        else if(tm === CARBON_EMITTING_TRAVEL_METHOD.BUS){
            outcome.emission = (matrix.distance.value * meterToMile * 0.*********);
        }
        else if(tm === CARBON_EMITTING_TRAVEL_METHOD.MOTORBIKE){
            outcome.emission = (matrix.distance.value * meterToMile * 0.418);
        }
        else if(tm === CARBON_EMITTING_TRAVEL_METHOD.TRAIN){
            outcome.emission = (matrix.distance.value * meterToMile * 0.07242048);
        }

    }
    return outcome;
};

const buildDailyCarbonFootprintSetV2 = async (projectId, inductions = [], daily_logs = [], unique_user_ids = [], totalOnly = false) => {
    try {
        sails.log.info('Build daily carbon footprint set');

        let user_induction = unique_user_ids.reduce((o, id) => {
            o[id] = inductions.find(ir => +ir.user_ref === +id) || {};
            return o;
        }, {});

        let daily_logs_by_user = _groupBy(daily_logs, (l) => l.day_of_yr);

        let incremental_sum = 0;
        let incremental_distance = 0;
        return Object.keys(daily_logs_by_user).reduce((o, date) => {
            let daily_sum = 0;
            let daily_distance = 0;
            o[date] = daily_logs_by_user[date].map(log => {

                let travelTimeOverride = getActiveTravelTime(user_induction[log.user_id], moment(date, dbDateFormat));
                let travel_time = travelTimeOverride.travel_time || {};
                let {emission: carbon_A, distance_in_meter: distance_A} = getCarbonFootprint(travelTimeOverride.travel_method, travel_time.to_work_dm, travelTimeOverride.vehicle_info);
                let {emission: carbon_B, distance_in_meter: distance_B} = getCarbonFootprint(travelTimeOverride.travel_method, travel_time.to_home_dm, travelTimeOverride.vehicle_info);
                // this should be counted double, from & to
                daily_sum = daily_sum + carbon_A + carbon_B;
                // we want these travel method distance only
                if(DISTANCE_TRAVELLED_COUNT_FOR.includes(travelTimeOverride.travel_method)){
                    daily_distance = daily_distance + Number(distance_A) + Number(distance_B);
                }
                return log;
            });
            incremental_sum += daily_sum;
            incremental_distance += daily_distance;
            if(totalOnly){
                o[date] = {
                    daily_sum: +daily_sum.toFixed(2),
                    daily_distance: +daily_distance.toFixed(2),
                    incremental_sum: +incremental_sum.toFixed(2),
                    incremental_distance: +incremental_distance.toFixed(2),
                };
            }

            return o;
        }, {});

    } catch (e) {
        sails.log.error('Failed to build carbon footprint sets', e);
        return {};
    }
};

const buildDailyCarbonFootprintByEmployers = async (inductions, daily_logs, unique_user_ids, totalCompanies) => {
    let user_induction = unique_user_ids.reduce((o, id) => {
        o[id] = inductions.find(ir => +ir.user_ref === +id) || {};
        return o;
    }, {});
    let daily_logs_by_user = _groupBy(daily_logs, (l) => l.day_of_yr);
    let companyWiseInductions = _groupBy(inductions, (l) => l.additional_data.employment_detail.employer);
    let incremental_sum = 0;
    let incremental_distance = 0;
    let data = {};
    let p = {};
    totalCompanies.forEach(company => {
        data[company] = [];
        let usersOfCompany = companyWiseInductions[company].map(r=> r.user_ref);
        incremental_sum = 0;
        incremental_distance = 0;
        Object.keys(daily_logs_by_user).reduce((o, date) => {
            p = {}
            let daily_sum = 0;
            let daily_distance = 0;
            daily_logs_by_user[date].forEach(log => {
                if(usersOfCompany.includes(log.user_id)) {
                    //sails.log.info("in object keys", log);
                    let travelTimeOverride = getActiveTravelTime(user_induction[log.user_id], moment(date, dbDateFormat));
                    let travel_time = travelTimeOverride.travel_time || {};
                    let {emission: carbon_A, distance_in_meter: distance_A} = getCarbonFootprint(travelTimeOverride.travel_method, travel_time.to_work_dm, travelTimeOverride.vehicle_info);
                    let {emission: carbon_B, distance_in_meter: distance_B} = getCarbonFootprint(travelTimeOverride.travel_method, travel_time.to_home_dm, travelTimeOverride.vehicle_info);
                    // this should be counted double, from & to
                    daily_sum = daily_sum + carbon_A + carbon_B;
                    // we want these travel method distance only
                    if(DISTANCE_TRAVELLED_COUNT_FOR.includes(travelTimeOverride.travel_method)){
                        daily_distance = daily_distance + Number(distance_A) + Number(distance_B);
                    }
                }
            });
            incremental_sum += daily_sum;
            incremental_distance += daily_distance;
            p = {
                day_of_yr: date,
                daily_sum: +daily_sum.toFixed(2),
                daily_distance: +daily_distance.toFixed(2),
                incremental_sum: +incremental_sum.toFixed(2),
                incremental_distance: +incremental_distance.toFixed(2),
            };
            data[company].push(p);
        });
    });
    return data;
};


const storeTimeLog = async (req, res, includeVisitorRef = false) => {

    const ALLOWED_EVENT_TYPE = ['IN', 'OUT'];

    let log = _.pick((req.body || {}), [
        'project_ref',
        'user_location',
        'event_type',
        'temperature',
        'extras',
        'attachment_ref'
    ]);
    log.event_type = log.event_type.toString().toUpperCase().trim();

    if(!log.project_ref || !log.event_type || !ALLOWED_EVENT_TYPE.includes(log.event_type) || !log.user_location || !log.user_location.lat || !log.user_location.long){
        sails.log.info('Invalid Request');
        return ResponseService.errorResponse(res, 'project_ref, event_type, user_location {lat,long} are required.');
    }

    if(req.body.declarations && HttpService.typeOf(req.body.declarations, 'array') && req.body.declarations.length){
        log.extras = {
            ...(log.extras || {}),
            declarations: req.body.declarations
        };
    }

    log.user_ref = req.user.id;
    log.company_ref = req.user.parent_company;
    log.event_date_time = moment().unix();
    if(includeVisitorRef){
        log.visitor_ref = req.body.visitor_id;
    }else{
        let revision = await getLatestUserRevision(log.user_ref);
        log.user_revision_ref = revision.id;
    }

    sails.log.info('Storing user time log, type:', log.event_type);
    try {
        let newRecord = await sails.models.usertimelog.create(log);
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: log.project_ref},
            select: ['custom_field']
        });
        await ingestTimeLogs([newRecord], projectInfo);
        return ResponseService.successResponse(res, {user_time_log: newRecord, created: true});
    }catch (createError) {
        sails.log.error('Failed to create user time log', createError);
        return ResponseService.errorResponse(res, sails.__('internal server error'), createError);
    }
};


const totalTimeReport = async (req, res) => {
    let projectId = req.param('projectId');
    let from_date = moment((req.query.from_date || '--'), dbDateFormat);
    let to_date = moment((req.query.to_date || '--'), dbDateFormat);
    let badge_ids = (req.query.badge_ids || []);
    if(!HttpService.typeOf(badge_ids, 'array') && !isNaN(+badge_ids)){
        badge_ids = [badge_ids];
    }
    if (!from_date.isValid() || !to_date.isValid()) {
        sails.log.info('Invalid Request');
        return ResponseService.errorResponse(res, 'Invalid from_date filter provided.',{from_date, to_date});
    }
    from_date = from_date.format(dbDateFormat);
    to_date = to_date.add(1, 'days').format(dbDateFormat);
    sails.log.info(`total-time report from, to: ${from_date}  <= date < ${to_date}, badge_ids: ${badge_ids} singleUserOnly: ${req.singleUserOnly}`);
    try {
        let badgeFilter = {};
        if(HttpService.typeOf(badge_ids, 'array') && badge_ids.length){
            badgeFilter = { optima_badge_number: badge_ids.map(id => +id) };
        }
        sails.log.info('Badge Filter:', badgeFilter);
        let where = {
            ...badgeFilter,
            status_code: [2, 4, 5, 6],
            project_ref: projectId
        };
        if(req.singleUserOnly){
            where.user_ref = req.singleUserOnly;
        }
        sails.log.info('Where filter is ', where);
        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'user_ref', 'travel_time', 'travel_method'],
            sort: ['id desc'],
            where
        });

        let user_ids = _uniq(induction_requests.map(ir => ir.user_ref));
        sails.log.info(`Find time records of user_ids count: ${(user_ids || []).length}`);
        if(!user_ids.length){
            // invalid badge id in mobile app request can trigger this
            sails.log.info('No induction record found, for this user', req.singleUserOnly);
            //return ResponseService.errorResponse(res, 'provided badge id not found.',{request: where});
        }

        let onlyTotal = (req.query.only_total || false);
        if(req.singleUserOnly){
            user_ids = [req.singleUserOnly];
        }
        let daily_logs = await getDailyTimeEventV2(projectId, from_date, to_date, user_ids, req.singleUserOnly, null, !onlyTotal, !onlyTotal);

        // Extending API to support project dashboard UI
        if(onlyTotal){
            let visitor_logs = await getVisitorsTimeLogForDates(projectId, from_date, to_date);
            sails.log.info('Compute total time for user records, count:', daily_logs.length);
            sails.log.info('Compute total time for visitor records, count:', visitor_logs.length);
            let default_in_duration = 0;
            if(daily_logs.length){
                let project = (await sails.models.project_reader.findOne({ where: {id: projectId}, select: ['id', 'name', 'default_in_duration']})) || {};

                if(project.default_in_duration && !isNaN(project.default_in_duration)){
                    default_in_duration = convertDecimalHrToDuration(project.default_in_duration);
                }
            }
            let total_operatives = [];
            let total_time = daily_logs.reduce((t, log) => {
                if(!total_operatives.includes(log.user_id)){
                    total_operatives.push(log.user_id);
                }
                let effective_time = (log.effective_time ? +log.effective_time : (default_in_duration + ((log.adjustment || 0) * 60)));
                return (t + effective_time)
            }, 0);

            let total_visitor_time = visitor_logs.reduce((t, log) => {
                let effective_time = (log.effective_time ? +log.effective_time : (default_in_duration + ((log.adjustment || 0) * 60)));
                return (t + effective_time)
            }, 0);

            let daily_footprint = await buildDailyCarbonFootprintSetV2(projectId, induction_requests, daily_logs , total_operatives, true);

            return ResponseService.successResponse(res, {
                total_time: (total_time + total_visitor_time),
                // total_user_time: total_time,
                // total_visitor_time,
                total_operatives,
                // total_logs: daily_logs.length,
                daily_footprint
            });
        }
        else{
            sails.log.error(`============= Shouldn't be logged ======================`);
            sails.log.error('Single user call from:', (req.user && req.user.id), req.path, `Time: ${moment().format()} Origin HOST:`, req.headers['host'], 'UA:', req.headers['user-agent']);
            // request is from download-records UI
            daily_logs = await expandDailyLogInfo(projectId, daily_logs, true, true);

        }

        let userIdVisited = _uniq(daily_logs.map(l => l.user_id));
        sails.log.info(`Got time logs of users: ${userIdVisited.join(',')}`);
        ResponseService.successResponse(res, {
            induction_requests: induction_requests.filter(ir => userIdVisited.includes(ir.user_ref)),
            daily_logs,
        });
    }catch (reportFetchError) {
        sails.log.error('Failed to fetch site time report', reportFetchError);
        return ResponseService.errorResponse(res, sails.__('internal server error'), reportFetchError);
    }

};

const getUserEventByDay = async (req, res, user_id, project_id) => {
    let request_date = moment((req.query.for_date || '--'), dbDateFormat);
    if (!request_date.isValid() || !project_id) {
        sails.log.info('Invalid Request');
        return ResponseService.errorResponse(res, 'Invalid for_date filter provided.',{request_date});
    }
    let day_of_yr_today = request_date.format(dbDateFormat);
    sails.log.info(`get project "${project_id}" user: "${user_id}" event for day: ${day_of_yr_today}`);

    let [log] = await sails.models.userdailylog_reader.find({
        where: {day_of_yr: day_of_yr_today, project_ref: +project_id, user_ref: +user_id},
        select: ['user_ref', 'project_ref', 'day_of_yr', 'first_in', 'recent_in', 'last_out', 'comments', 'adjustment_minutes'],
        sort: ['id DESC'],
        limit: 1
    });

    sails.log.info(`got project "${project_id}" user: "${user_id}" event for day: ${day_of_yr_today} id:`, (log && log.id));
    if(log && log.id){
        log.day_of_yr = moment(log.day_of_yr).format(dbDateFormat);
        log.is_still_on_site = isStillOnSite({
            clock_in: log.first_in,
            recent_in: log.recent_in,
            clock_out: log.last_out,
        });
    }

    ResponseService.successResponse(res, {log});
};

module.exports = {

    testBiometricConnection: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let data = _.pick((req.body || []), [
            'biometric_source',
            'site_id',
            'key'
        ]);
        sails.log.info('Test biometric setting', data.host, 'source:', data.biometric_source, 'site_id:', data.site_id);
        if (!data.site_id) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Biometric setting is required.');
        }
        let api_response = {};
        sails.log.info('Biometric setting', data);
        if(data.biometric_source === 'optima'){
            data.project_ref = projectId;
            api_response = await getOptimaConnectionStatus(data);
        }else{
            let {touchByteAPI} = sails.config.constants;
            api_response = await HttpService.makeGET(`${HttpService.makeBaseUrl(data)}${touchByteAPI.GET_site_info(data.site_id)}`,{}, {
                // 'Authorization': `${data.key}`
            }, true, 15000);
            sails.log.info('Response from n/w call', api_response.status);
        }

        if(api_response.success){
            api_response.data = {
                ...(api_response.data),
                is_valid: true,
            }
        }
        api_response.status = 200;
        HttpService.proxyHTTPResponse(res, api_response);
    },

    checkOptimaStatus: async (req, res) => {
        let projectId = +(req.param('projectId', 0));
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        sails.log.info(`checking connection project_id: ${projectId}, site_id: ${optima_setting.site_id}`);

        /**
         *
         * status unit
         * 0 Disconnected
         * 1 Incompatible type
         * 2 Incompatible version
         * 3 Incorrect password
         * 4 Reset
         * 5 Nominal
         * 6 Updating
         * 7 Collection data
         * 8 Busy
         * 9 Upgrading
         * 10 Offline
         * 11 Network unavailable
         * 12 Data frame
         *
         * There is an issue if status code =
         * 0, 1, 2, 3, 4, 10, 11, 12
         *
         * Ok if code = 5
         * Updating/downloading data = 8,9
         *
         * Progress 0 means there is nothing going on.
         * anything after 1-99 is updating the controller but the status will be 6
         *
         * When everything is back to normal it will be
         * status code 5 progress 0
         */
        let statusResponse = await callOptimaBoxGateway(optima_setting, {
            endpoint: 'checkStatus',
            method: 'GET',
        }, {
            fullDetail: true
        });
        let outcome = {
            success: (statusResponse.success || false),
            statusCode: statusResponse.status,
            data: statusResponse.data,
            message: statusResponse.message,
        };
        return ResponseService.successResponse(res, outcome);
    },

    createOptimaBadge: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let createRequest = _.pick((req.body || {}), [
            'badgeNumber',
            'lastName',
            'firstName',
            'groupId',
            'statusId',
            'timeSlotsId',
        ]);
        sails.log.info(`create optima badge, projectId: ${projectId}, inductionRequestId: ${inductionRequestId}`);
        let optima_setting = await sails.models.optimasetting_reader.findOne({
            project_ref: projectId,
            key: {'!=': null}
        });
        sails.log.info('got setting', optima_setting ? optima_setting.id : null);
        if (!optima_setting) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }

        let out = await createBadge(optima_setting, createRequest);
        if (!out.badge) {
            return ResponseService.errorResponse(res, sails.__('Failed to associate Optima badge'), out.data || {});
        }
        let induction_request = await sails.models.inductionrequest.updateOne({id: inductionRequestId})
            .set({
                optima_badge_number: out.badge,
            });
        sails.log.info('updated induction request successfully, id', induction_request ? induction_request.id : '');

        return ResponseService.successResponse(res, {
            optima_badge_number: out.badge,
            has_setting: true,
            induction_request
        });
    },

    remoteEnrolUserToOptima: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let inductionRequestId = +req.param('inductionRequestId', 0);
        let optima_setting = req.project_optima_setting;
        if (!optima_setting.has_fr) {
            return ResponseService.errorResponse(res, sails.__('facial_recognition_not_enabled'));
        }
        /*
        // No Need to additionally check for this??
        sails.log.info('optima enabled project have FR enabled, will check auto remote enrolment status');
        [optima_setting] = await populateProjectRefs([optima_setting], 'project_ref', [
            'id',
            'parent_company',
        ]);

        let company_setting = await sails.models.companysetting_reader.findOne({
            where: {
                company_ref: optima_setting.project_ref.parent_company,
                name: COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG,
            }
        });
        if (!(company_setting && company_setting.value && company_setting.value.optima_auto_enrol)) {
            sails.log.info('project have FR enabled, but auto enrolment is OFF');
            return ResponseService.errorResponse(res, sails.__('facial_recognition_not_enabled'));
        }*/

        sails.log.info('optima enabled project have FR enabled, with auto remote enrolment enabled');
        let ir = await sails.models.inductionrequest_reader.findOne({
            select: ['additional_data', 'optima_badge_number', 'user_ref'],
            where: {
                id: inductionRequestId,
                project_ref: projectId
            }
        });
        let profile_pic = ir.additional_data?.user_info?.profile_pic_ref || {};

        if (!ir || !ir.optima_badge_number || !profile_pic.file_url) {
            sails.log.info(`enrolment face URL not found for id:${inductionRequestId}, project: ${projectId}`);
            return ResponseService.errorResponse(res, sails.__('remote_enrolment_failed_face_url_not_found'));
        }
        let {error: detectionError, face, message: detectionFailedMessage} = await detectFaces(profile_pic.file_url);
        if(!face || detectionFailedMessage || detectionError){
            return ResponseService.errorResponse(res, detectionFailedMessage || `Failed while processing user profile pic, Valid face not found`);
        }

        let cropped = await resizeOrCropImageToFace(ir.user_ref, profile_pic.file_url, {faceBoundingBox: face.BoundingBox ,responseType: 'base64'});
        // let cropped = await resizeOrCropImageToFace(ir.user_ref, profile_pic.file_url, {responseType: 'base64'});
        if (!cropped.success) {
            return ResponseService.errorResponse(res, `Failed while processing user profile pic`);
        }
        let enrolment_result = await remoteEnrolFace(optima_setting, ir.optima_badge_number, {base64: cropped.file_base64});
        if(!enrolment_result.success){
            let err_message = _get(enrolment_result.data || {}, 'message', '');
            // sails.log.info('enrolment_result', JSON.stringify(enrolment_result));
            let m = err_message ? sails.__('almas_remote_enrolment_failed_with_reason', err_message) : sails.__('almas_remote_enrolment_failed');
            sails.log.info(`remote enrolment failed for id:${inductionRequestId}, project: ${projectId}, m: "${m}" file: ${profile_pic.file_url}`);
            return ResponseService.errorResponse(res, m);
        }
        return ResponseService.successResponse(res, {
            optima_badge_number: ir.optima_badge_number,
            has_setting: true,
            enrolment_result,
            enrolment_face_url: profile_pic.file_url,
            // induction_request
        });
    },

    reGenerateBadges: async (req, res) => {
        let projectId = req.param('projectId');
        sails.log.info('Re-generate optima badges, project:', projectId);

        // {"badgeNumber":0,"lastName":"<>","firstName":"<>","groupId":2,"statusId":0,"timeSlotsId":255}
        let createRequest = _.pick((req.body || {}), [
            'groupId',
            'statusId',
            'timeSlotsId',
        ]);
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key || createRequest.groupId === undefined || createRequest.statusId === undefined || createRequest.timeSlotsId === undefined) {
            return ResponseService.errorResponse(res, 'Invalid request body');
        }

        let inductions = await sails.models.inductionrequest_reader.find({
            select: ['record_id', 'status_code', 'project_ref', 'optima_badge_number', 'additional_data'],
            where: {
                optima_badge_number: null,
                status_code: [2, 6], // only `accepted` Or `In review`
                project_ref: projectId
            },
            sort: ['id asc'],
        });

        sails.log.info('total inductions:', inductions.length, 'createRequest', createRequest);
        ResponseService.successResponse(res, {count: inductions.length});

        let processed = [];
        for(let i = 0, len = inductions.length; i < len; i++){

            sails.log.info('processing induction id:', inductions[i].id);
            let {additional_data: {user_info}} = inductions[i];
            let createBody = {
                'badgeNumber': 0,
                'lastName': (user_info && user_info.last_name) || '',
                'firstName': (user_info && user_info.first_name) || '',
                ...createRequest,
            };
            let out = await createBadge(optima_setting, createBody);
            if(!out.badge){
                sails.log.info('Error from n/w call', out);
                sails.log.error(`badge creation call of ID:${inductions[i].id} failed, payload was`, createBody);
                return ;
            }

            let induction_request = await sails.models.inductionrequest.updateOne({id: inductions[i].id})
                .set({
                    optima_badge_number: out.badge,
                });
            processed.push({
                id: inductions[i].id,
                optima_badge_number: induction_request.optima_badge_number,
            });
        }
        sails.log.info('Total badges created', inductions.length, 'processed', JSON.stringify(processed));
    },

    getAllOptimaSetting: async (req, res) => {
        sails.log.info('fetch all optima settings');
        let optima_settings = await sails.models.optimasetting_reader.find().sort('id ASC');
        optima_settings = await populateProjectRefs(optima_settings, 'project_ref', [
            'id',
            'name',
            'project_initial',
            'use_prefix',
            'project_category',
            'parent_company',
            'contractor',
            'is_active',
            'disabled_on',
            'custom_field'
        ]);
        // get parent companies
        let parent_companies = (optima_settings || []).reduce((set, s) => {
            if (!s.project_ref || !s.project_ref.parent_company || set.includes(s.project_ref.parent_company)) {
                return set;
            }
            set.push(s.project_ref.parent_company);
            return set;
        }, []);
        let parent_employers = [];
        if (parent_companies.length) {
            sails.log.info('get parent company info of:', parent_companies);
            parent_employers = await sails.models.createemployer_reader.find({
                where: {id: parent_companies},
                select: ['name', 'id', 'country_code']
            });
        }

        sails.log.info('attaching contractor info with projects');
        optima_settings = (optima_settings || []).map(s => {
            if (!s.project_ref) {
                return s;
            }
            s.project_ref._contractor = parent_employers.find(e => e.id === s.project_ref.parent_company);
            return s;
        });

        sails.log.info('got settings', optima_settings.length);
        return ResponseService.successResponse(res, {optima_settings});
    },

    addUpdateOptimaSetting: async (req, res) => {

        let insertRequest = _.pick((req.body || {}), [
            'project_ref',
            'key',
            'enrolment_type',
            'allow_cards',
            'has_turnstile',
            'force_onsite_for_an_out',
            'biometric_source',
            'site_id',
            'tz',
            'geo_fence_locations',
            'selfie_required',
            'has_worker_clocking',
            'has_bulk_clocking',
            'has_vehicle_clocking',
            'kiosk_mode',
            'has_fr',
            'clock_in_mode',
            'liveness_check',
        ]);
        insertRequest = cleanOptimaSettingFields(insertRequest);

        sails.log.info('Insert / update optima setting', insertRequest.project_ref);

        try {

            let existingRecord = await sails.models.optimasetting.findOne({
                project_ref: insertRequest.project_ref
            });

            if((!existingRecord || !existingRecord.has_fr) && insertRequest.has_fr){
                sails.log.info('FR setting is enabled, creating project collection');
                let {success, collectionId, data} = await triggerCreateCollection(insertRequest.project_ref);
                if(!success){
                    sails.log.info(`FR failed while creating collection: ${collectionId}`, data);
                    return ResponseService.errorResponse(res, sails.__('facial_recognition_save_failed'));
                }
            }
            // we don't want to delete FR collection when setting gets disabled.

            if (existingRecord) {
                sails.log.info('got existing setting, updating record', existingRecord.id);
                let updatedRecord = await sails.models.optimasetting.updateOne({id: existingRecord.id}).set(insertRequest);
                return ResponseService.successResponse(res, {optima_setting: updatedRecord, updated: true});
            } else {
                sails.log.info('no existing setting found, creating one');
                let newRecord = await sails.models.optimasetting.create(insertRequest);
                return ResponseService.successResponse(res, {optima_setting: newRecord, created: true});
            }
        } catch (createError) {

            if(createError.code === 'E_UNIQUE'){
                sails.log.error('site id already exists into setting');
                return ResponseService.errorResponse(res, `Site id: "${insertRequest.site_id}" already exists for ${insertRequest.biometric_source}. Please unlink it before adding here.`, createError);
            }

            sails.log.error('Failed to add optima setting', createError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), createError);
        }
    },

    getOptimaSettingByProjectId: async (req, res) => {
        let projectId = req.param('projectId', 0);
        let existingRecord = await sails.models.optimasetting_reader.findOne({
            omit: ['key'],
            where: { project_ref: projectId }
        });
        return ResponseService.successResponse(res, {optima_setting: existingRecord || {}});
    },

    removeOptimaSetting: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`[ADMIN] Delete request for optima setting for project: ${projectId}, id:`, +req.body.optima_setting_id);
        let row = await sails.models.optimasetting.destroy({
            id: +req.body.optima_setting_id,
            project_ref: projectId,
        });
        sails.log.info('[ADMIN] Deleted optima setting record', JSON.stringify(row));
        return ResponseService.successResponse(res, {message: "Deleted record successfully"});
    },

    getProjectBadgeEvents: async (req, res) => {
        // get optima setting of given project
        // get event logs for same
        let projectId = +req.param('projectId', 0);
        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 50);
        let text = (req.param('q', '')).trim();

        if(isNaN(projectId) || isNaN(pageNumber) || isNaN(limit)){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info('fetch badge events for project', projectId, `page: ${pageNumber} skip: ${skip} search: ${text}`);
        let filteredInductions = await getFilteredInductionBySearch(projectId, text);
        if(text && !filteredInductions.length){
            // no matching result
            return ResponseService.successResponse(res, {
                badge_events: [],
                induction_requests: [],
                pageNumber,
                limit,
                total: 0,
            });
        }
        let user_id_filter = {};
        if(filteredInductions.length){
            user_id_filter = {
                user_ref: (filteredInductions.map(induction => induction.user_ref))
            }
        }

        let total = undefined;
        if (pageNumber === 1) {
            let {count} = await CacheService.getBadgeEventsCount({projectId, natureId: 0}, user_id_filter.user_ref);
            total = count;
        }
        // query all logs for this host
        let badge_events = await sails.models.badgeevent_reader.find({
            select: ['event_date_time', 'badge_number', 'unit_name', 'reader_name', 'temperature'],
            where: {
                project_ref: projectId,
                nature_id: 0,   // valid badge events only, hiding access card events
                ...(user_id_filter)
            },
            skip,
            limit,
            sort: ['project_ref', 'event_date_time DESC']
        });
        if((total === 0 || total < badge_events.length) && badge_events.length){
            total = badge_events.length;
        }

        let induction_requests = [];
        if(filteredInductions.length){
            induction_requests = filteredInductions;
        }else{
            let badgeIds = _.uniq(_.map((badge_events || []), r => r.badge_number));
            if(badgeIds && badgeIds.length){

                sails.log.info('Expanding badge Ids', badgeIds);
                induction_requests = await sails.models.inductionrequest_reader.find({
                    where: {
                        optima_badge_number: badgeIds,
                        project_ref: projectId
                    },
                    select: ['optima_badge_number', 'creator_name']
                });
            }
        }

        sails.log.info(`responding with badge events, count:${badge_events.length}`);
        return ResponseService.successResponse(res, {
            badge_events,
            induction_requests,
            pageNumber,
            limit,
            total,
        });
    },

    downloadBadgeEvents: async (req, res) => {
        sails.log.info("Downloading Badge Events XLSX.");
        let projectId = +req.param('projectId');

        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['name', 'custom_field']
        });
        let {custom_field: {timezone: project_timezone}} = projectInfo;

        const timezone = getProjectTimezone(projectInfo);

        let events_from = (+req.body.from_date) ? moment(+req.body.from_date * 1000).tz(timezone).format('DD-MM-YYYY') : '';
        let events_to = (+req.body.to_date) ? moment(+req.body.to_date * 1000).tz(timezone).format('DD-MM-YYYY') : '';

        let where = {
            project_ref: projectId
        }
        if (req.body.from_date && req.body.to_date) {
            where.event_date_time = {'>=': +req.body.from_date, '<=': +req.body.to_date};
        }

        sails.log.info('Search filter is', where);
        //Biometrics Logs
        let biometricLogs = await sails.models.badgeevent_reader.find({
            where: {
                ...where,
                nature_id: 0,   // valid badge events only, hiding access card events
            },
            select: ["event_date_time", "user_revision_ref", "user_ref", "reader_name", "unit_name"]
        }).sort('event_date_time DESC');
        sails.log.info(`Found ${biometricLogs.length} biometric events.`);

        let userIds = (biometricLogs || []).reduce((arr, log) => {
            if (log.user_ref) {
                arr.push(log.user_ref);
            }
            return arr;
        }, []);


        //Geo Fencing Logs
        let geoFenceLogs = await sails.models.usertimelog_reader.find({
            where,
            select: ["event_date_time", "user_revision_ref", "user_ref", "user_location",  "event_type", "visitor_ref"]
        }).sort('event_date_time DESC');
        sails.log.info(`Found ${geoFenceLogs.length} geo fence events.`);

        let visitorIds = [];
        userIds = (geoFenceLogs || []).reduce((arr, log) => {
            if (log.user_ref) {
                arr.push(log.user_ref);
            }
            if (log.visitor_ref) {
                visitorIds.push(log.visitor_ref);
            }
            return arr;
        }, userIds);
        userIds = _uniq(userIds);

        let visitorsInfo = await sails.models.visitor_reader.find({
            select: ['first_name', 'last_name', 'employer_ref', 'job_role_ref'],
            where:{
                id: _uniq(visitorIds)
            }
        });

        visitorsInfo = await populateEmployerRefs(visitorsInfo, 'employer_ref', ['id', 'name', 'company_initial', 'logo_file_id', 'country_code']);
        visitorsInfo = await populateJobRoles(visitorsInfo, 'job_role_ref');

        sails.log.info(`total unique users ${userIds.length} to fetch induction for project ${projectId}`);
        let inductions = await sails.models.inductionrequest_reader.find({
            where: {
                user_ref: userIds,
                project_ref: projectId
            },
            select: ['id', 'creator_name', 'user_ref', 'additional_data'],
            sort: ['id']
        })

        //Date | Time | First Name | Last Name | innDex ID | Company | Job Role | Reader
        let biometricEvents = (biometricLogs || []).reduce((arr, item) => {
            let induction = (inductions || []).find(uInfo => uInfo.user_ref === item.user_ref) || {};
            let user_info = (induction.additional_data && induction.additional_data.user_info) || {};
            let employment_detail = (induction.additional_data && induction.additional_data.employment_detail) || {};
            let eventDateTimeUnix =  +item.event_date_time * 1000;
            let row = {
                "event_epoch": eventDateTimeUnix,
                "event_date": momentTz(eventDateTimeUnix).tz(project_timezone).format('DD-MM-YYYY'),
                "event_time": momentTz(eventDateTimeUnix).tz(project_timezone).format('HH:mm:ss'),
                "first_name": user_info.first_name || '',
                "last_name":  user_info.last_name || '',
                "inndex_id":  `${item.user_ref || ''}`,
                "company":    employment_detail.employer || '',
                "job_role":   employment_detail.job_role || '',
                "event_from" : "Biometric",
                "location_name": `${item.reader_name}`,
                "event_type":  `${determineEventType(item.reader_name)}`,
            }
            arr.push(row);
            return arr;
        }, []);

        //Date | Time | First Name | Last Name | innDex ID | Company | Job Role | Location | Event
        let geoFenceEvents = (geoFenceLogs || []).reduce((arr, item) => {
            let row = {};
            let eventDateTimeUnix = +item.event_date_time * 1000;
            if (item.visitor_ref) {
                let visitor = (visitorsInfo || []).find(visInfo => visInfo.id == item.visitor_ref);
                row = {
                    "event_epoch": eventDateTimeUnix,
                    "event_date": momentTz(eventDateTimeUnix).tz(project_timezone).format('DD-MM-YYYY'),
                    "event_time": momentTz(eventDateTimeUnix).tz(project_timezone).format('HH:mm:ss'),
                    "first_name": (visitor) ? visitor.first_name : '',
                    "last_name":  (visitor) ? visitor.last_name : '',
                    "inndex_id":  'Visitor',
                    "company":    (visitor && visitor.employer_ref && visitor.employer_ref.name) ? visitor.employer_ref.name : '',
                    "job_role":   (visitor && visitor.job_role_ref && visitor.job_role_ref.name) ? visitor.job_role_ref.name : '',
                    "event_from": 'Geo-fenced',
                    "location_name": (item.user_location.name) ? `${item.user_location.name}` : '',
                    "event_type":      item.event_type,
                }
            } else {
                let induction = (inductions || []).find(uInfo => uInfo.user_ref === item.user_ref) || {};
                let user_info = (induction.additional_data && induction.additional_data.user_info) || {};
                let employment_detail = (induction.additional_data && induction.additional_data.employment_detail) || {};
                row = {
                    "event_epoch": eventDateTimeUnix,
                    "event_date": momentTz(eventDateTimeUnix).tz(project_timezone).format('DD-MM-YYYY'),
                    "event_time": momentTz(eventDateTimeUnix).tz(project_timezone).format('HH:mm:ss'),
                    "first_name": user_info.first_name || '',
                    "last_name":  user_info.last_name || '',
                    "inndex_id":  `${item.user_ref || ''}`,
                    "company":    employment_detail.employer || '',
                    "job_role":   employment_detail.job_role || '',
                    "event_from": 'Geo-fenced',
                    "location_name": (item.user_location.name) ? `${item.user_location.name}` : '',
                    "event_type":      item.event_type,
                }
            }
            arr.push(row);
            return arr;
        }, []);

        let workbook = await badgeEventExcel(biometricEvents, geoFenceEvents);

        let fileName = `${projectInfo.name}-All_Events-[${events_from}-${events_to}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    getProjectGeoFenceEvents: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 50);
        let text = (req.param('q', '')).trim();

        if(isNaN(projectId) || isNaN(pageNumber) || isNaN(limit)){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info('fetch geo-fence for project', projectId, `page: ${pageNumber} skip: ${skip} search: ${text}`);
        let filteredInductions = await getFilteredInductionBySearch(projectId, text);
        if(text && !filteredInductions.length){
            // no matching result
            return ResponseService.successResponse(res, {
                events: [],
                pageNumber,
                limit,
                total: 0,
            });
        }
        let user_id_filter = {};
        if(filteredInductions.length){
            user_id_filter = {
                user_ref: (filteredInductions.map(induction => induction.user_ref))
            }
        }
        let total = undefined;
        if (pageNumber === 1) {
            let {count} = await CacheService.getUserTimeLogCount({projectId}, user_id_filter.user_ref);
            total = count;
        }
        let events = await sails.models.usertimelog_reader.find({
            select: ['event_date_time', 'user_ref', 'event_type', 'temperature', 'user_location'],
            where: {
                project_ref: projectId,
                user_ref: {'!=': null},
                ...(user_id_filter),
            },
            skip,
            limit,
            sort: 'event_date_time DESC'
        });
        if((total === 0 || total < events.length) && events.length){
            total = events.length;
        }
        events = await populateUserRefs(events, 'user_ref', []);

        sails.log.info(`responding with all geo-fence events, count:${events.length}`);
        return ResponseService.successResponse(res, {
            events,
            pageNumber,
            limit,
            total,
        });
    },

    updateGeoFenceEventById: async (req, res) => {
        let project_ref = +req.param('projectId');
        let id = +req.param('userTimeLogId');
        let payload = _.pick((req.body || {}), [
            'temperature',
        ]);

        sails.log.info('Updating User Time Log event, id:', id, 'project_ref:', project_ref);
        let record = await sails.models.usertimelog.updateOne({id, project_ref}).set(payload);
        return ResponseService.successResponse(res, {record, updated: true});
    },

/*    // @deprecated spatel: after 3rd May 2021
    getProjectMemberTimeData: async (req, res) => {

        let projectId = req.param('projectId');
        sails.log.info('fetch member Time data for project', projectId);
        try {

            let induction_requests = await sails.models.inductionrequest.find({
                // optima_badge_number: { '!=': null},
                status_code: 2, // approved one only
                project_ref: projectId
            });

            // @todo: spatel: Once we start freezing user doc, below call won't be needed
            let updated_records = await expandUserDocs(induction_requests);
            let badge_daily_log = [];
            let user_ids = _uniq(induction_requests.map(i => i.user_ref));
            if(user_ids.length){
                badge_daily_log = await getAllDailyTimeEvent(projectId, user_ids);
            }

            return ResponseService.successResponse(res, {
                induction_requests: updated_records,
                badge_daily_log,
            });

        } catch (fetchError) {
            sails.log.error('Failed to fetch member time log', fetchError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), fetchError);
        }
    },*/

    getProjectMemberDataForDays: async (req, res) => {
        let projectId = req.param('projectId');
        let day_of_year = moment((req.query.day_of_year || '--'), dbDateFormat);
        if (!day_of_year.isValid()) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid day_of_year filter provided.',{day_of_year});
        }
        let next_day = day_of_year.clone().add(1, 'd').format(dbDateFormat);
        day_of_year = day_of_year.format(dbDateFormat);
        sails.log.info(`fetch member Time data for day: ${day_of_year}, project`, projectId);
        let badge_logs = await getDailyTimeEventV2(projectId, day_of_year, next_day, [], false, null, true, true);
        let userIdVisited = _uniq(badge_logs.map(l => l.user_id));
        sails.log.info(`day: ${day_of_year}, got time logs of users:`, JSON.stringify(userIdVisited));

        let additionalCols = [
            // `record_id::int`,
            // `"createdAt"`,
            'project_ref',
            'creator_name',
            `status_code`,
            `optima_badge_number::bigint`,
            'travel_time',
            'travel_method',
            'vehicle_reg_number',
            `additional_data -> 'user_info' -> 'email' as email`,
            `additional_data -> 'user_info' -> 'profile_pic_ref' as profile_pic_ref`,
            `additional_data -> 'employment_detail' ->> 'employer' as employer`,
            `additional_data -> 'employment_detail' ->> 'job_role' as job_role`,
            `additional_data -> 'contact_detail' ->> 'post_code' as user_postcode`,
        ];
        let {
            records: induction_requests
        } = await inductionFn.getProjectInductions(projectId, {
            statusCodes: [2, 4, 6],
            limit: -1,
            searchUserIds: userIdVisited,
            sortKey: 'id'
        }, additionalCols);
        return ResponseService.successResponse(res, {
            induction_requests: induction_requests.map(ir => {
                ir.optima_badge_number = ir.optima_badge_number ? +ir.optima_badge_number : null;
                ir.profile_pic = (ir.profile_pic_ref) ? (ir.profile_pic_ref.sm_url || ir.profile_pic_ref.file_url) : null;
                ir.profile_pic_ref = undefined;
                ir.is_shadow_user = isShadowUser({email: ir.email});
                delete ir.email;
                return ir;
            }),
            badge_logs,
        });
    },

    updateUserDailyLog: async (req, res) => {
        let user_ref = req.param('userId');
        let project_ref = req.param('projectId');
        let day_of_yr = (req.body.day_of_yr || '-');

        let payload = _.pick((req.body || {}), [
            'adjustment_minutes',
            'comments',
        ]);
        if ((isNaN(payload.adjustment_minutes) && !payload.comments) || !day_of_yr) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'adjustment_minutes / comments is required.',{payload, day_of_yr});
        }

        sails.log.info('Updating time log, user_id:', user_ref, 'day:', day_of_yr);
        let record = await sails.models.userdailylog.updateOne({user_ref, project_ref, day_of_yr}).set(payload);
        return ResponseService.successResponse(res, {record, updated: true});
    },

    updateBadgeEventById: async (req, res) => {
        let project_ref = +req.param('projectId');
        let id = +req.param('badgeEventId');
        let payload = _.pick((req.body || {}), [
            'temperature',
        ]);

        sails.log.info('Updating badge event log, id:', id, 'project_ref:', project_ref);
        let record = await sails.models.badgeevent.updateOne({id, project_ref}).set(payload);
        return ResponseService.successResponse(res, {record, updated: true});
    },

    getOptimaBadgeDetail: async (req, res) => {
        // being used on web ui for search
        let projectId = req.param('projectId');
        let badgeNumber = req.query.badge_number;
        sails.log.info('Badge number to search: ', badgeNumber);
        try {

            let induction_requests = await sails.models.inductionrequest_reader.findOne({
                optima_badge_number: badgeNumber,
                project_ref: projectId
            });
            let optima_setting = req.project_optima_setting;
            let api_response = await callOptimaBoxGateway(optima_setting, {
                endpoint: `Badge/${badgeNumber}`,
                method: 'GET',
            }, {});
            if (api_response.data && api_response.success) {
                api_response.data.has_setting = true;
            }
            if(api_response.error && String(api_response.data).match('does not exist')){
                api_response.data = {not_found: true, message: 'Badge does not exist'};
            }
            api_response.status = 200;

            if(induction_requests != undefined) {
                api_response.data.induction_requests = induction_requests;
            }

            HttpService.proxyHTTPResponse(res, api_response);
        } catch (fetchError) {
            sails.log.error('Failed to fetch badge number details', fetchError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), fetchError);
        }
    },

    getTotalSiteTimeReport: totalTimeReport,

    getLiveTvTotalSiteTimeReport: totalTimeReport,

    storeUserTimeLog: async (req, res) => {
        await storeTimeLog(req, res);
    },

    storeInnTimeAppLog: async (req, res) => {
        if(!req.body.user_id){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'user_id is required.');
        }
        let user = await sails.models.user_reader.findOne({where: {id: req.body.user_id}, select: ['id', 'parent_company']});
        if(!user || !user.id){
            sails.log.info('Invalid User Id');
            return ResponseService.errorResponse(res, 'user_id not found.');
        }

        req.user = {
            id: req.body.user_id,
            parent_company: user.parent_company,
        };
        req.body.project_ref = req.project.id;
        await storeTimeLog(req, res);
    },

    storeVisitorInnTimeAppLog: async (req, res) => {
        if(!req.body.visitor_id){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'visitor_id is required.');
        }
        req.user = {
            id: null,
            parent_company: req.body.company_ref,
        };
        req.body.project_ref = req.project.id;
        await storeTimeLog(req, res, true);
    },

    getProjectUsersGeoFenceTimeLogs: async (req, res) => {

        let employer_id = req.param('employerId');
        let project_id = req.param('projectId');
        let request_date = moment((req.query.for_date || '--'), dbDateFormat);
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        if (!request_date.isValid()) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'for_date query parameter not found');
        }
        let day_of_yr_today = request_date.format(dbDateFormat);

        sails.log.info('Querying Geo fence time logs for project:', project_id, day_of_yr_today, `is_inherited_project: ${is_inherited_project}`);

        let daily_log = await getAllDailyTimeEvent(project_id, [], (is_inherited_project ? null : employer_id));
        if(!daily_log || !daily_log.length){
            sails.log.info('No time log found, project_id:', project_id);
            return ResponseService.successResponse(res, {member_time_logs: []});
        }
        sails.log.info('Received total geo-fence time logs ', daily_log.length);
        let unique_user_ids = _uniq(daily_log.map(l => l.user_id));

        sails.log.info('fetch employment details of', unique_user_ids);
        let employees_details = await sails.models.userempdetail.find({
            where: {user_ref: unique_user_ids},
            select: ['id', 'employer', 'job_role', 'operative_type', 'user_ref', 'employment_company']
        });
        employees_details = await populateUserRefs(employees_details, 'user_ref', []);

        let member_time_logs = employees_details.map(e => {
            let member_logs = daily_log.filter(l => l.user_id === e.user_ref.id);
            // ideally get called for today's date only
            let event_of_today = eventOfToday(day_of_yr_today, member_logs, moment());
            return {
                user_id: e.user_ref && e.user_ref.id,
                name: getUserFullName(e.user_ref || {}),
                employer: e.employer,
                job_role: e.job_role,
                employment_company: e.employment_company,
                daily_logs: sortBadgeLogsByDay(member_logs),
                event_of_today,
                is_still_on_site: isStillOnSite(event_of_today),
                is_first_aider: null,
                has_SMSTS_or_SSSTS: null,
                has_fire_marshal: null,

            }
        }).filter(e => e.daily_logs && e.daily_logs.length);

        let user_ids = member_time_logs.map(l => +l.user_id);
        sails.log.info('Fetch documents of Members of project are ', user_ids);

        // Get project country code for competency filtering
        let project = await sails.models.project_reader.findOne({
            select: ['custom_field'],
            where: { id: project_id }
        });
        let country_code = (project && project.custom_field && project.custom_field.country_code) || 'GB';

        // Doesn't require parent docs, we need all documents for check only...
        // As Not responding with docs
        let docs = await sails.models.userdoc_reader.find({
            doc_owner_id: user_ids,
            // parent_doc_ref: null, // get only parent docs
            is_deleted: {'!=': 1},
            expiry_date: {'>': moment().valueOf()}
        });

        // Fetch competency mappings once for all users (optimized approach)
        const competencyMappings = await getCompetencyMappings(country_code);

        return ResponseService.successResponse(res, {
            member_time_logs: member_time_logs.map(async (l) => {
                let user_documents = docs.filter(d => d.doc_owner_id === l.user_id);
                l.is_first_aider = (await isFirstAider(user_documents, competencyMappings)) || null;
                l.has_SMSTS_or_SSSTS =
                    (await haveSMSTSOrSSSTSDoc(user_documents, competencyMappings)) || null;
                l.has_fire_marshal =
                    (await haveFireMarshal(user_documents, competencyMappings)) || null;

                return l;
            }),
        });
    },

    getProjectUserGeoFenceLog: async (req, res) => {
        let project_id = req.param('projectId');
        let user_id = req.param('userId');
        let event_type = req.param('interactionType');
        let event_log_id = +(req.param('eventLogId') || 0);
        let day_of_yr_today = moment((req.query.for_date || '--'), dbDateFormat);

        const ALLOWED_EVENT_TYPE = {
            clock_in: 'IN',
            clock_out: 'OUT'
        };

        // request should either have valid `event_log_id` OR `day_of_yr`
        if (isNaN(event_log_id) || (!event_log_id && !day_of_yr_today.isValid()) || isNaN(+project_id) || isNaN(+user_id) || !Object.keys(ALLOWED_EVENT_TYPE).includes(event_type)) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid Request, required fields are missing', {required: ['projectId', 'userId', 'interactionType', 'for_date']});
        }

        sails.log.info(`Querying Geo fence time log for project_id: ${project_id}, user_id: ${user_id}, day_of_yr: '${day_of_yr_today.format()}' '${event_type}' event_log_id: ${event_log_id}`);

        let log = {};
        if(event_log_id){
            // Respond with specific log itself.
            sails.log.info('Target id:', event_log_id, 'event_type:', event_type);

            log = await sails.models.usertimelog.findOne({
                where: {
                    id: event_log_id,
                    user_ref: +user_id,
                    project_ref: +project_id
                },
                select: ['user_ref', 'project_ref', 'attachment_ref', 'event_date_time', 'event_type', 'user_location', 'temperature', 'extras']
            }).populate('user_ref').populate('project_ref');
        }
        else{
            [log] = await sails.models.userdailylog.find({
                where: {day_of_yr: day_of_yr_today.format(dbDateFormat), project_ref: +project_id, user_ref: +user_id},
                select: ['user_ref', 'project_ref', 'day_of_yr', 'first_in', 'last_out', 'comments'],
                sort: ['id DESC'],
                limit: 1
            }).populate('user_ref').populate('project_ref');

            if (log && log.id) {
                log.day_of_yr = moment(log.day_of_yr).format(dbDateFormat);
                // this can be decided by using `events.source` field of timestamp...?
                let timestamp = (ALLOWED_EVENT_TYPE[event_type] === 'IN') ? log.first_in : log.last_out;
                if (timestamp) {
                    let where = {
                        event_date_time: +timestamp,
                        user_ref: +user_id,
                        project_ref: +project_id
                    };
                    sails.log.info('Target timestamp:', timestamp, 'event_type:', event_type, 'where:', where);

                    let raw_logs = await sails.models.usertimelog_reader.find({
                        where,
                        select: ['attachment_ref', 'event_date_time', 'event_type', 'user_location', 'extras']
                    }).sort([
                        {event_date_time: (ALLOWED_EVENT_TYPE[event_type] === 'IN') ? 'ASC' : 'DESC'}
                    ]).limit(1);
                    let exact_log = raw_logs.length ? raw_logs.shift() : {};

                    // Merging Processed log with Raw log.
                    log = {...log, ...exact_log};
                }
            }
        }

        if (log && log.user_ref && log.user_ref.profile_pic_ref) {
            log.user_ref.profile_pic_ref = await sails.models.userfile_reader.findOne({
                where: {id: log.user_ref.profile_pic_ref},
            });
        }

        if(log && log.attachment_ref){
            log.attachment_ref = await sails.models.userfile_reader.findOne({
                where: {id: log.attachment_ref},
            });
        }
        return ResponseService.successResponse(res, {log});
    },

    deleteProjectTimeLogById: async (req, res) => {
        let project_id = +(req.param('projectId', 0));
        let event_log_id = +(req.param('eventLogId') || 0);
        sails.log.info(`Delete time log request for project_id: ${project_id}, event_log_id: ${event_log_id} initiator ${req.user.id}`);

        let deleted_user_time_logs = await sails.models.usertimelog.destroy({
            project_ref: project_id,
            id: event_log_id
        });
        let user_time_log = deleted_user_time_logs.length ? deleted_user_time_logs.shift() : {};
        sails.log.info(`Deleted time log`, JSON.stringify(user_time_log));
        if(user_time_log && user_time_log.id){
            let day = moment.unix(+user_time_log.event_date_time);
            let searchRequest = {
                from_timestamp: day.clone().subtract(1, 'day').startOf('day').unix(),
                to_timestamp: day.clone().endOf('day').unix(),
                filter: {
                    user_ref: user_time_log.user_ref,
                    project_ref: project_id,
                }
            };
            sails.log.info(`Event date is : ${day.format()} Re-indexing`, searchRequest);
            await reIndexTimeLogs(searchRequest, Object.values(VALID_SOURCES), true, true, false);
        }

        return ResponseService.successResponse(res, {done: true, user_time_log});
    },

    createUserWorkingShift: async (req, res) => {
        let projectId = req.param('projectId', 0);
        let workingShiftRequest = _.pick((req.body || {}), [
            'visitor_ref',
            'user_ref',
            'start_time',
            'default_hrs',
            'tolerance_hrs',
            'valid_from',
            'valid_till',
        ]);
        if(
            (!workingShiftRequest.user_ref && !workingShiftRequest.visitor_ref) ||
            // !workingShiftRequest.project_ref ||
            workingShiftRequest.start_time === undefined ||
            workingShiftRequest.start_time === null ||
            // !workingShiftRequest.default_hrs ||
            // !workingShiftRequest.tolerance_hrs ||
            !workingShiftRequest.valid_from
        ){
            sails.log.info('Invalid request:', workingShiftRequest);
            return ResponseService.errorResponse(res, 'Invalid request', {workingShiftRequest});
        }
        try{
            workingShiftRequest.project_ref = projectId;
            sails.log.info('creating user working shift config, user_ref:', workingShiftRequest.user_ref, `visitor_ref: ${workingShiftRequest.visitor_ref}`);
            let user_working_shift = await sails.models.userworkingshift.create(workingShiftRequest);

            if(req.body.re_process_existing){
                // Re-process all logs of that user, as shift config will get changed.
                await reActOnShiftConfigChange(req.project_optima_setting, user_working_shift);
            }
            return ResponseService.successResponse(res, {user_working_shift});
        }catch (err) {
            sails.log.info('something went wrong while storing info: ', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    getUserActiveWorkingShift: async (req, res) => {
        let userIds = (req.body.userIds || []);
        let visitorIds = (req.body.visitorIds || []);
        let projectId = +req.param('projectId');
        try{
            if((!(userIds.length) && !(visitorIds.length)) || !projectId || isNaN(projectId)){
                sails.log.info('Invalid request:', {userIds, visitorIds, projectId});
                return ResponseService.errorResponse(res, 'Invalid request', {userIds, visitorIds, projectId});
            }
            let configs = [];
            // userIds = _uniq(userIds);
            sails.log.info(`fetching working shift for users, ${projectId}`, JSON.stringify(userIds));
            for(let i = 0, len = userIds.length; i < len; i++){
                // @todo: spatel: This DB call can be optimized to support larger UserIds list
                let config = await getShiftConfigOfUser(userIds[i].user_ref, projectId, userIds[i].timestamp);
                if(config && config.length){
                    configs.push(config[0]);
                }
            }

            // visitorIds = _uniq(visitorIds);
            sails.log.info(`fetching working shift for visitors, ${projectId}`, JSON.stringify(visitorIds));
            for(let i = 0, len = visitorIds.length; i < len; i++){
                // @todo: spatel: This DB call can be optimized to support larger UserIds list
                let config = await getShiftConfigOfUser(null, projectId, visitorIds[i].timestamp, visitorIds[i].visitor_ref);
                if(config && config.length){
                    configs.push(config[0]);
                }
            }
            return ResponseService.successResponse(res, {configs});
        }catch (err) {
            sails.log.info('something went wrong while fetching shift info: ', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }

    },

    removeUserShiftConfig: async (req, res) => {
        let projectId = +req.param('projectId');
        let configId = +req.param('configId');
        try{
            sails.log.info('Deleting shift configId:', configId, 'project:', projectId);
            let deleted = await sails.models.userworkingshift.destroyOne({project_ref: projectId, id: configId});
            sails.log.info('Deleted config, id', deleted ? deleted.id : null);
            if(deleted){
                // Re-process all logs of that user, as shift config will get changed.
                await reActOnShiftConfigChange(req.project_optima_setting, deleted);
            }
            return ResponseService.successResponse(res, {deleted});
        }catch (err) {
            sails.log.info('something went wrong while removing shift info: ', err);
            return ResponseService.errorResponse(res, sails.__('internal server error'), err);
        }
    },

    getCompanyProjectTimeLogs: async (req, res) => {
        let employer_id = req.param('employerId');
        let project_id = req.param('projectId');
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let request_date = moment((req.query.for_date || '--'), dbDateFormat);
        if (!request_date.isValid()) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'for_date query parameter not found');
        }
        let next_day = request_date.clone().add(1, 'd').format(dbDateFormat);
        let day_of_yr_today = request_date.format(dbDateFormat);

        sails.log.info('Querying time logs for project:', project_id, day_of_yr_today, `is_inherited_project: ${is_inherited_project}`);

        let daily_log = await getDailyTimeEventV2(project_id, day_of_yr_today, next_day, [], false, (is_inherited_project ? null : employer_id), false, true);
        if(!daily_log || !daily_log.length){
            sails.log.info('No time log found, project_id:', project_id);
            return ResponseService.successResponse(res, {member_time_logs: []});
        }

        sails.log.info('Received total time logs ', daily_log.length);
        let unique_user_ids = _uniq(daily_log.map(l => l.user_id));

        sails.log.info('fetch employment details of', unique_user_ids);
        let employees_details = await sails.models.userempdetail.find({
            where: {user_ref: unique_user_ids},
            select: ['id', 'employer', 'job_role', 'operative_type', 'user_ref']
        });
        employees_details = await populateUserRefs(employees_details, 'user_ref', []);

        return ResponseService.successResponse(res, {
            member_time_logs: daily_log.map(log => {
                let user = employees_details.find(e => e.user_ref.id === log.user_id) || {};
                log.name = getUserFullName(user.user_ref || {});
                log.employer = user.employer;
                log.job_role = user.job_role;
                if(log.recent_in && log.clock_out && +log.recent_in > +log.clock_out){
                    // Still on site?
                    // Continue showing counter, RC 486
                    log.effective_time = undefined;
                    log.duration_in_sec = undefined;
                    log.clock_out = undefined;
                }
                return log;
            }),
        });
    },

    // @note: Incorrect data source
    // being used into daily-activity component
    // @deprecated: should be deprecated as it is referring to invalid data source.
    getMembersTimeLogs: async (req, res) => {
        let projectId = req.param('projectId');
        let from_date = moment((req.query.from_date || '--'), dbDateFormat);
        let to_date = moment((req.query.to_date || '--'), dbDateFormat);
        let companyId = req.param('companyId');
        if (!from_date.isValid() || !to_date.isValid()) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid from_date filter provided.',{from_date, to_date});
        }
        from_date = from_date.format(dbDateFormat);
        to_date = to_date.add(1, 'days').format(dbDateFormat);
        sails.log.info(`total-time report from, to: ${from_date}  <= date < ${to_date}`);
        try {
            let daily_logs = await getDailyTimeEventV2(projectId, from_date, to_date, [], false, companyId);

            let userIds = [];
            daily_logs.map(daily_log => {
                if (daily_log.user_id) {
                    userIds.push(daily_log.user_id);
                }
            });
            userIds = _.uniq(userIds);
            sails.log.info('Total User Ids to expand:', userIds.length);
            if(userIds.length) {
                let users = await sails.models.user_reader.find({id: userIds});
                users = await attachProfilePicWithUsersInfo(users, [], true);
                let employers = await sails.models.userempdetail_reader.find({
                    where: {user_ref: userIds}
                });
                if(users && users.length){
                    // got those users
                    daily_logs = daily_logs.map(daily_log => {
                            if(daily_log.user_id){
                                let user = users.find(u => u.id === daily_log.user_id);
                                daily_log.employer = employers.find(e => e.user_ref === daily_log.user_id) || null;
                                daily_log.user_id = user;
                            }
                            return daily_log;
                    });
                }
            }

            ResponseService.successResponse(res, {
                daily_logs
            });
        } catch (reportFetchError) {
            sails.log.error('Failed to fetch site time report', reportFetchError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), reportFetchError);
        }
    },
    getOptimaAccessGroupAndReader: async (req, res) => {
        let projectId = req.param('projectId', 0);
        try {
            let optima_setting = req.project_optima_setting;
            if (!optima_setting || !optima_setting.key) {
                return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
            }

            let accessGroupsResponse = await callOptimaBoxGateway(optima_setting, {
                endpoint: 'Groups',
                method: 'GET',
            }, {});

            let access_groups = [];
            if (accessGroupsResponse.success && accessGroupsResponse.status === 200) {
                access_groups = accessGroupsResponse.data.groups;
            }else{
                return ResponseService.errorResponse(res, sails.__('Failed to fetch access groups / readers.'));
            }

            let readers = await getAllReadersList(optima_setting);

            return ResponseService.successResponse(res, {
                access_groups,
                readers
            });

        } catch (fetchError) {
            sails.log.error('Failed to fetch access groups and readers.', fetchError);
            return ResponseService.errorResponse(res, sails.__('Failed to fetch access groups / readers.'), fetchError);
        }
    },

    unlockAllReaders: async (req, res) => {
        let optima_setting = req.project_optima_setting;
        if (!optima_setting.has_optima_source) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        let readers = [];
        try {
            readers = await getAllReadersList(optima_setting);
        } catch (failure) {
            sails.log.info(`Failed while fetching readers list`);
            return ResponseService.errorResponse(res, 'Failed while fetching readers list');
        }

        let done = [];
        let failed = [];
        sails.log.info(`Unlock All readers initiated by ${req.user.id} on: ${moment().format()}`);
        for (let i = 0, len = readers.length; i < len; i++) {
            let reader = {
                id: readers[i].id,
                name: (readers[i].name || readers[i].libelle)
            };
            let apiResponse = await callOptimaBoxGateway(optima_setting, {
                endpoint: `api/v1/Lecteur/Ouverture/${reader.id}`,
                method: 'POST',
            }, {});

            sails.log.info('Response from unlock n/w call', apiResponse.status);
            reader.response_data = apiResponse.data;
            (!apiResponse.error) ? done.push(reader) : failed.push(reader);
        }
        let message = `Unlocked ${done.length} reader(s) out of ${readers.length} readers.`;
        sails.log.info(message);
        return ResponseService.successResponse(res, {
            done,
            failed,
            message
        });
    },

    toggleOptimaAccess: async (req, res) => {
        let badgeNumber = +req.param('badge_number', 0);
        let projectId = req.param('projectId', 0);
        let updateRequest = {
            "groupId": req.body.groupId,
            "firstName": req.body.firstName,
            "lastName": req.body.lastName,
        };
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key || !badgeNumber) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        let outcome = await updateBadge(optima_setting, badgeNumber, updateRequest);
        if(outcome && outcome.success){
            return ResponseService.successResponse(res, {
                api_response : outcome.data
            });
        }
        return ResponseService.errorResponse(res, sails.__(`Failed to update access group of the badge number ${badgeNumber}.`));
    },

    optimaFingerprintEnrolment: async (req, res) => {
        let projectId = req.param('projectId', 0);
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        let enrolmentRequest = _.pick((req.body.enrolment_request || {}), [
            'badgeNumber',
            'readerId',
            'fingerprintIndex',
        ]);

        if(optima_setting.enrolment_type === 'facial' && enrolmentRequest.fingerprintIndex) {
            // for facial enrolment reader API
            delete enrolmentRequest.fingerprintIndex;
        }
        sails.log.info('enrolmentRequest', enrolmentRequest, 'enrolment_type:', optima_setting.enrolment_type);

        let apiResponse = await callOptimaBoxGateway(optima_setting, {
            endpoint: `Enrolment`,
            method: 'POST',
        }, enrolmentRequest);
        let message = '';
        let already_enrolled = false;
        sails.log.info(`Enrolment Response: status: ${apiResponse.status}, data:`, apiResponse.data);
        if (apiResponse.success && apiResponse.status === 200) {
            sails.log.info(`Enrolment successful.`, apiResponse.status);
            return ResponseService.successResponse(res, {
                api_response : apiResponse.data
            });
        } else {
            if (typeof apiResponse.data == 'string') {
               message = apiResponse.data;
               already_enrolled = (message === 'Slot is already in use');
            }else if(apiResponse.data && apiResponse.data.message){
                message = apiResponse.data.message;
            }
        }

        sails.log.error(`Failed to do enrolment. ${message}`);
        return ResponseService.errorResponse(res, `Failed to do enrolment. ${message}`, {already_enrolled});
    },

    retrieveBadgeInformation: async (req, res) => {
        let badgeNumber = +req.param('badge_number');
        let projectId = req.param('projectId', 0);
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        sails.log.info(`Retrieve information for badge number`, badgeNumber);
        let apiResponse = await callOptimaBoxGateway(optima_setting, {
            endpoint: `Badge/${badgeNumber}`,
            method: 'GET',
        }, {});

        let message = '';
        if (!apiResponse.success || apiResponse.status !== 200) {
            // message = HttpService.typeOf(apiResponse.data, 'string') ? apiResponse.data : message;
            return ResponseService.errorResponse(res, `Failed to get information of the badge number ${badgeNumber}.`);
        }

        let remote_enrolment = {};
        let has_enrolment = !!_get(apiResponse.data, 'usagerResponseModelApi.printsModel.faceEnrolled', 0);
        if (has_enrolment) {
            let remote_enrolment_response = await getRemoteEnrolment(optima_setting, badgeNumber);
            remote_enrolment = remote_enrolment_response.success ? remote_enrolment_response.data : remote_enrolment_response;
        }
        return ResponseService.successResponse(res, {
            has_enrolment,
            badge_information: apiResponse.data,
            remote_enrolment: remote_enrolment,
            has_remote_enrolment: (has_enrolment && remote_enrolment.fileType !== 'NA' && !!remote_enrolment.base64),
        });
    },

    deleteBadgeNumber: async (req, res) => {
        let badgeNumber = +req.param('badge_number');
        let projectId = req.param('projectId', 0);
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        sails.log.info(`Delete badge`, badgeNumber);
        let {success: deleted} = await deleteBadge(optima_setting, badgeNumber);
        if (deleted) {
            let where = {
                optima_badge_number: badgeNumber,
                project_ref: projectId
            };
            await sails.models.inductionrequest.updateOne(where).set({optima_badge_number: null});
            return ResponseService.successResponse(res, {
                api_response : deleted
            });
        }
        return ResponseService.errorResponse(res, sails.__(`Failed to delete the badge number ${badgeNumber}.`));
    },

    deleteFingerprintEnrolment: async (req, res) => {
        let badgeNumber = +req.param('badge_number');
        let fingerIndex = +req.param('finger_index');
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        if(optima_setting.enrolment_type === 'facial'){
            const readerTechnology = 4;
            let apiResponse = await callOptimaBoxGateway(optima_setting, {
                endpoint: `Enrolment/${badgeNumber}/${readerTechnology}`,
                method: 'DELETE',
            }, {},'v5');
            if (apiResponse.success) {
                return ResponseService.successResponse(res, {
                    deleted: true,
                    data: (apiResponse.data)
                });
            }
            else{
                return ResponseService.errorResponse(res, `Failed to delete enrolment for badge number ${badgeNumber}.`,{
                    optima_setting: req.project_optima_setting,
                    data: (apiResponse.data)
                });
            }
        }
        const TYPE_OF_FINGERPRINT = 1;
        sails.log.info(`Delete enrolment for badgeNumber: ${badgeNumber}, type: ${TYPE_OF_FINGERPRINT}, finger: ${fingerIndex}`);
        try {
            let apiResponse = await callOptimaBoxGateway(optima_setting, {
                endpoint: `Enrolment/${badgeNumber}/${TYPE_OF_FINGERPRINT}/${fingerIndex}`,
                method: 'DELETE',
            }, {});

            if (apiResponse.success) {
                return ResponseService.successResponse(res, {
                    deleted: true,
                    data: (apiResponse.data)
                });
            }
            else{
                return ResponseService.errorResponse(res, `Failed to delete enrolment for badge number ${badgeNumber}.`,{
                    optima_setting: req.project_optima_setting,
                    data: (apiResponse.data)
                });
            }
        } catch (fetchError) {
            sails.log.error(`Failed to delete enrolment for ${badgeNumber}.`, fetchError);
            return ResponseService.errorResponse(res, `Failed to delete enrolment for badge number ${badgeNumber}.`, fetchError);
        }
    },

    getEnrollmentStatus: async (req, res) => {
        let badgeNumber = +req.param('badge_number');
        let optima_setting = req.project_optima_setting;
        if (!optima_setting || !optima_setting.key) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }
        sails.log.info(`Poll for enrolment status`, badgeNumber);
        try {
            let apiResponse = await callOptimaBoxGateway(optima_setting, {
                endpoint: `Enrolment/pool/${badgeNumber}`,
                method: 'GET',
            }, {});

            if (apiResponse.success && (apiResponse.data.enrolmentResult === 0 || apiResponse.data.enrolmentResult === 1)) {
                return ResponseService.successResponse(res, {
                    optima_setting: req.project_optima_setting,
                    pending: true,
                    data: (apiResponse.data)
                });
            }
            else if (apiResponse.success && apiResponse.data.enrolmentResult === 2) {
                return ResponseService.successResponse(res, {
                    optima_setting: req.project_optima_setting,
                    enrolled: true,
                    data: (apiResponse.data)
                });
            }else{
                return ResponseService.errorResponse(res, `Failed to check enrolment status for badge number ${badgeNumber}.`,{
                    optima_setting: req.project_optima_setting,
                    data: (apiResponse.data)
                });
            }
        } catch (fetchError) {
            sails.log.error(`Failed to poll for enrolment Status ${badgeNumber}.`, fetchError);
            return ResponseService.errorResponse(res, `Failed to check enrolment status for badge number ${badgeNumber}.`, fetchError);
        }

        /*count++;
        if(count >= 3){
            return ResponseService.successResponse(res, {
                optima_setting: req.project_optima_setting,
                enrolled: true,
            });
        }
        return ResponseService.successResponse(res, {
            optima_setting: req.project_optima_setting,
            pending: true,
        });*/
    },

    optimaEventListener: async (req, res) => {
        let called_at = (new Date()).getTime();
        let token = req.param('token', '--');
        if(token !== sails.config.custom.OPTIMA_LISTENER_KEY){
            res.status(401);
            return res.send(ResponseService.authErrorObject('Un-authorized access', true));
        }
        AccessLogService.logRequest({
            path: `${req.method}: ${req.path}`,
            action: 'optima/optimaeventlistener',
            user_agent: req.headers['user-agent'],
            platform: 'headless:' + req.param('site_id', ''),
            auth_token: token,
            ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
        }).catch(sails.log.error);

        // @todo: spatel: joi Validation
        // try{require('fs').writeFileSync(require('path').resolve(process.cwd(), `../json_dump-${req.param('site_id', '')}-${(new Date()).getTime()}.json`), JSON.stringify((req.body || {})));}catch (e) { console.error('Writing dump',e);}
        let outcome = await eventListener(req.body || {});
        let finished_at = (new Date()).getTime();
        AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.OPTIMA, req.param('site_id', ''), !outcome.error, (outcome.code || 200), {
            method: 'POST',
            url: null,
            payload: (req.body || {}),
            headers: (req.headers || {}),
            response_body: outcome,
            response_headers: ({})
        }, (finished_at - called_at), (req.options && req.options.action), 'notification').catch(sails.log.error);

        if(outcome.error){
            res.status(outcome.code || 500);
            return res.send(outcome);
        }
        return ResponseService.successResponse(res, outcome);
    },

    addManualGeoFenceEntries: async (req, res) => {
        let projectId = +req.param('projectId');
        let force_create = (req.body.create || '--') === true;
        let timezone = req.body.timezone || fall_back_timezone;
        sails.log.info('Add Manual Geo-fence entries: timezone:', timezone, ' projectId:', projectId, 'force_create:', force_create);
        let payload = req.body.logs || [];
        if(!Array.isArray(payload) || !payload.length){
            return ResponseService.errorResponse(res, 'Invalid request');
        }

        let logs = payload.map(l => {
            return {
                user_ref: +l.user_ref || null,
                visitor_ref: +l.visitor_ref || null,
                project_ref: projectId,
                user_revision_ref: null,
                user_location: {
                    ...(l.user_location || {}),
                    "is_manual": true
                },
                extras: {
                    ...(l.extras || {}),
                    creator: req.user.id,
                    declarations: l.declarations
                },
                event_date_time: +l.event_time,
                event_type: l.event_type.toString().toUpperCase().trim(),
                attachment_ref: l.attachment_ref || null,
            };
        });
        sails.log.info('Total records to create', logs.length);
        let userIds = [];
        let users = [];
        logs.forEach(log=> {
            if(log.user_ref) {
                userIds.push(log.user_ref);
            }
        });
        if(userIds.length) {
            users = await sails.models.user_reader.find({where: {id: _uniq(userIds)}, select: ['id', 'parent_company']});
        }
        for (let i = 0, len = logs.length; i < len; i++) {
            if(logs[i].user_ref) {
                let row = logs[i];
                let user = users.find(u=> u.id === row.user_ref);
                if(!user || !user.id){
                    sails.log.error('Invalid User Id', row);
                    // return ResponseService.errorResponse(res, 'user_id not found.', {row});
                }
                let revision = await getLatestUserRevision(row.user_ref);
                logs[i].user_revision_ref = revision.id;
                logs[i].company_ref = (user && user.parent_company) || undefined;
            }

        }

        if(!force_create){
            sails.log.info('Skipping creation of user time log, length:', logs.length);
            return ResponseService.successResponse(res, {user_time_log: logs, created: false});
        }

        sails.log.info('Storing user time log, length:', logs.length);
        let newRecords = await sails.models.usertimelog.createEach(logs);
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['custom_field']
        });
        await ingestTimeLogs(newRecords, projectInfo);
        return ResponseService.successResponse(res, {user_time_log: newRecords, created: true});
    },

    storeBulkInnTimeAppLogs: async (req, res) => {
        let {validationError, payload: {events}} = TimeLogValidator.bulkInnTimeLogRequest(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'All fields required.', {validationError});
        }

        let uniq_ids = _uniq(events.map(e => e.user_id));
        let users = await sails.models.user_reader.find({where: {id: uniq_ids}, select: ['id', 'parent_company']});
        sails.log.info('Total bulk records to create', events.length);

        let logs = [];
        let now_seconds = moment().unix();
        for (let i = 0, len = events.length; i < len; i++) {
            let row = events[i];
            let user = users.find(u => u.id === row.user_id) || {};
            if(!user.id){
                sails.log.error('Invalid User Id', row);
                continue;
            }
            let revision = await getLatestUserRevision(row.user_id);
            logs.push({
                project_ref: req.project.id,
                user_location: row.user_location,
                event_type: row.event_type,
                event_date_time: now_seconds,
                temperature: row.temperature,

                attachment_ref: row.attachment_ref,
                user_ref: user.id,
                company_ref: (user.parent_company) || undefined,
                user_revision_ref: revision.id,
                extras: {
                    ...(row.extras || {}),
                    declarations: row.declarations
                },
            });
        }

        sails.log.info('Storing user time logs, length:', logs.length);
        let newRecords = await sails.models.usertimelog.createEach(logs);
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: req.project.id},
            select: ['custom_field']
        });
        await ingestTimeLogs(newRecords, projectInfo);

        return ResponseService.successResponse(res, {user_time_logs: newRecords, created: true});
    },

    getClockingDeclarationAnswers: async (req, res) => {
        let project_ref = +req.param('projectId');
        let user_ref = +req.param('userId');

        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 50);

        if(isNaN(project_ref) || isNaN(pageNumber) || isNaN(limit)){
            sails.log.info('Invalid Request for getClockingDeclarationAnswers');
            return ResponseService.errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info('fetch geo-fence declarations for project', project_ref, 'user:', user_ref, `page: ${pageNumber} skip: ${skip}`);
        let total = await sails.models.usertimelog_reader.count({
            event_type: [EVENT_TYPE.IN],
            project_ref,
            user_ref,
            extras: {'!=': {}}
        });
        let events = await sails.models.usertimelog_reader.find({
            select: ['event_date_time', 'extras', 'event_type'], // 'user_location'
            where: {
                event_type: [EVENT_TYPE.IN],
                project_ref,
                user_ref,
                extras: {'!=': {}}
            },
            skip,
            limit,
            sort: 'event_date_time DESC'
        });

        sails.log.info(`responding with geo-fence declarations, count:${events.length}`);
        return ResponseService.successResponse(res, {
            events,
            pageNumber,
            limit,
            total,
        });
    },

    importMissingOptimaEvents: async (req, res) => {
        let projectId = +req.param('projectId');
        let optima_setting = req.project_optima_setting;
        let force_create = (req.body.create || '--') === true;
        let filter = _.pick((req.body || {}), [
            'page',
            'descending',
            'startDate',
            'endDate',
            'eventTypeIds',
        ]);
        sails.log.info('Importing Optima Events from history, projectId:', projectId, 'force_create:', force_create);
        if (!optima_setting || !optima_setting.has_optima_source) {
            return ResponseService.errorResponse(res, sails.__('optima configuration not enabled'));
        }

        let requestBody = Object.assign({
            "page": 1,
            "descending": false,
            "startDate": "16-06-2021 00:00:00",
            "endDate": "17-06-2021 00:00:00",
            "eventTypeIds": [
                0
            ],
        }, filter);
        sails.log.info('Lookout filter', requestBody);
        let eventHistoryResponse = await callOptimaBoxGateway(optima_setting, {
            endpoint: 'EventHistory',
            method: 'POST',
        }, requestBody);
        if(!force_create){
            return ResponseService.successResponse(res, eventHistoryResponse.data);
        }
        if(!eventHistoryResponse.data || !eventHistoryResponse.data.result){
            return ResponseService.errorResponse(res, 'No events found');
        }
        let rawRows = eventHistoryResponse.data.result || [];
        sails.log.info(`Requesting import of : ${rawRows.length} records`);
        let data = await importOptimaLogs(req.project_optima_setting, rawRows);
        return ResponseService.successResponse(res, {data})
    },

    // v3 API for members screen
    getProjectMembers: async (req, res) => {
        let project_id = +req.param('projectId');
        let employer_id = +req.param('employerId', 0);
        let nowMs = +req.param('nowMs', 0);

        // for company route only..
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');

        if (!nowMs || !project_id) {
            return ResponseService.errorResponse(res, 'project_id & nowMs is required');
        }

        let day_of_yr_today = moment(nowMs);
        sails.log.info(`get project "${project_id}" members nowMs: ${nowMs}, employer: ${employer_id}`);
        // get all members
        // get their competencies
        // check if they are on-site
        if(!employer_id){
            let approved_inductions_output = await sails.sendNativeQuery(`
                -- get members call
                SELECT id, user_ref,
                       creator_name,
                       user_doc_ids,
                       additional_data->'user_info'->'profile_pic_ref' as profile_pic_ref,
                       additional_data->'employment_detail' as employment_detail,
                       additional_data->'user_docs' as user_docs,
                       additional_data->'contact_detail' as contact_detail
                FROM induction_request
                WHERE project_ref = $1 and status_code IN (2,4,6);`,  // `approved` OR `Project Block` OR `In review` one only
                [project_id]
            );
            let approved_inductions = (HttpService.typeOf(approved_inductions_output.rows, 'array') && approved_inductions_output.rows.length) ? approved_inductions_output.rows : [];

            sails.log.info(`total ${approved_inductions.length} inductions for project "${project_id}" members`);
            let induction_requests = await expandUserDocs(approved_inductions);

            let user_ids = _uniq(approved_inductions.map(induction => +induction.user_ref));
            let time_logs_for_day = await getDailyTimeEventForDay(project_id, day_of_yr_today, user_ids);

            // Get project country code for competency filtering
            let project = await sails.models.project_reader.findOne({
                select: ['custom_field'],
                where: { id: project_id }
            });
            let country_code = (project && project.custom_field && project.custom_field.country_code) || 'GB';

            // Fetch competency mappings once for all users (optimized approach)
            const competencyMappings = await getCompetencyMappings(country_code);

           let members = await Promise.all( induction_requests.map(async (induction) => {
                const contact_detail = induction.contact_detail;
                let { employer, job_role, employment_company } =
                    induction.employment_detail || {};
                let user_documents = induction.user_doc_ids || []; //.filter(d => (d && d.doc_owner_id === induction.user_ref));
                let user_log_of_day = time_logs_for_day.find(
                    (l) => l.user_id === induction.user_ref
                );
                let profile_pic = induction.profile_pic_ref
                    ? induction.profile_pic_ref.sm_url ||
                      induction.profile_pic_ref.file_url
                    : null;

                // Use optimized competency functions with pre-fetched mappings
                const is_first_aider = await isFirstAider(user_documents, competencyMappings);
                const has_supervisor = await haveSMSTSOrSSSTSDoc(user_documents, competencyMappings);
                const has_fire_marshal = await haveFireMarshal(user_documents, competencyMappings);

                return {
                    id: induction.id,
                    user_id: induction.user_ref,
                    name: induction.creator_name,
                    profile_pic,
                    employer,
                    job_role,
                    employment_company,
                    mobile_no: (contact_detail && contact_detail.mobile_no),
                    mobile_number: (contact_detail && contact_detail.mobile_number),
                    event_of_today: user_log_of_day,
                    is_still_on_site: isStillOnSite(user_log_of_day),
                    is_first_aider: is_first_aider || null,
                    has_SMSTS_or_SSSTS: has_supervisor || null,
                    has_fire_marshal: has_fire_marshal || null,
                };
            }))

            ResponseService.successResponse(res, {
                members
            });

        }else{
            // company project
            ResponseService.successResponse(res, {
                members: []
            });
        }
    },

    getProjectOnSiteUsersAccessPoints: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let nowMs = +req.param('nowMs', 0);
        if (!projectId || !nowMs) {
            return ResponseService.errorResponse(res, 'project id & nowMs is required');
        }
        sails.log.info('fetch on site users for project', projectId, 'now', nowMs);
        let time_log_for_day = await getDailyTimeEventForDay(projectId, moment(nowMs), [], false, null, false, true);
        let daily_logs = time_log_for_day.filter(isStillOnSite);
        sails.log.info('Total on site users', daily_logs.length);
        if(!daily_logs.length){
            return ResponseService.successResponse(res, {members_w_location: []});
        }

        let users_locations = await deriveOnSiteUsersLocation(daily_logs, projectId);
        let on_site_users_inductions = await sails.models.inductionrequest_reader.find({
            where: {
                user_ref : (Object.keys(users_locations)),
                project_ref: +projectId
            },
            select: ['user_ref', 'additional_data']
        });
        let user_induction_info = (on_site_users_inductions || []).reduce((user_info_obj, induction) => {
            let user_info = (induction.additional_data && induction.additional_data.user_info) || {};
            let employment_detail = (induction.additional_data && induction.additional_data.employment_detail) || {};
            user_info_obj[induction.user_ref] = {
                id: induction.user_ref,
                name: (user_info.name || user_info.first_name) || '',
                employer: (employment_detail.employer) || '',
                job_role: (employment_detail.job_role) || '',
            };
            return user_info_obj;
        }, {});

        let members_w_location = (daily_logs || []).map(log => {
            return {
                ...(user_induction_info[log.user_id] || {}),
                location: (users_locations[log.user_id] || ''),
                recent_in: log.recent_in
            };
        });
        sails.log.info(`Total members_w_location of project: ${projectId} are:`, members_w_location.length);
        return ResponseService.successResponse(res, {members_w_location});
    },

    getProjectMemberDailyEvents: async (req, res) => {
        let project_id = +req.param('projectId');
        let employer_id = +req.param('employerId', 0);
        let user_ids = req.param('user_ids', []);

        // for company route only..
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');

        if(!project_id || !user_ids.length){
            return ResponseService.errorResponse(res, 'project_id & user_ids is required');
        }

        sails.log.info(`get project "${project_id}" member daily events user: ${user_ids}`);
        let daily_logs = await getAllDailyTimeEvent(project_id, user_ids, employer_id);
        sails.log.info(`got member daily events, total:`, daily_logs.length);
        ResponseService.successResponse(res, {daily_logs});
    },

    getUserEventByDay: async (req, res) => {
        return getUserEventByDay(req, res, req.user.id, +req.param('projectId', 0));
    },

    getInnTimeUserEventByDay: async (req, res) => {
        return getUserEventByDay(req, res, +req.param('userId', 0), req.project.id);
    },

    downloadProjectCarbonEmissionsReportXLSX: async (req, res) => {
        sails.log.info("post request for downloadProjectCarbonEmissionsReportXLSX")
        let projectId = req.param('projectId');
        let from_date = moment((req.body.from_date || '--'), dbDateFormat);
        let to_date = moment((req.body.to_date || '--'), dbDateFormat);

        if (!from_date.isValid() || !to_date.isValid()) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid from_date filter provided.',{from_date, to_date});
        }
        from_date = from_date.format(dbDateFormat);
        to_date = to_date.add(1, 'days').format(dbDateFormat);
        sails.log.info(`total-time report from, to: ${from_date}  <= date < ${to_date}`);
        let where = {
            status_code: [2, 6],
            project_ref: projectId
        };
        sails.log.info('Where filter is ', where);
        let induction_requests = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'user_ref', 'additional_data', 'travel_time', 'travel_method', 'optima_badge_number', 'status_code'],
            sort: ['id desc'],
            where
        });

        let user_ids = _uniq(induction_requests.map(ir => ir.user_ref));
        sails.log.info(`Find time records of user_ids count: ${(user_ids || []).length}`);
        if(!user_ids.length){
            // invalid badge id in mobile app request can trigger this
            sails.log.info('No induction record found, for this user', req.singleUserOnly);
            //return ResponseService.errorResponse(res, 'provided badge id not found.',{request: where});
        }
        let daily_logs = await getDailyTimeEventV2(projectId, from_date, to_date, user_ids, req.singleUserOnly, null);
        let project;
        daily_logs = daily_logs.reverse();
        let default_in_duration = 0;
        if(daily_logs.length){
            project = (await sails.models.project_reader.findOne({ where: {id: projectId}, select: ['id', 'name', 'default_in_duration', 'contractor', 'project_number']})) || {};

            if(project.default_in_duration && !isNaN(project.default_in_duration)){
                default_in_duration = convertDecimalHrToDuration(project.default_in_duration);
            }
        } else {
            return ResponseService.errorResponse(res, 'Workers Travel Carbon can not be downloaded due to insufficient data',{from_date, to_date});
        }
        let total_operatives = [];
        let total_time = daily_logs.reduce((t, log) => {
            if(!total_operatives.includes(log.user_id)){
                total_operatives.push(log.user_id);
            }
            let effective_time = (log.effective_time ? +log.effective_time : (default_in_duration + ((log.adjustment || 0) * 60)));
            return (t + effective_time)
        }, 0);
        let totalCompanies = [];
        induction_requests.forEach(ireq=> {
            if(!totalCompanies.includes(ireq.additional_data.employment_detail.employer)){
                totalCompanies.push(ireq.additional_data.employment_detail.employer);
            }
        });

        let daily_footprint = await buildDailyCarbonFootprintByEmployers(induction_requests, daily_logs , total_operatives, totalCompanies);
        let workbook = await getProjectEmissionsReportWorkbook(daily_footprint);
        let fileName = `${project.contractor}-${project.project_number}-${project.name}-Workers Travel Carbon.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },
};
