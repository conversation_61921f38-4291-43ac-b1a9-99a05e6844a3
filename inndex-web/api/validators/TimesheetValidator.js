const Joi = require('joi');
const { logIfError, commentSchema } = require('./SharedSchema');

module.exports = {
    saveTimesheetInfoValidator: (req) => {
        const schema = Joi.object({
            timesheets: Joi.array().items(
                Joi.object().keys({
                    id: Joi.number(),
                    day_of_yr: Joi.string(),
                    user_ref: Joi.number(),
                    status: Joi.number(),
                    actual_seconds: Joi.number().allow(null),
                    day_seconds: Joi.number().allow(null),
                    night_seconds: Joi.number().allow(null),
                    hours_state: Joi.number(),
                    travel_seconds: Joi.number().allow(null),
                    overtime_seconds: Joi.number().allow(null),
                    training_seconds: Joi.number().allow(null),
                    manager_auth_seconds: Joi.number().allow(null),
                    price_work_amount: Joi.number().allow(null).max(99999),
                }).unknown(false)
            ).min(1).required(),
        });

        const { error: validationError, value: payload } = schema.validate(req.body, { allowUnknown: true });
        logIfError('[saveTimesheetInfoValidator]', validationError);
        return { validationError, payload };
    },

    saveTsCommentValidator: (req) => {
        const schema = Joi.object({
            // id: Joi.number(),
            week_end_date: Joi.string(),
            user_ref: Joi.number(),
            comment: commentSchema.required().unknown(true),
        });

        const { error: validationError, value: payload } = schema.validate(req.body, { allowUnknown: true });
        logIfError('[saveTsCommentValidator]', validationError);
        return { validationError, payload };
    },

    saveTsWorkerRecordValidator: (req) => {
        const schema = Joi.object({
            user_ref: Joi.number().required(),
            week_end_date: Joi.string().required(),
            employee_number: Joi.string().allow(null, ''),
            hourly_rate: Joi.number().allow(null, ''),
        }).or('employee_number', 'hourly_rate');

        const { error: validationError, value: payload } = schema.validate(req.body, { allowUnknown: true });
        logIfError('[saveTsWorkerRecordValidator]', validationError);
        return { validationError, payload };
    },
};
