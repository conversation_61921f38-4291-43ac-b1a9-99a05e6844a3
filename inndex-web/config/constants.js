/**
 * Created by spatel on 01/07/20.
 */

const PRODUCTION_MODE = (process.env.APP_ENV === 'production');
const PRO_CORE_LIVE_MODE = (process.env.PRO_CORE_LIVE_MODE === 'true');
module.exports.constants = {

    displayDateFormat_DD_MM_YYYY: 'DD-MM-YYYY',
    displayDateFormat_D_MMM_Y: 'D/MMM/YYYY',

    displayTimeFormat_HH_mm_ss: 'HH:mm:ss',

    dbDateFormat_YYYY_MM_DD: 'YYYY-MM-DD',

    dbDateFormat_slash_DDMMYYYY: 'DD/MM/YYYY',

    dbDateFormat_YYYY_MM_DD_HH_mm_ss: 'YYYY-MM-DD HH:mm:ss',

    dateFormat_DD_MM_YYYY_HH_mm_ss: 'DD/MM/YYYY HH:mm:ss',

    displayDateFormat_DD_MM_YYYY_HH_mm_ss : 'DD-MM-YYYY HH:mm:ss',

    fall_back_timezone: 'Europe/London',
    fallback_locale: 'en-GB',

    maxPermissibleTemperature: +(37.8),

    defaultUserPassword: 'innDexRocks@321',

    kmToMiles: (km) => (km * 0.621371),

    DEFAULT_PAGE_SIZE: 50,

    CURRENCY_SYMBOL: {
        EUR: '€',
        GBP: '£',
        USD: '$',
    },

    getShadowUserEmail: (suffix) => `info+shadow${suffix}@inndex.co.uk`,

    isShadowUser: (user) => (
        (user.email.toString().startsWith('aeddanberrynz+shadow') && user.email.toString().endsWith('@gmail.com')) ||
        (user.email.toString().startsWith('info+shadow') && user.email.toString().endsWith('@inndex.co.uk'))
    ),

    touchByteAPI: {
        date_format: 'YYYY-MM-DD HH:mm:ss',

        GET_site_info: (site) => `/api/site?site=${site}`,

        GET_user_info: (username) => `/api/user?username=${username}`,

        POST_create_user: () => `/api/user`,

        GET_list_all_doors: (site) => `/api/doors?site=${site}`,

        GET_user_access: (site, username) => `/api/access/permission?site=${site}&username=${username}`,

        POST_grant_user_access: () => `/api/access/permission`,

        DELETE_revoke_user_access: () => `/api/access/permission`,

        GET_time_logs: (site, date_YYYY_MM_DD_HH_mm_ss) => `/api/access/log?site=${site}&date=${date_YYYY_MM_DD_HH_mm_ss}`,
    },

    DVLA_API: {
        POST_vehicle_enquiry_by_reg_no: '/vehicle-enquiry/v1/vehicles',
    },

    COUNTRIES_META_FILE: 'countries.meta.json',

    DATA_STORES: {
        default: 'default',
        storage: 'storage',
        reader: 'reader',
    },

    ALMAS_APIs: {
        REMOTE_ENROLMENT_API: 'remoteEnrolment',
    },
    INNDEX_SLS_FN_NAME: {
        CROP_IMAGE_TO_FACE: `${PRODUCTION_MODE ? `inndex-sls-production` : `inndex-sls-dev`}-crop-image-to-face-v1`,
        LAMBDA_UPDATE_VIDEO_CODEC_FN: `${PRODUCTION_MODE ? `inndex-sls-production` : `inndex-sls-dev`}-update-video-codec-v1`,
    },

    EMAIL_NOTIFICATION_FEATURE_CODES: {
        SITE_MESSAGING: 'site-messaging',
        COMPANY_SITE_MESSAGING: 'company-site-messaging',
        FORGOT_PASSWORD: 'forgot-password',
        NEW_PROJECT_CREATED: 'new-project-created',
        COMPETENCY_EXPIRY_ALERT_FOR_PROJECT_MANAGER: 'competency-expiry-alert-for-project-manager',
        COMPETENCY_EXPIRY_ALERT_FOR_MANAGER: 'competency-expiry-alert-for-manager',
        NEW_INDUCTION_CREATED: 'new-induction-created',

        PLAIN_MESSAGE_ALERT: 'plain-message-alert',
        PERMIT_TOOL: 'permit-tool',
        SHARE_TOOL_REPORT: 'share-tool-report',
    },


    INNDEX_SETTING_KEY: {
        PROCORE_MP_CRON_OVERRIDE: `procore_man_power_cron_override`,
        VEHICLE_TYPE_LIST: `vehicle_type_list`,
        TEST_PROJECT_IDs: `test_project_ids`,
        CLOSE_CALL_CATEGORY_EN_GB : 'close_call_category_en_gb',
        CLOSE_CALL_CATEGORY_EN_US : 'close_call_category_en_us',
        CLOSE_CALL_CATEGORY_DE_DE : 'close_call_category_de_de',
        GOOD_CALL_CATEGORY_EN_US : 'good_call_category_en_gb',
        GOOD_CALL_CATEGORY_EN_GB : 'good_call_category_en_us',
        GOOD_CALL_CATEGORY_DE_DE : 'good_call_category_de_de',
        OBSERVATION_CATEGORY_EN_GB : 'observation_category_en_gb',
        OBSERVATION_CATEGORY_EN_US : 'observation_category_en_us',
        OBSERVATION_CATEGORY_DE_DE : 'observation_category_de_de',
        META_LIGHTING_CONDITIONS_EN_GB: 'meta_lighting_conditions_en_gb',
        META_LIGHTING_CONDITIONS_EN_US: 'meta_lighting_conditions_en_us',
        META_LIGHTING_CONDITIONS_DE_DE: 'meta_lighting_conditions_de_de',
        TYPE_OF_ASSET_VEHICLES_EN_GB: 'type_of_asset_vehicles_en_gb',
        TYPE_OF_ASSET_EQUIPMENTS_EN_GB: 'type_of_asset_equipments_en_gb',
        TYPE_OF_TEMPORARY_WORKS_EN_GB: 'type_of_temporary_works_en_gb',
        VEHICLE_ASSET_INSPECTION_CHECKLIST: 'vehicle_asset_inspection_checklist',
        EQUIPMENT_ASSET_INSPECTION_CHECKLIST: 'equipment_asset_inspection_checklist',
        TEMPORARY_WORK_ASSET_INSPECTION_CHECKLIST: 'temporary_work_asset_inspection_checklist'
    },

    COMPANY_SETTING_KEY: {
        INCIDENT_REPORT_ACTION_CATEGORY_CONFIG: 'incident_report_action_category_config',
        FACIAL_RECOGNITION_CONFIG: 'facial_recognition_config',
        SKILL_MATRIX_EXEMPTION_CONFIG: 'skill_matrix_exemption_config',
        A_SITE_INTEGRATION_CONFIG: 'a_site_integration_config',
        TIMEZONE: 'timezone',
        PROCORE_CONFIG: 'company_procore_config',
        COMPANY_INDUCTION: 'company_induction',
        OKTA_SSO_SETTING: 'okta_sso_setting',
        CSCS_CONFIG: 'company_cscs_config',
        RTW_CHECK_CONFIG: 'company_rtw_check_config',
        FEATURES_STATUS: 'features_status',
        QR_CODE_CONFIG: 'qr_code_config',
        SSO_CONFIGURATION: 'sso_configuration',
        PROJECT_FEATURE_PERMISSION: 'project_feature_permission', // Used by super-admin to toggle features for this company
        ADDITIONAL_SETTINGS: 'additonal_settings',
        COMPANY_ALLOWED_DOMAIN: 'company_allowed_domain',
    },
    PROJECT_SETTING_KEY: {
        MANDATORY_COMPETENCY_EXCEPTION_USERS: 'mandatory_competency_exception_users',
        RAMS_ASSESSMENT_FORM: 'rams_assessment_form',
        SMART_SHEET_APP_INFO: `smart_sheet_app_info`,
        INDUCTION_SLOTS_INFO: `project_induction_slots`,
        PROCORE_INTEGRATION_PROJECT_INFO: `procore_project_and_token_info`,
        UK_DISTRICTS: 'uk_districts',
        A_SITE_PROJECT_CONFIG: 'asite_project_config',
        OBSERVATION_SETTING: 'observation_setting'
    },

    AUTH_SERVER_URL: PRO_CORE_LIVE_MODE ? "https://login.procore.com" : "https://login-sandbox.procore.com",
    API_SERVER_URL:  PRO_CORE_LIVE_MODE ? "https://api.procore.com" : "https://sandbox.procore.com",
    CALL_BACK_URL: "procore/auth/callback",

    // "app": "https://dev-portal-production.procore.com/developer_apps/eb9bf808-cf19-413b-b2d3-52bfc58b5d46",
    // https://developers.procore.com/developer_apps/8e273cd9-d80b-458c-9a0c-ea474fcce6ff # innDex
    PROCORE_APIs: {
        OAUTH_AUTHORIZE_URL: `oauth/authorize`,
        OAUTH_TOKEN_URL: `oauth/token`,
        GET_ME_URL: `rest/v1.0/me`,
        GET_COMPANIES_URL: `rest/v1.0/companies`,
        GET_PROJECTS_URL: `rest/v1.0/projects`,
        PROJECT_TIMESHEETS: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/timesheets`,
        PROJECT_VENDORS: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/vendors`,
        PROJECT_USERS: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/users`,
        PROJECT_USER_INFO: (PROJECT_ID, USER_ID) => `rest/v1.0/projects/${PROJECT_ID}/users/${USER_ID}`,
        PROJECT_TIMECARD_ENTRIES: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/timecard_entries`,
        PROJECT_VISITOR_LOG: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/visitor_logs`,
        PROJECT_MANPOWER_LOGS: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/manpower_logs`,
        PROJECT_PERMISSION_TEMPLATES: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/permission_templates`,
        PROJECT_OBSERVATION_TYPES: (PROJECT_ID) => `rest/v1.0/projects/${PROJECT_ID}/observation_types`,
        CREATE_OBSERVATION_ITEM: `rest/v1.0/observations/items`,
        COMPANY_HAZARDS: (COMPANY_ID) => `rest/v1.0/companies/${COMPANY_ID}/hazards`,
    },

    CITB_V2_APIs: {
        AUTHENTICATE: `smarttech/v2/authenticate`,
        CARD: `smarttech/v2/card`,
        REFRESH_TOKEN: `smarttech/v2/refreshtoken`,
    },
    RTW_CHECK_APIs: {
        GET_STATUS: `api/v1/rtw/status`,
        UPDATE_WEBHOOK: `api/v1/rtw/status`,
        UPDATE_STATUS: `api/v1/rtw/status/update`,
    },

    AZURE_APIs: {
        AZURE_AUTH_SERVER_URL: `https://login.microsoftonline.com`,
        OAUTH_AUTHORIZE_URL: `oauth2/v2.0/authorize`,
        OAUTH_TOKEN_URL: `oauth2/v2.0/token`,

        OAUTH_SCOPE: `openid user.read`,

        AZURE_CALLBACK_ROUTE: `azure/auth/callback`,

        GRAPH_ME_INFO_URL: `https://graph.microsoft.com/v1.0/me`,
    },

    POWERBI_APIs: {
        INNDEX_SETTING_NAME: 'power_bi_data',
        AUTH_SERVER_URL: `https://login.microsoftonline.com`,
        OAUTH_TOKEN_URL: `oauth2/v2.0/token`,
        GRANT_TYPE: `client_credentials`,
        SCOPE: 'https://analysis.windows.net/powerbi/api/.default',
        API_URL: 'https://api.powerbi.com/v1.0/myorg'
    },

    OKTA_APIs: {
        issuer_URL: `oauth2`,
        authorizationURL: `oauth2/v1/authorize`,
        tokenURL: `oauth2/v1/token`,
        userInfoURL: 'oauth2/v1/userinfo',
        scope: 'openid profile',

        OKTA_CALLBACK_ROUTE: `okta/auth/callback`,
    },

    ASSET_TYPES: {
        AssetEquipment: 'asset-equipment',
        AssetVehicle: 'asset-vehicle',
        AssetTemporaryWork: 'asset-temporary',
        AccessEquipment: 'access_equipment'
    },

    ASSET_TYPE_DATA : {
        'asset-equipment': { key: 'equipment', databaseTableReader: 'projectassetequipment_reader', databaseTable: 'projectassetequipment', title: 'Equipment', inspectionTypeKey: 'equipment_ref', inpsectionDBTableReader:'assetequipmentinspection_reader', inpsectionDBTable:'assetequipmentinspection', typeKey: 'equipment_type', photosKey: 'equipment_photos', itemKey: 'equipment_id'},
        'asset-vehicle': { key: 'vehicle', databaseTableReader: 'projectassetvehicles_reader', databaseTable: 'projectassetvehicles', title: 'Vehicle', inspectionTypeKey: 'vehicle_ref',inpsectionDBTableReader:'assetvehicleinspection_reader', inpsectionDBTable:'assetvehicleinspection', typeKey: 'type_of_vehicle', photosKey: 'vehicle_photos', itemKey: 'vehicle_id'},
        'asset-temporary': { key: 'temporary', databaseTableReader: 'projectassettemporarywork_reader', databaseTable: 'projectassettemporarywork', title: 'Temporary Works', inspectionTypeKey: 'temporary_work_ref', inpsectionDBTableReader:'assettemporaryworkinspection_reader', inpsectionDBTable:'assettemporaryworkinspection', typeKey: 'type_of_works', photosKey: 'photos', itemKey: 'item_id'},
    },

    FEATURES: {
        PERMIT_TOOL: 'permit_tool',
        PERMIT_REGISTER: 'permit_register',
    },

    INNDEX_SLS_ARN: {
        CONSTANT_LAMBDA_TEST_EMAIL_FN: 'LAMBDA_TEST_EMAIL_FN',
        CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE: 'SYNC_DAILY_BRIEFINGS_TO_ASITE',
        CONSTANT_SYNC_DAILY_PROGRESS_PHOTOS_TO_ASITE: 'SYNC_DAILY_PROGRESS_PHOTOS_TO_ASITE',
        CONSTANT_ACTIVE_PERMIT_FN_ARN: 'ACTIVE_PERMIT_FN_ARN',
        CONSTANT_MARK_PERMIT_EXPIRE_FN_ARN: 'MARK_PERMIT_EXPIRE_FN_ARN',
        CONSTANT_NOTIFY_BEFORE_PERMIT_EXPIRE_FN_ARN: 'NOTIFY_BEFORE_PERMIT_EXPIRE_FN_ARN',
    },

    INSPECTION_ID: [643, 988]
};
