/**
 * Created by spatel on 03/02/21.
 */

const {
    DataProcessingService: {
    },
    HttpService: {
        typeOf,
    },
    EmailService: {
        sendRawEmail,
    },
    ResponseService: {
        errorResponse,
        successResponse
    }
} = require('./../services');
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {DEFAULT_PAGE_SIZE, COMPANY_SETTING_KEY,} = sails.config.constants;
const {
    NotificationsValidator: {
        updateNotifications
    }
} = require('./../validators');

module.exports = {
    inviteToInduction: async (req, res) => {
        let project_ref = req.param('projectId');
        let recipients = req.body.recipients || [];

        let project = await sails.models.project.findOne({
            where: {id: project_ref},
            select: [
                'name',
                'induction_pin',
                'qrcode_image',
                'project_initial',
                'use_prefix',
                'contractor',
                'custom_field'
            ]
        });
        let contractor = await sails.models.createemployer_reader.findOne({
            where: {
                name: project.contractor,
                country_code: (project.custom_field && project.custom_field.country_code)
            },
            select: ['id', 'name', 'company_initial'],
        });

        let show_company_induction_step = false;
        let contractor_info = {};
        if(contractor){
            sails.log.info(`Looking for Company induction settings of company: ${contractor.id}`);
            let company_induction_setting  = await sails.models.companysetting_reader.findOne({
                where: {
                    company_ref: contractor.id,
                    name: COMPANY_SETTING_KEY.COMPANY_INDUCTION,
                }
            });
            show_company_induction_step = (company_induction_setting && company_induction_setting.value && company_induction_setting.value.status);
            if(show_company_induction_step){
                contractor_info = {
                    ...contractor,
                    singlr_phrasing: company_induction_setting.value.phrasing ? company_induction_setting.value.phrasing.singlr : 'Company Induction'
                };
            }
        }
        sails.log.info(`send invite for induction of project ${project_ref}, show company induction step: ${show_company_induction_step}`);

        // Validate data
        if (!typeOf(recipients, 'array') ||
            !recipients.length ||
            (recipients.filter(r => r.email).length === 0) ||
            !project ||
            !project.id
        ) {
            sails.log.info('Invalid Request for invite');
            return errorResponse(res, 'recipients list & project id should be valid', {recipients, project});
        }
        project.record_id = project.id.toString();
        if(project.project_initial && project.use_prefix){
            project.record_id = `${project.project_initial.toString().trim().toUpperCase()}${project.record_id}`;
        }
        let induction_phrase = (project.custom_field.induction_phrase_singlr || 'Induction');
        let subject = `${induction_phrase} Invitation: ${project.name}`;
        let extra = {
            project_ref,
            title: subject,
            project_name: project.name,
            induction_pin: project.induction_pin,
            project_reference_no: project.record_id,
            induction_phrase: induction_phrase,
            qrCodeImage: project.qrcode_image,
            show_company_induction_step,
            contractor_info,
        };

        let attachmentName = 'App-user-guide-2025.pdf';
        let attachmentPath = sails.config.custom.PUBLIC_URL+attachmentName;
        sails.log.info('attachmentPath: ', attachmentPath);

        let emailHtml = await sails.renderView('pages/mail/invite-to-induction', {
            ...extra,
            layout: false
        });
        for (let i = 0, len = recipients.length; i < len; i++) {
            let toUser = recipients[i].email;

            let invitation_log = {
                user_ref: null,
                email: toUser,
                name: null,
                mobile_no: null,
                invited_by: req.user.id,
                type: 'EMAIL',
                source_component: `project-induction:${project_ref}`,
                extra: extra,
            };
            sails.log.info('Sending mail to', toUser);
            try{
                let sentStatus = await sendRawEmail(subject, [toUser], emailHtml, attachmentPath, attachmentName);
                await sails.models.invitationlog.create(invitation_log);
            }catch(failure){
                sails.log.info('Failed to send mail', failure);
                return errorResponse(res, INTERNAL_SERVER_ERROR, failure);
            }
        }
        sails.log.info('Invitation emails has been sent');
        return successResponse(res, {sent: true});
    },

    getUserNotifications: async (req, res) => {
        let user_ref = req.user.id;

        //For pagination
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);

        sails.log.info('request to get user notifications for user id: ', user_ref);
        let notifications = await sails.models.usernotifications.find({where: {user_ref: user_ref}})
        .limit(pageSize)
        .skip(pageNumber * pageSize)
        .sort('createdAt DESC');

        return successResponse(res, {notifications: notifications});
    },

    getUserNotificationsCount: async (req, res) => {
        let count_all = req.param('all', 'unread') === 'all';

        sails.log.info(`get user notifications count user: ${req.user.id}, all: ${count_all}`);
        let notifications = await sails.models.usernotifications_reader.count({
            where: {
                user_ref: req.user.id,
            ...(count_all ? {} : {seen: false})
            }
        });

        return successResponse(res, {count: notifications});
    },

    updateNotificationStatus: async (req,res) => {
        let notificationIds = req.body.notificationIds || [];
        let seen = req.body.seen;
        sails.log.info('update request for user notifications id: ', notificationIds);
        let {validationError, payload} = updateNotifications(req);
        if(validationError) {
            return ResponseService.errorResponse(res, 'Validation Error: ', {validationError});
        }
        let notification = await sails.models.usernotifications.update({id: notificationIds}).set({
            seen: seen
        });
        sails.log.info('update request for user notifications successfully');
        return successResponse(res, {notifications: notification});
    },

    markAllNotificationsAsSeen: async (req,res) => {
        let userId = req.user.id;
        sails.log.info('mark as all notifications seen for user: ', userId);
        let notifications = await sails.models.usernotifications.update({user_ref: userId, seen: false}).set({
            seen: true
        });
        sails.log.info(`${notifications.length} notifications marked seen for user ${userId} successfully.`);
        return successResponse(res, {"message": `${notifications.length} notifications marked seen for user ${userId} successfully.`});
    },

    deleteNotificationById: async(req, res) => {
        let notificationId = +req.param('notificationId');
        sails.log.info('request to delete user notifications for id: ', notificationId);
        let deletedNotification = await sails.models.usernotifications.destroy({id: notificationId, user_ref: req.user.id});
        return successResponse(res, {deletedRecord: deletedNotification});
    }
};
